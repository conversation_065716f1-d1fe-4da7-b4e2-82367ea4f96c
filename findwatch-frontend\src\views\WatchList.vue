<template>
  <div class="watch-list">
    <nav class="navbar">
      <div class="nav-brand">
        <h2>寻表记</h2>
      </div>
      <div class="nav-menu">
        <router-link to="/dashboard" class="nav-item">首页</router-link>
        <router-link to="/watches" class="nav-item active">手表管理</router-link>
        <router-link to="/brands" class="nav-item">品牌管理</router-link>
        <router-link to="/profile" class="nav-item">个人信息</router-link>
        <button @click="logout" class="logout-btn">退出</button>
      </div>
    </nav>

    <div class="main-content">
      <div class="page-header">
        <h1>手表管理</h1>
        <button @click="showAddDialog = true" class="add-btn">添加手表</button>
      </div>

      <div class="search-bar">
        <input
          type="text"
          v-model="searchQuery"
          placeholder="搜索手表名称、品牌..."
          class="search-input"
        />
        <button @click="loadWatches" class="search-btn">搜索</button>
      </div>

      <div class="watch-grid" v-if="watches.length > 0">
        <div v-for="watch in filteredWatches" :key="watch.id" class="watch-card">
          <div class="watch-image">
            <img :src="watch.image || '/placeholder-watch.jpg'" :alt="watch.name" />
          </div>
          <div class="watch-info">
            <h3>{{ watch.name }}</h3>
            <p class="brand">{{ watch.brand }}</p>
            <p class="model">型号: {{ watch.model }}</p>
            <p class="price">¥{{ watch.price?.toLocaleString() || '暂无价格' }}</p>
            <div class="watch-actions">
              <button @click="editWatch(watch)" class="edit-btn">编辑</button>
              <button @click="deleteWatch(watch.id)" class="delete-btn">删除</button>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="empty-state">
        <p>暂无手表数据</p>
        <button @click="loadWatches" class="reload-btn">重新加载</button>
      </div>
    </div>

    <!-- 添加/编辑对话框 -->
    <div v-if="showAddDialog || showEditDialog" class="dialog-overlay" @click="closeDialogs">
      <div class="dialog" @click.stop>
        <h2>{{ showAddDialog ? '添加手表' : '编辑手表' }}</h2>
        <form @submit.prevent="saveWatch">
          <div class="form-group">
            <label>手表名称</label>
            <input type="text" v-model="currentWatch.name" required />
          </div>
          <div class="form-group">
            <label>品牌</label>
            <input type="text" v-model="currentWatch.brand" required />
          </div>
          <div class="form-group">
            <label>型号</label>
            <input type="text" v-model="currentWatch.model" />
          </div>
          <div class="form-group">
            <label>价格</label>
            <input type="number" v-model="currentWatch.price" />
          </div>
          <div class="dialog-actions">
            <button type="button" @click="closeDialogs" class="cancel-btn">取消</button>
            <button type="submit" class="save-btn">保存</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'WatchList',
  data() {
    return {
      watches: [],
      searchQuery: '',
      showAddDialog: false,
      showEditDialog: false,
      currentWatch: {
        id: null,
        name: '',
        brand: '',
        model: '',
        price: null
      }
    }
  },
  computed: {
    filteredWatches() {
      if (!this.searchQuery) return this.watches
      return this.watches.filter(watch =>
        watch.name.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
        watch.brand.toLowerCase().includes(this.searchQuery.toLowerCase())
      )
    }
  },
  async mounted() {
    await this.loadWatches()
  },
  methods: {
    async loadWatches() {
      try {
        // 修复接口路径：使用正确的后端接口
        const response = await this.$http.get('/v1/watches')
        this.watches = response.data || []
      } catch (error) {
        console.error('加载手表列表失败:', error)
        // 使用模拟数据
        this.watches = [
          {
            id: 1,
            name: 'Submariner',
            brand: 'Rolex',
            model: '116610LN',
            price: 65000
          },
          {
            id: 2,
            name: 'Speedmaster',
            brand: 'Omega',
            model: '311.30.42.30.01.005',
            price: 35000
          }
        ]
      }
    },
    editWatch(watch) {
      this.currentWatch = { ...watch }
      this.showEditDialog = true
    },
    async deleteWatch(id) {
      if (confirm('确定要删除这个手表吗？')) {
        try {
          // 修复接口路径
          await this.$http.delete(`/v1/watches/${id}`)
          await this.loadWatches()
        } catch (error) {
          console.error('删除手表失败:', error)
          alert('删除失败，请重试')
        }
      }
    },
    async saveWatch() {
      try {
        if (this.showAddDialog) {
          // 修复接口路径
          await this.$http.post('/v1/watches', this.currentWatch)
        } else {
          // 修复接口路径
          await this.$http.put(`/v1/watches/${this.currentWatch.id}`, this.currentWatch)
        }
        await this.loadWatches()
        this.closeDialogs()
      } catch (error) {
        console.error('保存手表失败:', error)
        alert('保存失败，请重试')
      }
    },
    closeDialogs() {
      this.showAddDialog = false
      this.showEditDialog = false
      this.currentWatch = {
        id: null,
        name: '',
        brand: '',
        model: '',
        price: null
      }
    },
    logout() {
      localStorage.removeItem('userToken')
      localStorage.removeItem('userInfo')
      this.$router.push('/login')
    }
  }
}
</script>

<style scoped>
.watch-list {
  min-height: 100vh;
}

.navbar {
  background: white;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
}

.nav-brand h2 {
  color: #333;
  margin: 0;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 20px;
}

.nav-item {
  text-decoration: none;
  color: #666;
  padding: 8px 16px;
  border-radius: 4px;
  transition: all 0.3s;
}

.nav-item:hover,
.nav-item.active {
  color: #667eea;
  background: #f0f2ff;
}

.logout-btn {
  background: #ff4757;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.main-content {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.add-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
}

.search-bar {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.search-input {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.search-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
}

.watch-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.watch-card {
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  overflow: hidden;
}

.watch-image {
  height: 200px;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.watch-image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: cover;
}

.watch-info {
  padding: 20px;
}

.watch-info h3 {
  margin: 0 0 10px 0;
  color: #333;
}

.brand {
  color: #667eea;
  font-weight: 500;
  margin: 5px 0;
}

.model, .price {
  margin: 5px 0;
  color: #666;
}

.watch-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.edit-btn {
  background: #2ed573;
  color: white;
  border: none;
  padding: 5px 15px;
  border-radius: 3px;
  cursor: pointer;
}

.delete-btn {
  background: #ff4757;
  color: white;
  border: none;
  padding: 5px 15px;
  border-radius: 3px;
  cursor: pointer;
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #666;
}

.reload-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  margin-top: 10px;
}

.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.dialog {
  background: white;
  padding: 30px;
  border-radius: 10px;
  width: 90%;
  max-width: 500px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  color: #333;
}

.form-group input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.dialog-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 20px;
}

.cancel-btn {
  background: #ccc;
  color: #333;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
}

.save-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
}
</style>
