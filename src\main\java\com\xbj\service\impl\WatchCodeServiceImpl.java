package com.xbj.service.impl;

import com.xbj.dao.WatchCodeMapper;
import com.xbj.entity.WatchCode;
import com.xbj.entity.WatchCodeExample;
import com.xbj.service.WatchCodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class WatchCodeServiceImpl implements WatchCodeService {

    @Autowired
    private WatchCodeMapper watchCodeMapper;

    @Override
    public long countByExample(WatchCodeExample example) {
        return watchCodeMapper.countByExample(example);
    }

    @Override
    public int deleteByExample(WatchCodeExample example) {
        return watchCodeMapper.deleteByExample(example);
    }

    @Override
    public int deleteByPrimaryKey(String id) {
        return watchCodeMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(WatchCode record) {
        return watchCodeMapper.insert(record);
    }

    @Override
    public int insertSelective(WatchCode record) {
        return watchCodeMapper.insertSelective(record);
    }

    @Override
    public List<WatchCode> selectByExample(WatchCodeExample example) {
        return watchCodeMapper.selectByExample(example);
    }

    @Override
    public List<WatchCode> selectAllType() {
        return watchCodeMapper.selectAllType();
    }

    @Override
    public WatchCode selectByPrimaryKey(String id) {
        return watchCodeMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByExampleSelective(WatchCode record, WatchCodeExample example) {
        return watchCodeMapper.updateByExampleSelective(record,example);
    }

    @Override
    public int updateByExample(WatchCode record, WatchCodeExample example) {
        return watchCodeMapper.updateByExample(record, example);
    }

    @Override
    public int updateByPrimaryKeySelective(WatchCode record) {
        return watchCodeMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(WatchCode record) {
        return watchCodeMapper.updateByPrimaryKey(record);
    }
}
