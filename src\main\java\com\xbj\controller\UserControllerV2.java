package com.xbj.controller;

import com.xbj.common.ApiResponse;
import com.xbj.common.ResultCode;
import com.xbj.entity.User;
import com.xbj.entity.UserExample;
import com.xbj.exception.BusinessException;
import com.xbj.interceptor.TraceInterceptor;
import com.xbj.service.UserService;
import com.xbj.util.JWTUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 用户管理控制器 - 微服务化重构版本
 */
@Tag(name = "用户管理", description = "用户信息管理相关接口")
@RestController
@RequestMapping("/api/v1/users")
public class UserControllerV2 {

    private static final Logger logger = LoggerFactory.getLogger(UserControllerV2.class);

    @Autowired
    private UserService userService;

    /**
     * 获取当前用户信息
     */
    @Operation(summary = "获取用户信息", description = "获取当前登录用户的详细信息")
    @SecurityRequirement(name = "Bearer Authentication")
    @GetMapping("/profile")
    public ApiResponse<User> getUserProfile(HttpServletRequest request) {
        try {
            String traceId = TraceInterceptor.getCurrentTraceId();
            logger.info("获取用户信息请求 - TraceId: {}", traceId);

            // 验证JWT令牌
            String token = request.getHeader("authorization");
            if (!StringUtils.hasText(token)) {
                throw new BusinessException(ResultCode.UNAUTHORIZED, "缺少访问令牌");
            }

            Map<String, Object> tokenResult = JWTUtils.verifyTokenGetResult(token, "lookforwatch");
            boolean isValid = Boolean.parseBoolean(tokenResult.get("isSuccess").toString());
            
            if (!isValid) {
                throw new BusinessException(ResultCode.TOKEN_INVALID);
            }

            String openid = tokenResult.get("openid").toString();
            
            // 查询用户信息
            UserExample userExample = new UserExample();
            UserExample.Criteria criteria = userExample.or();
            criteria.andOpenidEqualTo(openid);
            
            List<User> users = userService.selectByExample(userExample);
            
            if (users.isEmpty()) {
                throw new BusinessException(ResultCode.USER_NOT_FOUND);
            }

            User user = users.get(0);
            
            ApiResponse<User> result = ApiResponse.success("获取成功", user);
            result.setTraceId(traceId);
            
            logger.info("获取用户信息成功 - OpenId: {}, UserId: {}, TraceId: {}", 
                       openid, user.getId(), traceId);
            return result;
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            logger.error("获取用户信息失败", e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 更新用户信息
     */
    @Operation(summary = "更新用户信息", description = "更新当前登录用户的信息")
    @SecurityRequirement(name = "Bearer Authentication")
    @PutMapping("/profile")
    public ApiResponse<User> updateUserProfile(@Valid @RequestBody User userUpdate, 
                                             HttpServletRequest request) {
        try {
            String traceId = TraceInterceptor.getCurrentTraceId();
            logger.info("更新用户信息请求 - TraceId: {}, UserUpdate: {}", traceId, userUpdate);

            // 验证JWT令牌
            String token = request.getHeader("authorization");
            if (!StringUtils.hasText(token)) {
                throw new BusinessException(ResultCode.UNAUTHORIZED, "缺少访问令牌");
            }

            Map<String, Object> tokenResult = JWTUtils.verifyTokenGetResult(token, "lookforwatch");
            boolean isValid = Boolean.parseBoolean(tokenResult.get("isSuccess").toString());
            
            if (!isValid) {
                throw new BusinessException(ResultCode.TOKEN_INVALID);
            }

            String openid = tokenResult.get("openid").toString();
            
            // 查询现有用户信息
            UserExample userExample = new UserExample();
            UserExample.Criteria criteria = userExample.or();
            criteria.andOpenidEqualTo(openid);
            
            List<User> users = userService.selectByExample(userExample);
            
            if (users.isEmpty()) {
                throw new BusinessException(ResultCode.USER_NOT_FOUND);
            }

            User existingUser = users.get(0);
            
            // 更新允许修改的字段
            if (StringUtils.hasText(userUpdate.getNickName())) {
                existingUser.setNickName(userUpdate.getNickName());
            }
            if (StringUtils.hasText(userUpdate.getAvatarUrl())) {
                existingUser.setAvatarUrl(userUpdate.getAvatarUrl());
            }
            if (StringUtils.hasText(userUpdate.getGender())) {
                existingUser.setGender("1".equals(userUpdate.getGender()) ? "男" : "女");
            }
            if (StringUtils.hasText(userUpdate.getPhone())) {
                existingUser.setPhone(userUpdate.getPhone());
            }
            
            // 执行更新
            int updateResult = userService.updateByPrimaryKeySelective(existingUser);
            
            if (updateResult <= 0) {
                throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "更新失败");
            }

            ApiResponse<User> result = ApiResponse.success("更新成功", existingUser);
            result.setTraceId(traceId);
            
            logger.info("更新用户信息成功 - OpenId: {}, UserId: {}, TraceId: {}", 
                       openid, existingUser.getId(), traceId);
            return result;
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            logger.error("更新用户信息失败", e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "更新用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 删除用户账户
     */
    @Operation(summary = "删除用户账户", description = "删除当前登录用户的账户")
    @SecurityRequirement(name = "Bearer Authentication")
    @DeleteMapping("/profile")
    public ApiResponse<Boolean> deleteUserAccount(HttpServletRequest request) {
        try {
            String traceId = TraceInterceptor.getCurrentTraceId();
            logger.info("删除用户账户请求 - TraceId: {}", traceId);

            // 验证JWT令牌
            String token = request.getHeader("authorization");
            if (!StringUtils.hasText(token)) {
                throw new BusinessException(ResultCode.UNAUTHORIZED, "缺少访问令牌");
            }

            Map<String, Object> tokenResult = JWTUtils.verifyTokenGetResult(token, "lookforwatch");
            boolean isValid = Boolean.parseBoolean(tokenResult.get("isSuccess").toString());
            
            if (!isValid) {
                throw new BusinessException(ResultCode.TOKEN_INVALID);
            }

            String openid = tokenResult.get("openid").toString();
            
            // 查询用户信息
            UserExample userExample = new UserExample();
            UserExample.Criteria criteria = userExample.or();
            criteria.andOpenidEqualTo(openid);
            
            List<User> users = userService.selectByExample(userExample);
            
            if (users.isEmpty()) {
                throw new BusinessException(ResultCode.USER_NOT_FOUND);
            }

            User user = users.get(0);
            
            // 执行删除
            int deleteResult = userService.deleteByPrimaryKey(user.getId());
            
            if (deleteResult <= 0) {
                throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "删除失败");
            }

            ApiResponse<Boolean> result = ApiResponse.success("删除成功", true);
            result.setTraceId(traceId);
            
            logger.info("删除用户账户成功 - OpenId: {}, UserId: {}, TraceId: {}", 
                       openid, user.getId(), traceId);
            return result;
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            logger.error("删除用户账户失败", e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "删除用户账户失败: " + e.getMessage());
        }
    }

    /**
     * 检查用户是否存在
     */
    @Operation(summary = "检查用户存在性", description = "检查指定OpenID的用户是否存在")
    @SecurityRequirement(name = "Bearer Authentication")
    @GetMapping("/exists")
    public ApiResponse<Boolean> checkUserExists(HttpServletRequest request) {
        try {
            String traceId = TraceInterceptor.getCurrentTraceId();
            logger.info("检查用户存在性请求 - TraceId: {}", traceId);

            // 验证JWT令牌
            String token = request.getHeader("authorization");
            if (!StringUtils.hasText(token)) {
                throw new BusinessException(ResultCode.UNAUTHORIZED, "缺少访问令牌");
            }

            Map<String, Object> tokenResult = JWTUtils.verifyTokenGetResult(token, "lookforwatch");
            boolean isValid = Boolean.parseBoolean(tokenResult.get("isSuccess").toString());
            
            if (!isValid) {
                throw new BusinessException(ResultCode.TOKEN_INVALID);
            }

            String openid = tokenResult.get("openid").toString();
            
            // 查询用户信息
            UserExample userExample = new UserExample();
            UserExample.Criteria criteria = userExample.or();
            criteria.andOpenidEqualTo(openid);
            
            List<User> users = userService.selectByExample(userExample);
            boolean exists = !users.isEmpty();

            ApiResponse<Boolean> result = ApiResponse.success("检查完成", exists);
            result.setTraceId(traceId);
            
            logger.info("检查用户存在性完成 - OpenId: {}, Exists: {}, TraceId: {}", 
                       openid, exists, traceId);
            return result;
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            logger.error("检查用户存在性失败", e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "检查用户存在性失败: " + e.getMessage());
        }
    }
}
