package com.xbj.controller;

import com.xbj.entity.User;
import com.xbj.entity.UserExample;
import com.xbj.service.UserService;
import com.xbj.util.JWTUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@RestController
@RequestMapping("/user")
public class UserController {

    @Autowired
    private UserService userService;

    @RequestMapping("/updateUser")
    public boolean updateUser(@RequestBody User user, HttpServletRequest request){
        String token = request.getHeader("authorization");
        Map res = JWTUtils.verifyTokenGetResult(token, "lookforwatch");
        boolean isSuccess = Boolean.parseBoolean(res.get("isSuccess").toString());
        if (isSuccess){
            /* String phoneNumber = user.getPhone();
            String email = user.getEmail();
            String wx = user.getWxNumber();
            String gender = user.getGender();
            String nickName = user.getNickName();*/
            UserExample userExample = new UserExample();
            UserExample.Criteria or = userExample.or();
            or.andOpenidEqualTo(res.get("openid").toString());
            int i = userService.updateByExampleSelective(user, userExample);
            return i>0;
        }
        return false;
    }

}
