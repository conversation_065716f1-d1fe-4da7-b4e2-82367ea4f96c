<template>
  <div class="user-profile">
    <nav class="navbar">
      <div class="nav-brand">
        <h2>寻表记</h2>
      </div>
      <div class="nav-menu">
        <router-link to="/dashboard" class="nav-item">首页</router-link>
        <router-link to="/watches" class="nav-item">手表管理</router-link>
        <router-link to="/brands" class="nav-item">品牌管理</router-link>
        <router-link to="/profile" class="nav-item active">个人信息</router-link>
        <button @click="logout" class="logout-btn">退出</button>
      </div>
    </nav>

    <div class="main-content">
      <div class="profile-container">
        <div class="profile-header">
          <div class="avatar">
            <img :src="userInfo.avatar || '/default-avatar.png'" :alt="userInfo.nickname" />
          </div>
          <div class="user-basic">
            <h1>{{ userInfo.nickname || userInfo.username }}</h1>
            <p class="user-id">用户ID: {{ userInfo.id }}</p>
          </div>
        </div>

        <div class="profile-content">
          <div class="info-section">
            <h2>基本信息</h2>
            <form @submit.prevent="updateProfile" class="profile-form">
              <div class="form-row">
                <div class="form-group">
                  <label>用户名</label>
                  <input type="text" v-model="userInfo.username" readonly />
                </div>
                <div class="form-group">
                  <label>昵称</label>
                  <input type="text" v-model="userInfo.nickname" />
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-group">
                  <label>邮箱</label>
                  <input type="email" v-model="userInfo.email" />
                </div>
                <div class="form-group">
                  <label>手机号</label>
                  <input type="tel" v-model="userInfo.phone" />
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-group">
                  <label>省份</label>
                  <input type="text" v-model="userInfo.province" />
                </div>
                <div class="form-group">
                  <label>国家</label>
                  <input type="text" v-model="userInfo.country" />
                </div>
              </div>

              <div class="form-actions">
                <button type="submit" class="save-btn" :disabled="saving">
                  {{ saving ? '保存中...' : '保存修改' }}
                </button>
              </div>
            </form>
          </div>

          <div class="password-section">
            <h2>修改密码</h2>
            <form @submit.prevent="changePassword" class="password-form">
              <div class="form-group">
                <label>当前密码</label>
                <input type="password" v-model="passwordForm.currentPassword" required />
              </div>
              <div class="form-group">
                <label>新密码</label>
                <input type="password" v-model="passwordForm.newPassword" required />
              </div>
              <div class="form-group">
                <label>确认新密码</label>
                <input type="password" v-model="passwordForm.confirmPassword" required />
              </div>
              <div class="form-actions">
                <button type="submit" class="change-password-btn" :disabled="changingPassword">
                  {{ changingPassword ? '修改中...' : '修改密码' }}
                </button>
              </div>
            </form>
          </div>

          <div class="stats-section">
            <h2>我的统计</h2>
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-number">{{ userStats.watchCount }}</div>
                <div class="stat-label">收藏手表</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">{{ userStats.favoriteCount }}</div>
                <div class="stat-label">关注品牌</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">{{ userStats.loginDays }}</div>
                <div class="stat-label">登录天数</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'UserProfile',
  data() {
    return {
      userInfo: {
        id: null,
        username: '',
        nickname: '',
        email: '',
        phone: '',
        province: '',
        country: '',
        avatar: ''
      },
      passwordForm: {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      userStats: {
        watchCount: 0,
        favoriteCount: 0,
        loginDays: 0
      },
      saving: false,
      changingPassword: false
    }
  },
  async mounted() {
    await this.loadUserInfo()
    await this.loadUserStats()
  },
  methods: {
    async loadUserInfo() {
      try {
        // 从localStorage获取用户信息
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
        this.userInfo = {
          id: userInfo.id || 1,
          username: userInfo.username || 'admin',
          nickname: userInfo.nickname || '管理员',
          email: '<EMAIL>',
          phone: '13800138000',
          province: '北京市',
          country: '中国',
          avatar: ''
        }
      } catch (error) {
        console.error('加载用户信息失败:', error)
      }
    },
    async loadUserStats() {
      try {
        // 模拟用户统计数据
        this.userStats = {
          watchCount: 25,
          favoriteCount: 8,
          loginDays: 156
        }
      } catch (error) {
        console.error('加载用户统计失败:', error)
      }
    },
    async updateProfile() {
      this.saving = true
      try {
        // 这里应该调用API更新用户信息
        await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用
        
        // 更新localStorage中的用户信息
        localStorage.setItem('userInfo', JSON.stringify(this.userInfo))
        
        alert('个人信息更新成功')
      } catch (error) {
        console.error('更新个人信息失败:', error)
        alert('更新失败，请重试')
      } finally {
        this.saving = false
      }
    },
    async changePassword() {
      if (this.passwordForm.newPassword !== this.passwordForm.confirmPassword) {
        alert('新密码和确认密码不一致')
        return
      }
      
      if (this.passwordForm.newPassword.length < 6) {
        alert('新密码长度不能少于6位')
        return
      }
      
      this.changingPassword = true
      try {
        // 这里应该调用API修改密码
        await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用
        
        alert('密码修改成功')
        this.passwordForm = {
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        }
      } catch (error) {
        console.error('修改密码失败:', error)
        alert('修改密码失败，请重试')
      } finally {
        this.changingPassword = false
      }
    },
    logout() {
      localStorage.removeItem('userToken')
      localStorage.removeItem('userInfo')
      this.$router.push('/login')
    }
  }
}
</script>

<style scoped>
.user-profile {
  min-height: 100vh;
}

.navbar {
  background: white;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
}

.nav-brand h2 {
  color: #333;
  margin: 0;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 20px;
}

.nav-item {
  text-decoration: none;
  color: #666;
  padding: 8px 16px;
  border-radius: 4px;
  transition: all 0.3s;
}

.nav-item:hover,
.nav-item.active {
  color: #667eea;
  background: #f0f2ff;
}

.logout-btn {
  background: #ff4757;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.main-content {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.profile-container {
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  overflow: hidden;
}

.profile-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40px;
  display: flex;
  align-items: center;
  gap: 30px;
}

.avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  overflow: hidden;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-basic h1 {
  margin: 0 0 10px 0;
  font-size: 28px;
}

.user-id {
  margin: 0;
  opacity: 0.8;
}

.profile-content {
  padding: 40px;
}

.info-section,
.password-section,
.stats-section {
  margin-bottom: 40px;
}

.info-section h2,
.password-section h2,
.stats-section h2 {
  color: #333;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #f0f2ff;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  color: #333;
  font-weight: 500;
}

.form-group input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.form-group input:focus {
  outline: none;
  border-color: #667eea;
}

.form-group input[readonly] {
  background: #f5f5f5;
  color: #666;
}

.form-actions {
  margin-top: 20px;
}

.save-btn,
.change-password-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s;
}

.save-btn:hover,
.change-password-btn:hover {
  background: #5a6fd8;
}

.save-btn:disabled,
.change-password-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 5px;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .profile-header {
    flex-direction: column;
    text-align: center;
  }
}
</style>
