package com.xbj.service;

import com.xbj.entity.User;
import com.xbj.entity.UserExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UserService {
    long countByExample(UserExample example);

    int deleteByExample(UserExample example);

    int deleteByPrimaryKey(String id);

    int insert(User record);

    int insertSelective(User record);

    List<User> selectByExample(UserExample example);

    User selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") User record, @Param("example") UserExample example);

    int updateByExample(@Param("record") User record, @Param("example") UserExample example);

    int updateByPrimaryKeySelective(User record);

    int updateByPrimaryKey(User record);

    User getUserByUnionId(String openId);

    int updateByOpenId(String phoneNum,String countryCode,String openId);
}
