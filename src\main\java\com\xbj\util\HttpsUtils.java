package com.xbj.util;

import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.ResourceBundle;

/**
 * <AUTHOR>
 * @create 2020-01-13 15:36
 */
public class HttpsUtils {
    private  static final Logger logger = LoggerFactory.getLogger(HttpsUtils.class);
    /**
     * 获取微信小程序的session_key和openid
     *
     * <AUTHOR>
     * @param code 微信前端login()方法返回的code
     * @return jsonObject
     *
     * */
    /**
     * @deprecated 使用WeixinService.getSessionKeyAndOpenId(String code)替代
     */
    @Deprecated
    public static JSONObject getSessionKeyAndOpenId(String code){
        //微信登录的code值
        //读取属性文件
        ResourceBundle resourceBundle = ResourceBundle.getBundle("weixin");
        //服务器端调用接口的url
        String requestUrl = resourceBundle.getString("url");
        //封装需要的参数信息
        Map<String,String> requestUrlParam = new HashMap<>(16);
        //开发者设置中的appId
        requestUrlParam.put("appid",resourceBundle.getString("appId"));
        //开发者设置中的appSecret
        requestUrlParam.put("secret",resourceBundle.getString("appSecret"));
        //小程序调用wx.login返回的code
        requestUrlParam.put("js_code", code);
        //默认参数
        requestUrlParam.put("grant_type", "authorization_code");
        return JSONObject.parseObject(sendPost(requestUrl, requestUrlParam));
    }

    /**
     * 向指定 URL 发送POST方法的请求
     *
     * @param url 发送请求的 URL
     * @return 所代表远程资源的响应结果
     */
    public static String sendPost(String url, Map<String, ?> paramMap) {
        PrintWriter out = null;
        BufferedReader in = null;
        StringBuilder result = new StringBuilder();

        StringBuilder param = new StringBuilder();

        for (String key : paramMap.keySet()) {
            param.append(key).append("=").append(paramMap.get(key)).append("&");
        }
        try {
            URL realUrl = new URL(url);
            // 打开和URL之间的连接
            URLConnection conn = realUrl.openConnection();
            // 设置通用的请求属性
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("Accept-Charset", "utf-8");
            conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            // 获取URLConnection对象对应的输出流
            out = new PrintWriter(conn.getOutputStream());
            // 发送请求参数
            out.print(param);
            // flush输出流的缓冲
            out.flush();
            // 定义BufferedReader输入流来读取URL的响应
            in = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
        }
        //使用finally块来关闭输出流、输入流
        finally{
            try{
                if(out!=null){
                    out.close();
                }
                if(in!=null){
                    in.close();
                }
            }
            catch(IOException ex){
                ex.printStackTrace();
            }
        }
        return result.toString();
    }

    public static String getRequestRealIp(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0];
        }

        if (checkIp(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (checkIp(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (checkIp(ip)) {
            ip = request.getHeader("X-Real-IP");
        }

        if (checkIp(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    private static boolean checkIp(String ip) {
        return ip == null || ip.length() == 0 || "unkown".equalsIgnoreCase(ip);
    }
//    public static void main(String[] args) {
//        ResourceBundle resourceBundle = ResourceBundle.getBundle("weixin");
//        String requestUrl = resourceBundle.getString("url");
//        System.out.println(requestUrl);
//    }
}
