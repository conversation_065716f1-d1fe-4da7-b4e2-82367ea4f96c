<template>
  <div class="test-container">
    <h2>前后端连接测试</h2>
    
    <div class="test-section">
      <h3>1. 测试后端连接</h3>
      <button @click="testBackendConnection" :disabled="loading">
        {{ loading ? '测试中...' : '测试连接' }}
      </button>
      <div v-if="connectionResult" class="result">
        <p><strong>状态:</strong> {{ connectionResult.success ? '成功' : '失败' }}</p>
        <p><strong>消息:</strong> {{ connectionResult.message }}</p>
        <p v-if="connectionResult.data"><strong>数据:</strong> {{ connectionResult.data }}</p>
      </div>
    </div>

    <div class="test-section">
      <h3>2. 测试登录接口</h3>
      <div class="login-form">
        <input v-model="testUsername" placeholder="用户名 (admin)" />
        <input v-model="testPassword" type="password" placeholder="密码 (123456)" />
        <button @click="testLogin" :disabled="loginLoading">
          {{ loginLoading ? '登录中...' : '测试登录' }}
        </button>
      </div>
      <div v-if="loginResult" class="result">
        <p><strong>状态:</strong> {{ loginResult.success ? '成功' : '失败' }}</p>
        <p><strong>消息:</strong> {{ loginResult.message }}</p>
        <div v-if="loginResult.data">
          <p><strong>Token:</strong> {{ loginResult.data.token }}</p>
          <p><strong>用户名:</strong> {{ loginResult.data.username }}</p>
          <p><strong>昵称:</strong> {{ loginResult.data.nickname }}</p>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>3. 网络信息</h3>
      <p><strong>前端地址:</strong> {{ frontendUrl }}</p>
      <p><strong>后端地址:</strong> {{ backendUrl }}</p>
      <p><strong>API基础URL:</strong> {{ apiBaseUrl }}</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Test',
  data() {
    return {
      loading: false,
      loginLoading: false,
      connectionResult: null,
      loginResult: null,
      testUsername: 'admin',
      testPassword: '123456',
      frontendUrl: window.location.origin,
      backendUrl: 'http://localhost:8083',
      apiBaseUrl: this.$http.defaults.baseURL
    }
  },
  methods: {
    async testBackendConnection() {
      this.loading = true;
      this.connectionResult = null;
      
      try {
        const response = await this.$http.get('/test/hello');
        this.connectionResult = {
          success: true,
          message: response.data.message || '连接成功',
          data: response.data.data
        };
      } catch (error) {
        this.connectionResult = {
          success: false,
          message: error.message || '连接失败',
          data: error.response?.data
        };
      } finally {
        this.loading = false;
      }
    },

    async testLogin() {
      this.loginLoading = true;
      this.loginResult = null;
      
      try {
        const response = await this.$http.post('/test/login', {
          username: this.testUsername,
          password: this.testPassword
        });
        
        this.loginResult = {
          success: true,
          message: response.data.message || '登录成功',
          data: response.data.data
        };
      } catch (error) {
        this.loginResult = {
          success: false,
          message: error.response?.data?.message || error.message || '登录失败',
          data: error.response?.data
        };
      } finally {
        this.loginLoading = false;
      }
    }
  }
}
</script>

<style scoped>
.test-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.login-form {
  margin: 10px 0;
}

.login-form input {
  margin-right: 10px;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

button {
  padding: 10px 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

button:hover:not(:disabled) {
  background-color: #0056b3;
}

.result {
  margin-top: 15px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 4px solid #007bff;
}

h2, h3 {
  color: #333;
}
</style>
