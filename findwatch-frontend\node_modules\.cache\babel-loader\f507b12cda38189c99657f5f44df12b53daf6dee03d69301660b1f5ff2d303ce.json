{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { createApp } from 'vue';\nimport App from './App.vue';\nimport router from './router';\nimport axios from 'axios';\n\n// 配置axios\naxios.defaults.baseURL = 'http://localhost:8083/api';\naxios.defaults.timeout = 10000;\n\n// 请求拦截器\naxios.interceptors.request.use(config => {\n  // 自动添加token到请求头\n  const token = localStorage.getItem('userToken');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 响应拦截器\naxios.interceptors.response.use(response => {\n  return response.data;\n}, error => {\n  console.error('API请求错误:', error);\n\n  // 处理401未授权错误（token过期或无效）\n  if (error.response && error.response.status === 401) {\n    localStorage.removeItem('userToken');\n    localStorage.removeItem('userInfo');\n    router.push('/login');\n    return Promise.reject(new Error('登录已过期，请重新登录'));\n  }\n  return Promise.reject(error);\n});\nconst app = createApp(App);\napp.config.globalProperties.$http = axios;\napp.use(router);\napp.mount('#app');", "map": {"version": 3, "names": ["createApp", "App", "router", "axios", "defaults", "baseURL", "timeout", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "headers", "Authorization", "error", "Promise", "reject", "response", "data", "console", "status", "removeItem", "push", "Error", "app", "globalProperties", "$http", "mount"], "sources": ["D:/devSpace/ideaWorkspace/findwatch/findwatch-frontend/src/main.js"], "sourcesContent": ["import { createApp } from 'vue'\nimport App from './App.vue'\nimport router from './router'\nimport axios from 'axios'\n\n// 配置axios\naxios.defaults.baseURL = 'http://localhost:8083/api'\naxios.defaults.timeout = 10000\n\n// 请求拦截器\naxios.interceptors.request.use(\n  config => {\n    // 自动添加token到请求头\n    const token = localStorage.getItem('userToken')\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`\n    }\n    return config\n  },\n  error => {\n    return Promise.reject(error)\n  }\n)\n\n// 响应拦截器\naxios.interceptors.response.use(\n  response => {\n    return response.data\n  },\n  error => {\n    console.error('API请求错误:', error)\n\n    // 处理401未授权错误（token过期或无效）\n    if (error.response && error.response.status === 401) {\n      localStorage.removeItem('userToken')\n      localStorage.removeItem('userInfo')\n      router.push('/login')\n      return Promise.reject(new Error('登录已过期，请重新登录'))\n    }\n\n    return Promise.reject(error)\n  }\n)\n\nconst app = createApp(App)\napp.config.globalProperties.$http = axios\napp.use(router)\napp.mount('#app')\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,KAAK;AAC/B,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,OAAO;;AAEzB;AACAA,KAAK,CAACC,QAAQ,CAACC,OAAO,GAAG,2BAA2B;AACpDF,KAAK,CAACC,QAAQ,CAACE,OAAO,GAAG,KAAK;;AAE9B;AACAH,KAAK,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CAC5BC,MAAM,IAAI;EACR;EACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;EAC/C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACI,OAAO,CAACC,aAAa,GAAG,UAAUJ,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACDM,KAAK,IAAI;EACP,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAb,KAAK,CAACI,YAAY,CAACY,QAAQ,CAACV,GAAG,CAC7BU,QAAQ,IAAI;EACV,OAAOA,QAAQ,CAACC,IAAI;AACtB,CAAC,EACDJ,KAAK,IAAI;EACPK,OAAO,CAACL,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;;EAEhC;EACA,IAAIA,KAAK,CAACG,QAAQ,IAAIH,KAAK,CAACG,QAAQ,CAACG,MAAM,KAAK,GAAG,EAAE;IACnDV,YAAY,CAACW,UAAU,CAAC,WAAW,CAAC;IACpCX,YAAY,CAACW,UAAU,CAAC,UAAU,CAAC;IACnCrB,MAAM,CAACsB,IAAI,CAAC,QAAQ,CAAC;IACrB,OAAOP,OAAO,CAACC,MAAM,CAAC,IAAIO,KAAK,CAAC,aAAa,CAAC,CAAC;EACjD;EAEA,OAAOR,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,MAAMU,GAAG,GAAG1B,SAAS,CAACC,GAAG,CAAC;AAC1ByB,GAAG,CAAChB,MAAM,CAACiB,gBAAgB,CAACC,KAAK,GAAGzB,KAAK;AACzCuB,GAAG,CAACjB,GAAG,CAACP,MAAM,CAAC;AACfwB,GAAG,CAACG,KAAK,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}