package com.xbj.test;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import org.springframework.util.StringUtils;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/7 18:55
 **/
public class GenerateSQL {



    public static void main(String[] args) {
        String fileName = "D://demo.xlsx";
        StringBuffer content = new StringBuffer("");
        HashSet<String> sqlSet = new HashSet<>();
        //sqlSet.add();
        //sqlSet.contains()
        // 这里 需要指定读用哪个class去读，然后读取第一个sheet 文件流会自动关闭
        // 这里默认每次会读取100条数据 然后返回过来 直接调用使用数据就行
        // 具体需要返回多少行可以在`PageReadListener`的构造函数设置
        EasyExcel.read(fileName, LevelData.class, new PageReadListener<LevelData>(dataList -> {
            for (LevelData demoData : dataList) {

                String sql1 ="INSERT INTO t_dsmc_classification\n" +
                        "(classification_name, parent_id, `depth`, leaf, create_time, update_time, status)\n" +
                        "VALUES ('"+demoData.getFirstKind()+"', 0, 1, 0, unix_timestamp(now()), unix_timestamp(now()), 1);";


                if (!sqlSet.contains(demoData.getFirstKind())){
                    sqlSet.add(demoData.getFirstKind());
                    content.append(sql1).append("\r\n\n");
                }
                String sql2 = "INSERT INTO t_dsmc_classification\n" +
                        "(classification_name, parent_id, `depth`, leaf, create_time, update_time, status)\n" +
                        "select '"+demoData.getSecondKind()+"',\n" +
                        "       ifnull(t1.classification_id, 0),\n" +
                        "       if(t1.`depth` is null, 1, t1.`depth` + 1),\n" +
                        "       if(t1.classification_id is null, 0, 1),\n" +
                        "       unix_timestamp(now()),\n" +
                        "       unix_timestamp(now()),\n" +
                        "       1\n" +
                        "from (select classification_id, `depth` from t_dsmc_classification where classification_name = '"+demoData.getFirstKind()+"') t1;";
                if (!sqlSet.contains(demoData.getFirstKind()+demoData.getSecondKind())){
                    sqlSet.add(demoData.getFirstKind()+demoData.getSecondKind());
                    content.append(sql2).append("\r\n\n");
                }
                String sql3 = "INSERT INTO t_dsmc_classification\n" +
                        "(classification_name, parent_id, `depth`, leaf, create_time, update_time, status)\n" +
                        "select '"+demoData.getThirdKind()+"',\n" +
                        "       ifnull(t1.classification_id, 0),\n" +
                        "       if(t1.`depth` is null, 1, t1.`depth` + 1),\n" +
                        "       if(t1.classification_id is null, 0, 1),\n" +
                        "       unix_timestamp(now()),\n" +
                        "       unix_timestamp(now()),\n" +
                        "       1\n" +
                        "from (select classification_id, `depth`\n" +
                        "      from t_dsmc_classification\n" +
                        "      where classification_name = '"+demoData.getSecondKind()+"'\n" +
                        "        and parent_id =\n" +
                        "            (select classification_id from t_dsmc_classification where classification_name = '"+demoData.getFirstKind()+"')) t1;";
                if (!sqlSet.contains(demoData.getFirstKind()+demoData.getSecondKind()+demoData.getThirdKind())){
                    sqlSet.add(demoData.getFirstKind()+demoData.getSecondKind()+demoData.getThirdKind());
                    content.append(sql3).append("\r\n\n");
                }
                String sql4 = "INSERT INTO t_dsmc_classification\n" +
                        "(classification_name, parent_id, `depth`, leaf, create_time, update_time, status)\n" +
                        "select '"+demoData.getFourthKind()+"',\n" +
                        "       ifnull(t1.classification_id, 0),\n" +
                        "       if(t1.`depth` is null, 1, t1.`depth` + 1),\n" +
                        "       if(t1.classification_id is null, 0, 1),\n" +
                        "       unix_timestamp(now()),\n" +
                        "       unix_timestamp(now()),\n" +
                        "       1\n" +
                        "from (select classification_id, `depth`\n" +
                        "      from t_dsmc_classification\n" +
                        "      where classification_name = '"+demoData.getThirdKind()+"'\n" +
                        "        and parent_id =\n" +
                        "            (select classification_id\n" +
                        "             from t_dsmc_classification\n" +
                        "             where classification_name = '"+demoData.getSecondKind()+"'\n" +
                        "               and parent_id =\n" +
                        "                   (select classification_id from t_dsmc_classification where classification_name = '"+demoData.getFirstKind()+"'))) t1;";
                if (!sqlSet.contains(demoData.getFirstKind()+demoData.getSecondKind()+demoData.getThirdKind()+demoData.getFourthKind())){
                    sqlSet.add(demoData.getFirstKind()+demoData.getSecondKind()+demoData.getThirdKind()+demoData.getFourthKind());
                    content.append(sql4).append("\r\n\n");
                }
                if (!StringUtils.isEmpty(demoData.getFifthKind())){
                    String sql5="INSERT INTO t_dsmc_classification\n" +
                            "(classification_name, parent_id, `depth`, leaf, create_time, update_time, status)\n" +
                            "select '"+demoData.getFifthKind()+"',\n" +
                            "       ifnull(t1.classification_id, 0),\n" +
                            "       if(t1.`depth` is null, 1, t1.`depth` + 1),\n" +
                            "       if(t1.classification_id is null, 0, 1),\n" +
                            "       unix_timestamp(now()),\n" +
                            "       unix_timestamp(now()),\n" +
                            "       1\n" +
                            "from (select classification_id, `depth`\n" +
                            "      from t_dsmc_classification\n" +
                            "      where classification_name = '"+demoData.getFourthKind()+"'\n" +
                            "        and parent_id =\n" +
                            "            (select classification_id\n" +
                            "             from t_dsmc_classification\n" +
                            "             where classification_name = '"+demoData.getThirdKind()+"'\n" +
                            "               and parent_id =\n" +
                            "                   (select classification_id\n" +
                            "                    from t_dsmc_classification\n" +
                            "                    where classification_name = '"+demoData.getSecondKind()+"'\n" +
                            "                      and parent_id = (select classification_id\n" +
                            "                                       from t_dsmc_classification\n" +
                            "                                       where classification_name = '"+demoData.getFirstKind()+"')))) t1;";
                    if (!sqlSet.contains(demoData.getFirstKind()+demoData.getSecondKind()+demoData.getThirdKind()+demoData.getFourthKind()+demoData.getFifthKind())){
                        sqlSet.add(demoData.getFirstKind()+demoData.getSecondKind()+demoData.getThirdKind()+demoData.getFourthKind()+demoData.getFifthKind());
                        content.append(sql5).append("\r\n\n");
                    }
                }
                System.out.println(sqlSet);
                List<String> items = splitData(demoData.getData());

                // 遍历拆分后的项
                for (String item : items) {
                    System.out.println(item);
                    String sql6 = "";
                    if (StringUtils.isEmpty(demoData.getFifthKind())){
                        sql6 = "INSERT INTO t_dsmc_sensitivetype_class\n" +
                                "(data_item_name, classification_id, level_id, is_negative, flag, content, create_time, status)\n" +
                                "select '"+item+"',\n" +
                                "       (select classification_id\n" +
                                "        from t_dsmc_classification\n" +
                                "        where classification_name = '"+demoData.getFourthKind()+"'\n" +
                                "          and parent_id = (select classification_id\n" +
                                "                           from t_dsmc_classification\n" +
                                "                           where classification_name = '"+demoData.getThirdKind()+"'\n" +
                                "                             and parent_id = (select classification_id\n" +
                                "                                              from t_dsmc_classification\n" +
                                "                                              where classification_name = '"+demoData.getSecondKind()+"'\n" +
                                "                                                and parent_id = (select classification_id\n" +
                                "                                                                 from t_dsmc_classification\n" +
                                "                                                                 where classification_name = '"+demoData.getFirstKind()+"')))),\n" +
                                "       (select level_id from t_dsmc_data_level where level_name = '"+demoData.getLevel()+"'),\n" +
                                "       1, -- 是否负面清单：0-否，1-是\n" +
                                "       0,\n" +
                                "       '',\n" +
                                "       unix_timestamp(now()),\n" +
                                "       1;";
                    }else {
                        sql6 = "INSERT INTO t_dsmc_sensitivetype_class\n" +
                                "(data_item_name, classification_id, level_id, is_negative, flag, content, create_time, status)\n" +
                                "select '"+item+"',\n" +
                                "       (select classification_id\n" +
                                "        from t_dsmc_classification\n" +
                                "        where classification_name = '"+demoData.getFifthKind()+"'\n" +
                                "          and parent_id = (select classification_id\n" +
                                "                           from t_dsmc_classification\n" +
                                "                           where classification_name = '"+demoData.getFourthKind()+"'\n" +
                                "                             and parent_id = (select classification_id\n" +
                                "                                              from t_dsmc_classification\n" +
                                "                                              where classification_name = '"+demoData.getThirdKind()+"'\n" +
                                "                                                and parent_id = (select classification_id\n" +
                                "                                                                 from t_dsmc_classification\n" +
                                "                                                                 where classification_name = '"+demoData.getSecondKind()+"')))),\n" +
                                "       (select level_id from t_dsmc_data_level where level_name = '"+demoData.getLevel()+"'),\n" +
                                "       1, -- 是否负面清单：0-否，1-是\n" +
                                "       0,\n" +
                                "       '',\n" +
                                "       unix_timestamp(now()),\n" +
                                "       1;";
                    }
                    /*String sql6 = "INSERT INTO t_dsmc_sensitivetype_class\n" +
                            "(data_item_name, classification_id, level_id, is_negative, flag, content, create_time, status)\n" +
                            "select \n" +
                            "'"+item+"',\n" +
                            "(select classification_id from t_dsmc_classification where classification_name = '"+(StringUtils.isEmpty(demoData.getFifthKind())? demoData.getFourthKind(): demoData.getFifthKind())+"'),\n" +
                            "(select level_id from t_dsmc_data_level where level_name = '"+demoData.getLevel()+"'),\n" +
                            "1,\n" +
                            "0,\n" +
                            "'',\n" +
                            "unix_timestamp(now()),\n" +
                            "1;";*/
                    content.append(sql6).append("\r\n\n");
                }
                //String s = JSON.toJSONString(demoData);
                //System.out.println(("读取到一条数据:"+s));
            }
        })).sheet().doRead();


        String fileName1 = "D://out2.sql";

        try {
            BufferedWriter writer = new BufferedWriter(new FileWriter(fileName1));
            writer.write(content.toString());
            writer.close();
            System.out.println("文件生成成功！");
        } catch (IOException e) {
            System.out.println("文件生成失败: " + e.getMessage());
        }
    }


    private static List<String> splitData(String data) {
        List<String> items = new ArrayList<>();
        StringBuilder item = new StringBuilder();
        int parenthesesLevel = 0;

        for (char c : data.toCharArray()) {
            if (c == '、' && parenthesesLevel == 0) {
                items.add(item.toString().trim());
                item.setLength(0);
            } else {
                item.append(c);
                if (c == '（') {
                    parenthesesLevel++;
                } else if (c == '）') {
                    parenthesesLevel--;
                }
            }
        }

        if (item.length() > 0) {
            items.add(item.toString().trim());
        }

        return items;
    }
}
