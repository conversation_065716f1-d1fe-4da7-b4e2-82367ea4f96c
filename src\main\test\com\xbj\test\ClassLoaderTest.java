package com.xbj.test;

public class ClassLoaderTest {

    public static void main(String[] args) {
        for (String arg : args) {
            System.out.println(arg);
        }
        //获取系统类加载器
        ClassLoader systemClassLoader = ClassLoader.getSystemClassLoader();
        System.out.println(systemClassLoader);//sun.misc.Launcher$AppClassLoader@18b4aac2

        //获取其上层：扩展类加载器
        ClassLoader extClassLoader = systemClassLoader.getParent();
        System.out.println(extClassLoader);//sun.misc.Launcher$ExtClassLoader@5b480cf9

        //获取其上层：
        ClassLoader bootstrapClassLoader = extClassLoader.getParent();
        System.out.println(bootstrapClassLoader);//null

        //对于用户自定义类来说：默认使用系统类加载器进行加载
        ClassLoader classLoader = ClassLoaderTest.class.getClassLoader();
        System.out.println(classLoader);//sun.misc.Launcher$AppClassLoader@18b4aac2

        //String 类使用引导类加载器加载，Java的核心类库都使用 引导类加载器加载
        ClassLoader classLoader1 = String.class.getClassLoader();
        System.out.println(classLoader1);//null
        method1();
    }

    private static void method1() {
        System.out.println("开始执行method1");
        method2();
        System.out.println("method1执行结束");
    }

    private static void method2() {
        System.out.println("开始执行method2");
        method3();
        System.out.println("method2执行结束");
    }

    private static void method3() {
        System.out.println("开始执行method3");
        System.out.println("method3执行结束");
    }


}
