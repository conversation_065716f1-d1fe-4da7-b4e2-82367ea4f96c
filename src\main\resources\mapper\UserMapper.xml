<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xbj.dao.UserMapper">
    <resultMap id="BaseResultMap" type="com.xbj.entity.User">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="openid" jdbcType="VARCHAR" property="openid"/>
        <result column="unionid" jdbcType="VARCHAR" property="unionid"/>
        <result column="nick_name" jdbcType="VARCHAR" property="nickName"/>
        <result column="province" jdbcType="VARCHAR" property="province"/>
        <result column="country" jdbcType="VARCHAR" property="country"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="adress" jdbcType="VARCHAR" property="adress"/>
        <result column="gender" jdbcType="VARCHAR" property="gender"/>
        <result column="wx_number" jdbcType="VARCHAR" property="wxNumber"/>
        <result column="data1" jdbcType="VARCHAR" property="data1"/>
        <result column="data2" jdbcType="VARCHAR" property="data2"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
    id, openid, unionid, nick_name, province, country, phone, email, adress, gender,
    wx_number, data1, data2
  </sql>
    <select id="selectByExample" parameterType="com.xbj.entity.UserExample" resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from user
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from user
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from user
    where id = #{id,jdbcType=VARCHAR}
  </delete>
    <delete id="deleteByExample" parameterType="com.xbj.entity.UserExample">
        delete from user
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.xbj.entity.User">
    insert into user (id, openid, unionid,
      nick_name, province, country,
      phone, email, adress,
      gender, wx_number, data1,
      data2)
    values (#{id,jdbcType=VARCHAR}, #{openid,jdbcType=VARCHAR}, #{unionid,jdbcType=VARCHAR},
      #{nickName,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR}, #{country,jdbcType=VARCHAR},
      #{phone,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, #{adress,jdbcType=VARCHAR},
      #{gender,jdbcType=VARCHAR}, #{wxNumber,jdbcType=VARCHAR}, #{data1,jdbcType=VARCHAR},
      #{data2,jdbcType=VARCHAR})
  </insert>
    <insert id="insertSelective" parameterType="com.xbj.entity.User">
        insert into user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="openid != null">
                openid,
            </if>
            <if test="unionid != null">
                unionid,
            </if>
            <if test="nickName != null">
                nick_name,
            </if>
            <if test="province != null">
                province,
            </if>
            <if test="country != null">
                country,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="email != null">
                email,
            </if>
            <if test="adress != null">
                adress,
            </if>
            <if test="gender != null">
                gender,
            </if>
            <if test="wxNumber != null">
                wx_number,
            </if>
            <if test="data1 != null">
                data1,
            </if>
            <if test="data2 != null">
                data2,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="openid != null">
                #{openid,jdbcType=VARCHAR},
            </if>
            <if test="unionid != null">
                #{unionid,jdbcType=VARCHAR},
            </if>
            <if test="nickName != null">
                #{nickName,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                #{province,jdbcType=VARCHAR},
            </if>
            <if test="country != null">
                #{country,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="adress != null">
                #{adress,jdbcType=VARCHAR},
            </if>
            <if test="gender != null">
                #{gender,jdbcType=VARCHAR},
            </if>
            <if test="wxNumber != null">
                #{wxNumber,jdbcType=VARCHAR},
            </if>
            <if test="data1 != null">
                #{data1,jdbcType=VARCHAR},
            </if>
            <if test="data2 != null">
                #{data2,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.xbj.entity.UserExample" resultType="java.lang.Long">
        select count(*) from user
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <select id="getUserByUnionId" resultType="com.xbj.entity.User">
    select nick_name,province,country,phone,email,adress,gender,wx_number from user where openid = #{openId,jdbcType=VARCHAR}
  </select>
    <update id="updateByOpenId">
    update user set phone = #{phoneNum,jdbcType=VARCHAR},data2 = #{countryCode,jdbcType=VARCHAR} where openid = #{openId,jdbcType=VARCHAR}
  </update>
    <update id="updateByExampleSelective" parameterType="map">
        update user
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=VARCHAR},
            </if>
            <if test="record.openid != null">
                openid = #{record.openid,jdbcType=VARCHAR},
            </if>
            <if test="record.unionid != null">
                unionid = #{record.unionid,jdbcType=VARCHAR},
            </if>
            <if test="record.nickName != null">
                nick_name = #{record.nickName,jdbcType=VARCHAR},
            </if>
            <if test="record.province != null">
                province = #{record.province,jdbcType=VARCHAR},
            </if>
            <if test="record.country != null">
                country = #{record.country,jdbcType=VARCHAR},
            </if>
            <if test="record.phone != null">
                phone = #{record.phone,jdbcType=VARCHAR},
            </if>
            <if test="record.email != null">
                email = #{record.email,jdbcType=VARCHAR},
            </if>
            <if test="record.adress != null">
                adress = #{record.adress,jdbcType=VARCHAR},
            </if>
            <if test="record.gender != null">
                gender = #{record.gender,jdbcType=VARCHAR},
            </if>
            <if test="record.wxNumber != null">
                wx_number = #{record.wxNumber,jdbcType=VARCHAR},
            </if>
            <if test="record.data1 != null">
                data1 = #{record.data1,jdbcType=VARCHAR},
            </if>
            <if test="record.data2 != null">
                data2 = #{record.data2,jdbcType=VARCHAR},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update user
        set id = #{record.id,jdbcType=VARCHAR},
        openid = #{record.openid,jdbcType=VARCHAR},
        unionid = #{record.unionid,jdbcType=VARCHAR},
        nick_name = #{record.nickName,jdbcType=VARCHAR},
        province = #{record.province,jdbcType=VARCHAR},
        country = #{record.country,jdbcType=VARCHAR},
        phone = #{record.phone,jdbcType=VARCHAR},
        email = #{record.email,jdbcType=VARCHAR},
        adress = #{record.adress,jdbcType=VARCHAR},
        gender = #{record.gender,jdbcType=VARCHAR},
        wx_number = #{record.wxNumber,jdbcType=VARCHAR},
        data1 = #{record.data1,jdbcType=VARCHAR},
        data2 = #{record.data2,jdbcType=VARCHAR}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.xbj.entity.User">
        update user
        <set>
            <if test="openid != null">
                openid = #{openid,jdbcType=VARCHAR},
            </if>
            <if test="unionid != null">
                unionid = #{unionid,jdbcType=VARCHAR},
            </if>
            <if test="nickName != null">
                nick_name = #{nickName,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                province = #{province,jdbcType=VARCHAR},
            </if>
            <if test="country != null">
                country = #{country,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="adress != null">
                adress = #{adress,jdbcType=VARCHAR},
            </if>
            <if test="gender != null">
                gender = #{gender,jdbcType=VARCHAR},
            </if>
            <if test="wxNumber != null">
                wx_number = #{wxNumber,jdbcType=VARCHAR},
            </if>
            <if test="data1 != null">
                data1 = #{data1,jdbcType=VARCHAR},
            </if>
            <if test="data2 != null">
                data2 = #{data2,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xbj.entity.User">
    update user
    set openid = #{openid,jdbcType=VARCHAR},
      unionid = #{unionid,jdbcType=VARCHAR},
      nick_name = #{nickName,jdbcType=VARCHAR},
      province = #{province,jdbcType=VARCHAR},
      country = #{country,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      adress = #{adress,jdbcType=VARCHAR},
      gender = #{gender,jdbcType=VARCHAR},
      wx_number = #{wxNumber,jdbcType=VARCHAR},
      data1 = #{data1,jdbcType=VARCHAR},
      data2 = #{data2,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>
