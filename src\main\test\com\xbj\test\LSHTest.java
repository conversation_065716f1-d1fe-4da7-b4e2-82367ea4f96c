package com.xbj.test;

import info.debatty.java.lsh.LSHMinHash;
import info.debatty.java.lsh.SuperBit;

import java.io.File;
import java.io.FileInputStream;
import java.io.ObjectInputStream;
import java.util.Random;

public class LSHTest {
    public static void main(String[] args) {
        int n =10;
        SuperBit superBit = new SuperBit(n);
        String a = "前言：在Java原生调用RabbitMQ的过程中，发现需要将消息转换为二进制数组才可以写入队列，而消费者取出来的是二进制数组转换成10进制的ASCII码，需要还原为源值。\n" +
                "\n" +
                "public static void main(String[] args) {\n" +
                "// 二进制数组\n" +
                "\n" +
                "byte[] bs = \"1\".getBytes();\n" +
                "\n" +
                "System.out.println(bs);\n" +
                "\n" +
                "// 十进制ASCII码\n" +
                "\n" +
                "String bi = new BigInteger(bs).toString(10);\n" +
                "\n" +
                "System.out.println(bi);\n" +
                "\n" +
                "// 还原为源值\n" +
                "\n" +
                "System.out.println(Character.toString((char) Integer.parseInt(bi)));\n" +
                "\n" +
                "}\n" +
                "\n" +
                "MQ部分代码\n" +
                "\n" +
                "ConnectionFactory factory = new ConnectionFactory();";
        String b = "前言：在Java原生调用RabbitMQ的过程中，发现需要将消息转换为二进制数组才可以写入队列，而消费者取出来的是二进制数组转换成10进制的ASCII码，需要还原为源值。\n" +
                "\n" +
                "public static void main(String[] args) {\n" +
                "// 二进制数组\n" +
                "\n" +
                "byte[] bs = \"1\".getBytes();\n" +
                "\n" +
                "System.out.println(bs);\n" +
                "\n" +
                "// 十进制ASCII码\n" +
                "\n" +
                "String bi = new BigInteger(bs).toString(10);\n" +
                "\n" +
                "System.out.println(bi);\n" +
                "\n" +
                "// 还原为源值\n" +
                "\n" +
                "System.out.println(Character.toString((char) Integer.parseInt(bi)));\n" +
                "\n" +
                "}\n" +
                "\n" +
                "MQ部分代码\n" +
                "\n" +
                "ConnectionFactory factory = new C12312313y();";

            //superBit.signature(a.getBytes(StandardCharsets.UTF_8))

        Random rand = new Random();
        double[] v1 = new double[]{-9.69108409E8, -1.2875009E7, 1.594134731E9, 1.381064441E9, -4.00620992E8, -5.47669931E8, 6.50368047E8, 2.062383003E9, -5.68232694E8, -9.3492549E7};
        double[] v2 = new double[]{-2.69102409E8, -1.2875009E7, 1.594134731E9, 1.381064441E9, -4.00620992E8, -5.47669931E8, 6.50368047E8, 2.062383003E9, -5.68232694E8, -9.3492549E7};
        /*for (int i = 0; i < n; i++) {
            v1[i] = rand.nextInt();
            v2[i] = rand.nextInt();
        }*/

        /*long[] longs = HashUtil.cityHash128(a.getBytes(StandardCharsets.UTF_8));
        long[] longs1 = HashUtil.cityHash128(b.getBytes(StandardCharsets.UTF_8));
        Double[] a1 = (Double[]) ArrayUtil.cast(Double.class, longs);
        Double[] b1 = (Double[]) ArrayUtil.cast(Double.class, longs1);
        double[] doubles = ArrayUtil.unWrap(a1);
        double[] doubles1 = ArrayUtil.unWrap(b1);*/
        /*boolean[] signature = superBit.signature(v1);
        boolean[] signature1 = superBit.signature(v2);
        System.out.println(ArrayUtil.toString(v1));
        System.out.println(ArrayUtil.toString(v2));
        System.out.println(superBit.similarity(signature,signature1));*/
        /*int[] minHash = getMinHash(a);
        int[] minHash2 = getMinHash(b);*/
        //a.split();

    }

    private static int[] getMinHash(String str){
        int n = 100;
        double sparsity = 0.75;
        boolean[] vector = new boolean[n];
        Random rand = new Random();
        for (int j = 0; j < n; j++) {
            vector[j] = rand.nextDouble() > sparsity;
        }
        LSHMinHash saved_lsh = null;
        try {
            File tempFile = File.createTempFile(str, ".ser");
            FileInputStream fin = new FileInputStream(tempFile);
            ObjectInputStream ois = new ObjectInputStream(fin);
            saved_lsh = (LSHMinHash) ois.readObject();
        } catch (Exception e) {
            e.printStackTrace();
        }
        //saved_lsh.hash(vector);
        return saved_lsh.hash(vector);
    }

}
