package com.xbj.test;

import java.io.*;
import java.util.regex.*;

public class SQLFileRename {
    public static void main(String[] args) {
        try {
            // 读取SQL文件，指定字符编码为UTF-8
            File inputFile = new File("E:\\input.sql");
            BufferedReader reader = new BufferedReader(new InputStreamReader(new FileInputStream(inputFile), "UTF-8"));

            // 创建新的SQL文件
            File outputFile = new File("E:\\output.sql");
            BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(outputFile), "UTF-8"));

            String line;
            while ((line = reader.readLine()) != null) {
                // 使用正则表达式查找并替换表名
                String modifiedLine = replaceTableNames(line);

                // 使用正则表达式查找并替换字段名
                modifiedLine = replaceColumnNames(modifiedLine);

                writer.write(modifiedLine);
                writer.newLine();
            }

            reader.close();
            writer.close();

            System.out.println("SQL文件修改完成。修改后的文件已保存为output.sql。");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static String replaceTableNames(String input) {
        // 匹配表名，例如 "create table t_dsmc_alarm" 中的 "t_dsmc_alarm"
        Pattern pattern = Pattern.compile("create table (\\w+)");
        Matcher matcher = pattern.matcher(input);
        StringBuffer output = new StringBuffer();

        while (matcher.find()) {
            String oldTableName = matcher.group(1);
            String newTableName = renameFirstLetterUppercase(oldTableName);
            matcher.appendReplacement(output, "create table " + newTableName);
        }
        matcher.appendTail(output);

        return output.toString();
    }

    private static String replaceColumnNames(String input) {
        // 匹配字段名，例如 "uid varchar(32) not null" 中的 "uid"
        Pattern pattern = Pattern.compile("(\\w+)\\s+");
        Matcher matcher = pattern.matcher(input);
        StringBuffer output = new StringBuffer();

        while (matcher.find()) {
            String oldColumnName = matcher.group(1);
            String newColumnName = renameFirstLetterUppercase(oldColumnName);
            matcher.appendReplacement(output, newColumnName + " ");
        }
        matcher.appendTail(output);

        return output.toString();
    }

    private static String renameFirstLetterUppercase(String name) {
        return name.substring(0, 1).toUpperCase() + name.substring(1);
    }
}
