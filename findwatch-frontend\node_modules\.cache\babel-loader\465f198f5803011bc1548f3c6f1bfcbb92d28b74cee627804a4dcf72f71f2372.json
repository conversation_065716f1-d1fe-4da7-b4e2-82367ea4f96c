{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nexport default {\n  name: 'UserProfile',\n  data() {\n    return {\n      userInfo: {\n        id: null,\n        username: '',\n        nickname: '',\n        email: '',\n        phone: '',\n        province: '',\n        country: '',\n        avatar: ''\n      },\n      passwordForm: {\n        currentPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n      },\n      userStats: {\n        watchCount: 0,\n        favoriteCount: 0,\n        loginDays: 0\n      },\n      saving: false,\n      changingPassword: false\n    };\n  },\n  async mounted() {\n    await this.loadUserInfo();\n    await this.loadUserStats();\n  },\n  methods: {\n    async loadUserInfo() {\n      try {\n        // 从localStorage获取用户信息\n        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');\n        this.userInfo = {\n          id: userInfo.id || 1,\n          username: userInfo.username || 'admin',\n          nickname: userInfo.nickname || '管理员',\n          email: '<EMAIL>',\n          phone: '13800138000',\n          province: '北京市',\n          country: '中国',\n          avatar: ''\n        };\n      } catch (error) {\n        console.error('加载用户信息失败:', error);\n      }\n    },\n    async loadUserStats() {\n      try {\n        // 模拟用户统计数据\n        this.userStats = {\n          watchCount: 25,\n          favoriteCount: 8,\n          loginDays: 156\n        };\n      } catch (error) {\n        console.error('加载用户统计失败:', error);\n      }\n    },\n    async updateProfile() {\n      this.saving = true;\n      try {\n        // 这里应该调用API更新用户信息\n        await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟API调用\n\n        // 更新localStorage中的用户信息\n        localStorage.setItem('userInfo', JSON.stringify(this.userInfo));\n        alert('个人信息更新成功');\n      } catch (error) {\n        console.error('更新个人信息失败:', error);\n        alert('更新失败，请重试');\n      } finally {\n        this.saving = false;\n      }\n    },\n    async changePassword() {\n      if (this.passwordForm.newPassword !== this.passwordForm.confirmPassword) {\n        alert('新密码和确认密码不一致');\n        return;\n      }\n      if (this.passwordForm.newPassword.length < 6) {\n        alert('新密码长度不能少于6位');\n        return;\n      }\n      this.changingPassword = true;\n      try {\n        // 这里应该调用API修改密码\n        await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟API调用\n\n        alert('密码修改成功');\n        this.passwordForm = {\n          currentPassword: '',\n          newPassword: '',\n          confirmPassword: ''\n        };\n      } catch (error) {\n        console.error('修改密码失败:', error);\n        alert('修改密码失败，请重试');\n      } finally {\n        this.changingPassword = false;\n      }\n    },\n    logout() {\n      localStorage.removeItem('userToken');\n      localStorage.removeItem('userInfo');\n      this.$router.push('/login');\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "userInfo", "id", "username", "nickname", "email", "phone", "province", "country", "avatar", "passwordForm", "currentPassword", "newPassword", "confirmPassword", "userStats", "watchCount", "favoriteCount", "loginDays", "saving", "changingPassword", "mounted", "loadUserInfo", "loadUserStats", "methods", "JSON", "parse", "localStorage", "getItem", "error", "console", "updateProfile", "Promise", "resolve", "setTimeout", "setItem", "stringify", "alert", "changePassword", "length", "logout", "removeItem", "$router", "push"], "sources": ["D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\UserProfile.vue"], "sourcesContent": ["<template>\n  <div class=\"user-profile\">\n    <nav class=\"navbar\">\n      <div class=\"nav-brand\">\n        <h2>寻表记</h2>\n      </div>\n      <div class=\"nav-menu\">\n        <router-link to=\"/dashboard\" class=\"nav-item\">首页</router-link>\n        <router-link to=\"/watches\" class=\"nav-item\">手表管理</router-link>\n        <router-link to=\"/brands\" class=\"nav-item\">品牌管理</router-link>\n        <router-link to=\"/profile\" class=\"nav-item active\">个人信息</router-link>\n        <button @click=\"logout\" class=\"logout-btn\">退出</button>\n      </div>\n    </nav>\n\n    <div class=\"main-content\">\n      <div class=\"profile-container\">\n        <div class=\"profile-header\">\n          <div class=\"avatar\">\n            <img :src=\"userInfo.avatar || '/default-avatar.png'\" :alt=\"userInfo.nickname\" />\n          </div>\n          <div class=\"user-basic\">\n            <h1>{{ userInfo.nickname || userInfo.username }}</h1>\n            <p class=\"user-id\">用户ID: {{ userInfo.id }}</p>\n          </div>\n        </div>\n\n        <div class=\"profile-content\">\n          <div class=\"info-section\">\n            <h2>基本信息</h2>\n            <form @submit.prevent=\"updateProfile\" class=\"profile-form\">\n              <div class=\"form-row\">\n                <div class=\"form-group\">\n                  <label>用户名</label>\n                  <input type=\"text\" v-model=\"userInfo.username\" readonly />\n                </div>\n                <div class=\"form-group\">\n                  <label>昵称</label>\n                  <input type=\"text\" v-model=\"userInfo.nickname\" />\n                </div>\n              </div>\n              \n              <div class=\"form-row\">\n                <div class=\"form-group\">\n                  <label>邮箱</label>\n                  <input type=\"email\" v-model=\"userInfo.email\" />\n                </div>\n                <div class=\"form-group\">\n                  <label>手机号</label>\n                  <input type=\"tel\" v-model=\"userInfo.phone\" />\n                </div>\n              </div>\n              \n              <div class=\"form-row\">\n                <div class=\"form-group\">\n                  <label>省份</label>\n                  <input type=\"text\" v-model=\"userInfo.province\" />\n                </div>\n                <div class=\"form-group\">\n                  <label>国家</label>\n                  <input type=\"text\" v-model=\"userInfo.country\" />\n                </div>\n              </div>\n\n              <div class=\"form-actions\">\n                <button type=\"submit\" class=\"save-btn\" :disabled=\"saving\">\n                  {{ saving ? '保存中...' : '保存修改' }}\n                </button>\n              </div>\n            </form>\n          </div>\n\n          <div class=\"password-section\">\n            <h2>修改密码</h2>\n            <form @submit.prevent=\"changePassword\" class=\"password-form\">\n              <div class=\"form-group\">\n                <label>当前密码</label>\n                <input type=\"password\" v-model=\"passwordForm.currentPassword\" required />\n              </div>\n              <div class=\"form-group\">\n                <label>新密码</label>\n                <input type=\"password\" v-model=\"passwordForm.newPassword\" required />\n              </div>\n              <div class=\"form-group\">\n                <label>确认新密码</label>\n                <input type=\"password\" v-model=\"passwordForm.confirmPassword\" required />\n              </div>\n              <div class=\"form-actions\">\n                <button type=\"submit\" class=\"change-password-btn\" :disabled=\"changingPassword\">\n                  {{ changingPassword ? '修改中...' : '修改密码' }}\n                </button>\n              </div>\n            </form>\n          </div>\n\n          <div class=\"stats-section\">\n            <h2>我的统计</h2>\n            <div class=\"stats-grid\">\n              <div class=\"stat-item\">\n                <div class=\"stat-number\">{{ userStats.watchCount }}</div>\n                <div class=\"stat-label\">收藏手表</div>\n              </div>\n              <div class=\"stat-item\">\n                <div class=\"stat-number\">{{ userStats.favoriteCount }}</div>\n                <div class=\"stat-label\">关注品牌</div>\n              </div>\n              <div class=\"stat-item\">\n                <div class=\"stat-number\">{{ userStats.loginDays }}</div>\n                <div class=\"stat-label\">登录天数</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'UserProfile',\n  data() {\n    return {\n      userInfo: {\n        id: null,\n        username: '',\n        nickname: '',\n        email: '',\n        phone: '',\n        province: '',\n        country: '',\n        avatar: ''\n      },\n      passwordForm: {\n        currentPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n      },\n      userStats: {\n        watchCount: 0,\n        favoriteCount: 0,\n        loginDays: 0\n      },\n      saving: false,\n      changingPassword: false\n    }\n  },\n  async mounted() {\n    await this.loadUserInfo()\n    await this.loadUserStats()\n  },\n  methods: {\n    async loadUserInfo() {\n      try {\n        // 从localStorage获取用户信息\n        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')\n        this.userInfo = {\n          id: userInfo.id || 1,\n          username: userInfo.username || 'admin',\n          nickname: userInfo.nickname || '管理员',\n          email: '<EMAIL>',\n          phone: '13800138000',\n          province: '北京市',\n          country: '中国',\n          avatar: ''\n        }\n      } catch (error) {\n        console.error('加载用户信息失败:', error)\n      }\n    },\n    async loadUserStats() {\n      try {\n        // 模拟用户统计数据\n        this.userStats = {\n          watchCount: 25,\n          favoriteCount: 8,\n          loginDays: 156\n        }\n      } catch (error) {\n        console.error('加载用户统计失败:', error)\n      }\n    },\n    async updateProfile() {\n      this.saving = true\n      try {\n        // 这里应该调用API更新用户信息\n        await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用\n        \n        // 更新localStorage中的用户信息\n        localStorage.setItem('userInfo', JSON.stringify(this.userInfo))\n        \n        alert('个人信息更新成功')\n      } catch (error) {\n        console.error('更新个人信息失败:', error)\n        alert('更新失败，请重试')\n      } finally {\n        this.saving = false\n      }\n    },\n    async changePassword() {\n      if (this.passwordForm.newPassword !== this.passwordForm.confirmPassword) {\n        alert('新密码和确认密码不一致')\n        return\n      }\n      \n      if (this.passwordForm.newPassword.length < 6) {\n        alert('新密码长度不能少于6位')\n        return\n      }\n      \n      this.changingPassword = true\n      try {\n        // 这里应该调用API修改密码\n        await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用\n        \n        alert('密码修改成功')\n        this.passwordForm = {\n          currentPassword: '',\n          newPassword: '',\n          confirmPassword: ''\n        }\n      } catch (error) {\n        console.error('修改密码失败:', error)\n        alert('修改密码失败，请重试')\n      } finally {\n        this.changingPassword = false\n      }\n    },\n    logout() {\n      localStorage.removeItem('userToken')\n      localStorage.removeItem('userInfo')\n      this.$router.push('/login')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.user-profile {\n  min-height: 100vh;\n}\n\n.navbar {\n  background: white;\n  padding: 0 20px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  height: 60px;\n}\n\n.nav-brand h2 {\n  color: #333;\n  margin: 0;\n}\n\n.nav-menu {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n}\n\n.nav-item {\n  text-decoration: none;\n  color: #666;\n  padding: 8px 16px;\n  border-radius: 4px;\n  transition: all 0.3s;\n}\n\n.nav-item:hover,\n.nav-item.active {\n  color: #667eea;\n  background: #f0f2ff;\n}\n\n.logout-btn {\n  background: #ff4757;\n  color: white;\n  border: none;\n  padding: 8px 16px;\n  border-radius: 4px;\n  cursor: pointer;\n}\n\n.main-content {\n  padding: 20px;\n  max-width: 1000px;\n  margin: 0 auto;\n}\n\n.profile-container {\n  background: white;\n  border-radius: 10px;\n  box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n  overflow: hidden;\n}\n\n.profile-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 40px;\n  display: flex;\n  align-items: center;\n  gap: 30px;\n}\n\n.avatar {\n  width: 100px;\n  height: 100px;\n  border-radius: 50%;\n  overflow: hidden;\n  background: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.avatar img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.user-basic h1 {\n  margin: 0 0 10px 0;\n  font-size: 28px;\n}\n\n.user-id {\n  margin: 0;\n  opacity: 0.8;\n}\n\n.profile-content {\n  padding: 40px;\n}\n\n.info-section,\n.password-section,\n.stats-section {\n  margin-bottom: 40px;\n}\n\n.info-section h2,\n.password-section h2,\n.stats-section h2 {\n  color: #333;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 2px solid #f0f2ff;\n}\n\n.form-row {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 20px;\n  margin-bottom: 20px;\n}\n\n.form-group {\n  margin-bottom: 15px;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 5px;\n  color: #333;\n  font-weight: 500;\n}\n\n.form-group input {\n  width: 100%;\n  padding: 12px;\n  border: 1px solid #ddd;\n  border-radius: 5px;\n  font-size: 14px;\n  transition: border-color 0.3s;\n}\n\n.form-group input:focus {\n  outline: none;\n  border-color: #667eea;\n}\n\n.form-group input[readonly] {\n  background: #f5f5f5;\n  color: #666;\n}\n\n.form-actions {\n  margin-top: 20px;\n}\n\n.save-btn,\n.change-password-btn {\n  background: #667eea;\n  color: white;\n  border: none;\n  padding: 12px 30px;\n  border-radius: 5px;\n  cursor: pointer;\n  font-size: 14px;\n  transition: background 0.3s;\n}\n\n.save-btn:hover,\n.change-password-btn:hover {\n  background: #5a6fd8;\n}\n\n.save-btn:disabled,\n.change-password-btn:disabled {\n  background: #ccc;\n  cursor: not-allowed;\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n  gap: 20px;\n}\n\n.stat-item {\n  text-align: center;\n  padding: 20px;\n  background: #f8f9fa;\n  border-radius: 10px;\n}\n\n.stat-number {\n  font-size: 32px;\n  font-weight: bold;\n  color: #667eea;\n  margin-bottom: 5px;\n}\n\n.stat-label {\n  color: #666;\n  font-size: 14px;\n}\n\n@media (max-width: 768px) {\n  .form-row {\n    grid-template-columns: 1fr;\n  }\n  \n  .profile-header {\n    flex-direction: column;\n    text-align: center;\n  }\n}\n</style>\n"], "mappings": ";AAuHA,eAAe;EACbA,IAAI,EAAE,aAAa;EACnBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,QAAQ,EAAE;QACRC,EAAE,EAAE,IAAI;QACRC,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE,EAAE;QACZC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE;MACV,CAAC;MACDC,YAAY,EAAE;QACZC,eAAe,EAAE,EAAE;QACnBC,WAAW,EAAE,EAAE;QACfC,eAAe,EAAE;MACnB,CAAC;MACDC,SAAS,EAAE;QACTC,UAAU,EAAE,CAAC;QACbC,aAAa,EAAE,CAAC;QAChBC,SAAS,EAAE;MACb,CAAC;MACDC,MAAM,EAAE,KAAK;MACbC,gBAAgB,EAAE;IACpB;EACF,CAAC;EACD,MAAMC,OAAOA,CAAA,EAAG;IACd,MAAM,IAAI,CAACC,YAAY,CAAC;IACxB,MAAM,IAAI,CAACC,aAAa,CAAC;EAC3B,CAAC;EACDC,OAAO,EAAE;IACP,MAAMF,YAAYA,CAAA,EAAG;MACnB,IAAI;QACF;QACA,MAAMpB,QAAO,GAAIuB,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,UAAU,KAAK,IAAI;QACpE,IAAI,CAAC1B,QAAO,GAAI;UACdC,EAAE,EAAED,QAAQ,CAACC,EAAC,IAAK,CAAC;UACpBC,QAAQ,EAAEF,QAAQ,CAACE,QAAO,IAAK,OAAO;UACtCC,QAAQ,EAAEH,QAAQ,CAACG,QAAO,IAAK,KAAK;UACpCC,KAAK,EAAE,qBAAqB;UAC5BC,KAAK,EAAE,aAAa;UACpBC,QAAQ,EAAE,KAAK;UACfC,OAAO,EAAE,IAAI;UACbC,MAAM,EAAE;QACV;MACF,EAAE,OAAOmB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;MAClC;IACF,CAAC;IACD,MAAMN,aAAaA,CAAA,EAAG;MACpB,IAAI;QACF;QACA,IAAI,CAACR,SAAQ,GAAI;UACfC,UAAU,EAAE,EAAE;UACdC,aAAa,EAAE,CAAC;UAChBC,SAAS,EAAE;QACb;MACF,EAAE,OAAOW,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;MAClC;IACF,CAAC;IACD,MAAME,aAAaA,CAAA,EAAG;MACpB,IAAI,CAACZ,MAAK,GAAI,IAAG;MACjB,IAAI;QACF;QACA,MAAM,IAAIa,OAAO,CAACC,OAAM,IAAKC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,GAAE;;QAExD;QACAN,YAAY,CAACQ,OAAO,CAAC,UAAU,EAAEV,IAAI,CAACW,SAAS,CAAC,IAAI,CAAClC,QAAQ,CAAC;QAE9DmC,KAAK,CAAC,UAAU;MAClB,EAAE,OAAOR,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChCQ,KAAK,CAAC,UAAU;MAClB,UAAU;QACR,IAAI,CAAClB,MAAK,GAAI,KAAI;MACpB;IACF,CAAC;IACD,MAAMmB,cAAcA,CAAA,EAAG;MACrB,IAAI,IAAI,CAAC3B,YAAY,CAACE,WAAU,KAAM,IAAI,CAACF,YAAY,CAACG,eAAe,EAAE;QACvEuB,KAAK,CAAC,aAAa;QACnB;MACF;MAEA,IAAI,IAAI,CAAC1B,YAAY,CAACE,WAAW,CAAC0B,MAAK,GAAI,CAAC,EAAE;QAC5CF,KAAK,CAAC,aAAa;QACnB;MACF;MAEA,IAAI,CAACjB,gBAAe,GAAI,IAAG;MAC3B,IAAI;QACF;QACA,MAAM,IAAIY,OAAO,CAACC,OAAM,IAAKC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,GAAE;;QAExDI,KAAK,CAAC,QAAQ;QACd,IAAI,CAAC1B,YAAW,GAAI;UAClBC,eAAe,EAAE,EAAE;UACnBC,WAAW,EAAE,EAAE;UACfC,eAAe,EAAE;QACnB;MACF,EAAE,OAAOe,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;QAC9BQ,KAAK,CAAC,YAAY;MACpB,UAAU;QACR,IAAI,CAACjB,gBAAe,GAAI,KAAI;MAC9B;IACF,CAAC;IACDoB,MAAMA,CAAA,EAAG;MACPb,YAAY,CAACc,UAAU,CAAC,WAAW;MACnCd,YAAY,CAACc,UAAU,CAAC,UAAU;MAClC,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,QAAQ;IAC5B;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}