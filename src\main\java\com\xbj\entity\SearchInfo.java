package com.xbj.entity;

import java.util.List;
import java.util.Map;

public class SearchInfo {

    /**
     * 当前页
     */
    private Integer curentPage;
    /**
     * 起始行
     */
    private Integer startRow;
    /**
     * 分页大小
     */
    private Integer pageSize;
    /**
     * 查询参数
     */
    private String name;

    private String brand;

    private Map<String, List<WatchCode>> more;

    private String seriesName;

    public Map<String, List<WatchCode>> getMore() {
        return more;
    }

    public void setMore(Map<String, List<WatchCode>> more) {
        this.more = more;
    }

    public String getSeriesName() {
        return seriesName;
    }

    public void setSeriesName(String seriesName) {
        this.seriesName = seriesName;
    }

    private Integer minPrice ;
    private Integer maxPrice;

    public Integer getMinPrice() {
        return minPrice;
    }

    public void setMinPrice(Integer minPrice) {
        this.minPrice = minPrice;
    }

    public Integer getMaxPrice() {
        return maxPrice;
    }

    public void setMaxPrice(Integer maxPrice) {
        this.maxPrice = maxPrice;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public Integer getCurentPage() {
        return curentPage;
    }

    public void setCurentPage(Integer curentPage) {
        this.curentPage = curentPage;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getStartRow() {
        return startRow;
    }

    public void setStartRow(Integer startRow) {
        this.startRow = startRow;
    }
}
