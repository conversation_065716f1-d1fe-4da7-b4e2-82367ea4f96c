{"ast": null, "code": "import { createElementVNode as _createElementVNode, vModelText as _vModelText, withDirectives as _withDirectives, toDisplayString as _toDisplayString, withModifiers as _withModifiers, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"login-container\"\n};\nconst _hoisted_2 = {\n  class: \"login-box\"\n};\nconst _hoisted_3 = {\n  class: \"form-group\"\n};\nconst _hoisted_4 = {\n  class: \"form-group\"\n};\nconst _hoisted_5 = [\"disabled\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[5] || (_cache[5] = _createElementVNode(\"div\", {\n    class: \"login-header\"\n  }, [_createElementVNode(\"h1\", null, \"寻表记\"), _createElementVNode(\"p\", null, \"手表管理系统\")], -1 /* CACHED */)), _createElementVNode(\"form\", {\n    onSubmit: _cache[2] || (_cache[2] = _withModifiers((...args) => $options.handleLogin && $options.handleLogin(...args), [\"prevent\"])),\n    class: \"login-form\"\n  }, [_createElementVNode(\"div\", _hoisted_3, [_cache[3] || (_cache[3] = _createElementVNode(\"label\", {\n    for: \"username\"\n  }, \"用户名\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    id: \"username\",\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $data.loginForm.username = $event),\n    placeholder: \"请输入用户名\",\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.loginForm.username]])]), _createElementVNode(\"div\", _hoisted_4, [_cache[4] || (_cache[4] = _createElementVNode(\"label\", {\n    for: \"password\"\n  }, \"密码\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"password\",\n    id: \"password\",\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $data.loginForm.password = $event),\n    placeholder: \"请输入密码\",\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.loginForm.password]])]), _createElementVNode(\"button\", {\n    type: \"submit\",\n    class: \"login-btn\",\n    disabled: $data.loading\n  }, _toDisplayString($data.loading ? '登录中...' : '登录'), 9 /* TEXT, PROPS */, _hoisted_5)], 32 /* NEED_HYDRATION */), _cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n    class: \"login-footer\"\n  }, [_createElementVNode(\"p\", null, \"演示账号: admin / 123456\")], -1 /* CACHED */))])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "onSubmit", "_cache", "_withModifiers", "args", "$options", "handleLogin", "_hoisted_3", "for", "type", "id", "$data", "loginForm", "username", "$event", "placeholder", "required", "_hoisted_4", "password", "disabled", "loading", "_hoisted_5"], "sources": ["D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\Login.vue"], "sourcesContent": ["<template>\n  <div class=\"login-container\">\n    <div class=\"login-box\">\n      <div class=\"login-header\">\n        <h1>寻表记</h1>\n        <p>手表管理系统</p>\n      </div>\n      \n      <form @submit.prevent=\"handleLogin\" class=\"login-form\">\n        <div class=\"form-group\">\n          <label for=\"username\">用户名</label>\n          <input\n            type=\"text\"\n            id=\"username\"\n            v-model=\"loginForm.username\"\n            placeholder=\"请输入用户名\"\n            required\n          />\n        </div>\n        \n        <div class=\"form-group\">\n          <label for=\"password\">密码</label>\n          <input\n            type=\"password\"\n            id=\"password\"\n            v-model=\"loginForm.password\"\n            placeholder=\"请输入密码\"\n            required\n          />\n        </div>\n        \n        <button type=\"submit\" class=\"login-btn\" :disabled=\"loading\">\n          {{ loading ? '登录中...' : '登录' }}\n        </button>\n      </form>\n      \n      <div class=\"login-footer\">\n        <p>演示账号: admin / 123456</p>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Login',\n  data() {\n    return {\n      loginForm: {\n        username: '',\n        password: ''\n      },\n      loading: false\n    }\n  },\n  methods: {\n    async handleLogin() {\n      this.loading = true\n\n      try {\n        // 调用后端登录API - 修复接口路径\n        const response = await this.$http.post('/test/login', {\n          username: this.loginForm.username,\n          password: this.loginForm.password\n        })\n\n        // 后端返回ApiResponse格式：{code, message, data}\n        if (response.code === 200) {\n          // 保存登录状态\n          localStorage.setItem('userToken', response.data.token)\n          localStorage.setItem('userInfo', JSON.stringify(response.data))\n\n          alert('登录成功！')\n          this.$router.push('/dashboard')\n        } else {\n          alert(response.message || '登录失败')\n        }\n      } catch (error) {\n        console.error('登录失败:', error)\n        if (error.response && error.response.data && error.response.data.message) {\n          alert(error.response.data.message)\n        } else if (error.message) {\n          alert(error.message)\n        } else {\n          alert('登录失败，请检查网络连接或联系管理员')\n        }\n      } finally {\n        this.loading = false\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.login-container {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.login-box {\n  background: white;\n  padding: 40px;\n  border-radius: 10px;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\n  width: 100%;\n  max-width: 400px;\n}\n\n.login-header {\n  text-align: center;\n  margin-bottom: 30px;\n}\n\n.login-header h1 {\n  color: #333;\n  margin-bottom: 10px;\n  font-size: 28px;\n}\n\n.login-header p {\n  color: #666;\n  font-size: 14px;\n}\n\n.form-group {\n  margin-bottom: 20px;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 5px;\n  color: #333;\n  font-weight: 500;\n}\n\n.form-group input {\n  width: 100%;\n  padding: 12px;\n  border: 1px solid #ddd;\n  border-radius: 5px;\n  font-size: 14px;\n  transition: border-color 0.3s;\n}\n\n.form-group input:focus {\n  outline: none;\n  border-color: #667eea;\n}\n\n.login-btn {\n  width: 100%;\n  padding: 12px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  border-radius: 5px;\n  font-size: 16px;\n  cursor: pointer;\n  transition: opacity 0.3s;\n}\n\n.login-btn:hover {\n  opacity: 0.9;\n}\n\n.login-btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.login-footer {\n  text-align: center;\n  margin-top: 20px;\n  padding-top: 20px;\n  border-top: 1px solid #eee;\n}\n\n.login-footer p {\n  color: #999;\n  font-size: 12px;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAW;;EAObA,KAAK,EAAC;AAAY;;EAWlBA,KAAK,EAAC;AAAY;;;uBAnB7BC,mBAAA,CAuCM,OAvCNC,UAuCM,GAtCJC,mBAAA,CAqCM,OArCNC,UAqCM,G,0BApCJD,mBAAA,CAGM;IAHDH,KAAK,EAAC;EAAc,IACvBG,mBAAA,CAAY,YAAR,KAAG,GACPA,mBAAA,CAAa,WAAV,QAAM,E,qBAGXA,mBAAA,CA0BO;IA1BAE,QAAM,EAAAC,MAAA,QAAAA,MAAA,MAAAC,cAAA,KAAAC,IAAA,KAAUC,QAAA,CAAAC,WAAA,IAAAD,QAAA,CAAAC,WAAA,IAAAF,IAAA,CAAW;IAAER,KAAK,EAAC;MACxCG,mBAAA,CASM,OATNQ,UASM,G,0BARJR,mBAAA,CAAiC;IAA1BS,GAAG,EAAC;EAAU,GAAC,KAAG,qB,gBACzBT,mBAAA,CAME;IALAU,IAAI,EAAC,MAAM;IACXC,EAAE,EAAC,UAAU;+DACJC,KAAA,CAAAC,SAAS,CAACC,QAAQ,GAAAC,MAAA;IAC3BC,WAAW,EAAC,QAAQ;IACpBC,QAAQ,EAAR;iDAFSL,KAAA,CAAAC,SAAS,CAACC,QAAQ,E,KAM/Bd,mBAAA,CASM,OATNkB,UASM,G,0BARJlB,mBAAA,CAAgC;IAAzBS,GAAG,EAAC;EAAU,GAAC,IAAE,qB,gBACxBT,mBAAA,CAME;IALAU,IAAI,EAAC,UAAU;IACfC,EAAE,EAAC,UAAU;+DACJC,KAAA,CAAAC,SAAS,CAACM,QAAQ,GAAAJ,MAAA;IAC3BC,WAAW,EAAC,OAAO;IACnBC,QAAQ,EAAR;iDAFSL,KAAA,CAAAC,SAAS,CAACM,QAAQ,E,KAM/BnB,mBAAA,CAES;IAFDU,IAAI,EAAC,QAAQ;IAACb,KAAK,EAAC,WAAW;IAAEuB,QAAQ,EAAER,KAAA,CAAAS;sBAC9CT,KAAA,CAAAS,OAAO,0CAAAC,UAAA,E,sDAIdtB,mBAAA,CAEM;IAFDH,KAAK,EAAC;EAAc,IACvBG,mBAAA,CAA2B,WAAxB,sBAAoB,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}