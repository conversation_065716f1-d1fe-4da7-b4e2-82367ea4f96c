2025-07-07 14:57:42.262 [main] INFO  [] com.xbj.FindWatchApplication - Starting FindWatchApplication using Java 1.8.0_411 on DESKTOP-AFF0TER with PID 25108 (D:\devSpace\ideaWorkspace\findwatch\target\classes started by YY in D:\devSpace\ideaWorkspace\findwatch)
2025-07-07 14:57:42.264 [main] DEBUG [] com.xbj.FindWatchApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-07 14:57:42.264 [main] INFO  [] com.xbj.FindWatchApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-07 14:57:42.685 [main] WARN  [] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.support.BeanDefinitionOverrideException: Invalid bean definition with name 'traceInterceptor' defined in class path resource [com/xbj/config/MicroserviceConfig.class]: Cannot register bean definition [Root bean: class [null]; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=microserviceConfig; factoryMethodName=traceInterceptor; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/xbj/config/MicroserviceConfig.class]] for bean 'traceInterceptor': There is already [Generic bean: class [com.xbj.interceptor.TraceInterceptor]; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null; defined in file [D:\devSpace\ideaWorkspace\findwatch\target\classes\com\xbj\interceptor\TraceInterceptor.class]] bound.
2025-07-07 14:57:42.695 [main] INFO  [] o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-07 14:57:42.704 [main] ERROR [] o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

The bean 'traceInterceptor', defined in class path resource [com/xbj/config/MicroserviceConfig.class], could not be registered. A bean with that name has already been defined in file [D:\devSpace\ideaWorkspace\findwatch\target\classes\com\xbj\interceptor\TraceInterceptor.class] and overriding is disabled.

Action:

Consider renaming one of the beans or enabling overriding by setting spring.main.allow-bean-definition-overriding=true

2025-07-07 14:59:08.837 [main] INFO  [] com.xbj.FindWatchApplication - Starting FindWatchApplication using Java 1.8.0_411 on DESKTOP-AFF0TER with PID 32168 (D:\devSpace\ideaWorkspace\findwatch\target\classes started by YY in D:\devSpace\ideaWorkspace\findwatch)
2025-07-07 14:59:08.839 [main] DEBUG [] com.xbj.FindWatchApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-07 14:59:08.841 [main] INFO  [] com.xbj.FindWatchApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-07 14:59:09.446 [main] INFO  [] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-07 14:59:09.448 [main] INFO  [] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-07 14:59:09.464 [main] INFO  [] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 Redis repository interfaces.
2025-07-07 14:59:09.521 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\devSpace\ideaWorkspace\findwatch\target\classes\com\xbj\dao\UserMapper.class]
2025-07-07 14:59:09.521 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\devSpace\ideaWorkspace\findwatch\target\classes\com\xbj\dao\WatchCodeMapper.class]
2025-07-07 14:59:09.521 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\devSpace\ideaWorkspace\findwatch\target\classes\com\xbj\dao\WatchMapper.class]
2025-07-07 14:59:09.522 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.xbj.dao.UserMapper' mapperInterface
2025-07-07 14:59:09.524 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-07-07 14:59:09.524 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'watchCodeMapper' and 'com.xbj.dao.WatchCodeMapper' mapperInterface
2025-07-07 14:59:09.524 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'watchCodeMapper'.
2025-07-07 14:59:09.524 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'watchMapper' and 'com.xbj.dao.WatchMapper' mapperInterface
2025-07-07 14:59:09.525 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'watchMapper'.
2025-07-07 14:59:10.956 [main] INFO  [] o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-07 14:59:10.963 [main] INFO  [] org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 14:59:10.965 [main] INFO  [] org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-07 14:59:11.116 [main] INFO  [] o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 14:59:11.117 [main] INFO  [] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2239 ms
2025-07-07 14:59:11.350 [main] WARN  [] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController': Unsatisfied dependency expressed through field 'userService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userServiceImpl': Unsatisfied dependency expressed through field 'userMapper'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userMapper' defined in file [D:\devSpace\ideaWorkspace\findwatch\target\classes\com\xbj\dao\UserMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [org/mybatis/spring/boot/autoconfigure/MybatisAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is java.lang.IllegalStateException: Property 'configuration' and 'configLocation' can not specified with together
2025-07-07 14:59:11.354 [main] INFO  [] org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-07 14:59:11.364 [main] INFO  [] o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-07 14:59:11.380 [main] ERROR [] org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController': Unsatisfied dependency expressed through field 'userService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userServiceImpl': Unsatisfied dependency expressed through field 'userMapper'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userMapper' defined in file [D:\devSpace\ideaWorkspace\findwatch\target\classes\com\xbj\dao\UserMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [org/mybatis/spring/boot/autoconfigure/MybatisAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is java.lang.IllegalStateException: Property 'configuration' and 'configLocation' can not specified with together
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:713)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:929)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at com.xbj.FindWatchApplication.main(FindWatchApplication.java:20)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userServiceImpl': Unsatisfied dependency expressed through field 'userMapper'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userMapper' defined in file [D:\devSpace\ideaWorkspace\findwatch\target\classes\com\xbj\dao\UserMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [org/mybatis/spring/boot/autoconfigure/MybatisAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is java.lang.IllegalStateException: Property 'configuration' and 'configLocation' can not specified with together
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:713)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710)
	... 20 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userMapper' defined in file [D:\devSpace\ideaWorkspace\findwatch\target\classes\com\xbj\dao\UserMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [org/mybatis/spring/boot/autoconfigure/MybatisAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is java.lang.IllegalStateException: Property 'configuration' and 'configLocation' can not specified with together
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1534)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1417)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710)
	... 34 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [org/mybatis/spring/boot/autoconfigure/MybatisAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is java.lang.IllegalStateException: Property 'configuration' and 'configLocation' can not specified with together
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:633)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1519)
	... 45 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is java.lang.IllegalStateException: Property 'configuration' and 'configLocation' can not specified with together
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:648)
	... 58 common frames omitted
Caused by: java.lang.IllegalStateException: Property 'configuration' and 'configLocation' can not specified with together
	at org.springframework.util.Assert.state(Assert.java:76)
	at org.mybatis.spring.SqlSessionFactoryBean.afterPropertiesSet(SqlSessionFactoryBean.java:574)
	at org.mybatis.spring.SqlSessionFactoryBean.getObject(SqlSessionFactoryBean.java:720)
	at org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration.sqlSessionFactory(MybatisAutoConfiguration.java:187)
	at org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$$EnhancerBySpringCGLIB$$ea38b84c.CGLIB$sqlSessionFactory$1(<generated>)
	at org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$$EnhancerBySpringCGLIB$$ea38b84c$$FastClassBySpringCGLIB$$30480ef4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:244)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331)
	at org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$$EnhancerBySpringCGLIB$$ea38b84c.sqlSessionFactory(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	... 59 common frames omitted
2025-07-07 14:59:54.722 [main] INFO  [] com.xbj.FindWatchApplication - Starting FindWatchApplication using Java 1.8.0_411 on DESKTOP-AFF0TER with PID 24296 (D:\devSpace\ideaWorkspace\findwatch\target\classes started by YY in D:\devSpace\ideaWorkspace\findwatch)
2025-07-07 14:59:54.726 [main] DEBUG [] com.xbj.FindWatchApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-07 14:59:54.726 [main] INFO  [] com.xbj.FindWatchApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-07 14:59:55.472 [main] INFO  [] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-07 14:59:55.474 [main] INFO  [] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-07 14:59:55.489 [main] INFO  [] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 Redis repository interfaces.
2025-07-07 14:59:55.541 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\devSpace\ideaWorkspace\findwatch\target\classes\com\xbj\dao\UserMapper.class]
2025-07-07 14:59:55.542 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\devSpace\ideaWorkspace\findwatch\target\classes\com\xbj\dao\WatchCodeMapper.class]
2025-07-07 14:59:55.542 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\devSpace\ideaWorkspace\findwatch\target\classes\com\xbj\dao\WatchMapper.class]
2025-07-07 14:59:55.543 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.xbj.dao.UserMapper' mapperInterface
2025-07-07 14:59:55.544 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-07-07 14:59:55.545 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'watchCodeMapper' and 'com.xbj.dao.WatchCodeMapper' mapperInterface
2025-07-07 14:59:55.545 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'watchCodeMapper'.
2025-07-07 14:59:55.545 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'watchMapper' and 'com.xbj.dao.WatchMapper' mapperInterface
2025-07-07 14:59:55.546 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'watchMapper'.
2025-07-07 14:59:56.715 [main] INFO  [] o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-07 14:59:56.723 [main] INFO  [] org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 14:59:56.723 [main] INFO  [] org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-07 14:59:56.880 [main] INFO  [] o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 14:59:56.881 [main] INFO  [] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2121 ms
2025-07-07 14:59:57.165 [main] DEBUG [] org.mybatis.spring.SqlSessionFactoryBean - Parsed configuration file: 'class path resource [mybatis-config.xml]'
2025-07-07 14:59:57.213 [main] DEBUG [] org.mybatis.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\devSpace\ideaWorkspace\findwatch\target\classes\mapper\UserMapper.xml]'
2025-07-07 14:59:57.223 [main] DEBUG [] org.mybatis.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\devSpace\ideaWorkspace\findwatch\target\classes\mapper\WatchCodeMapper.xml]'
2025-07-07 14:59:57.238 [main] DEBUG [] org.mybatis.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\devSpace\ideaWorkspace\findwatch\target\classes\mapper\WatchMapper.xml]'
2025-07-07 14:59:57.831 [main] WARN  [] o.s.b.a.freemarker.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-07 14:59:57.854 [main] INFO  [] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 6 endpoint(s) beneath base path '/actuator'
2025-07-07 14:59:57.881 [main] WARN  [] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8080 is already in use
2025-07-07 14:59:57.898 [main] INFO  [] org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-07 14:59:57.909 [main] INFO  [] o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-07 14:59:57.922 [main] ERROR [] o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

2025-07-07 15:00:22.960 [main] INFO  [] com.xbj.FindWatchApplication - Starting FindWatchApplication using Java 1.8.0_411 on DESKTOP-AFF0TER with PID 11144 (D:\devSpace\ideaWorkspace\findwatch\target\classes started by YY in D:\devSpace\ideaWorkspace\findwatch)
2025-07-07 15:00:22.965 [main] DEBUG [] com.xbj.FindWatchApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-07 15:00:22.966 [main] INFO  [] com.xbj.FindWatchApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-07 15:00:23.749 [main] INFO  [] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-07 15:00:23.751 [main] INFO  [] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-07 15:00:23.770 [main] INFO  [] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
2025-07-07 15:00:23.828 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\devSpace\ideaWorkspace\findwatch\target\classes\com\xbj\dao\UserMapper.class]
2025-07-07 15:00:23.829 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\devSpace\ideaWorkspace\findwatch\target\classes\com\xbj\dao\WatchCodeMapper.class]
2025-07-07 15:00:23.829 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\devSpace\ideaWorkspace\findwatch\target\classes\com\xbj\dao\WatchMapper.class]
2025-07-07 15:00:23.831 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.xbj.dao.UserMapper' mapperInterface
2025-07-07 15:00:23.832 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-07-07 15:00:23.832 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'watchCodeMapper' and 'com.xbj.dao.WatchCodeMapper' mapperInterface
2025-07-07 15:00:23.832 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'watchCodeMapper'.
2025-07-07 15:00:23.833 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'watchMapper' and 'com.xbj.dao.WatchMapper' mapperInterface
2025-07-07 15:00:23.833 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'watchMapper'.
2025-07-07 15:00:25.021 [main] INFO  [] o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-07-07 15:00:25.028 [main] INFO  [] org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 15:00:25.029 [main] INFO  [] org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-07 15:00:25.170 [main] INFO  [] o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 15:00:25.171 [main] INFO  [] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2152 ms
2025-07-07 15:00:25.583 [main] DEBUG [] org.mybatis.spring.SqlSessionFactoryBean - Parsed configuration file: 'class path resource [mybatis-config.xml]'
2025-07-07 15:00:25.642 [main] DEBUG [] org.mybatis.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\devSpace\ideaWorkspace\findwatch\target\classes\mapper\UserMapper.xml]'
2025-07-07 15:00:25.653 [main] DEBUG [] org.mybatis.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\devSpace\ideaWorkspace\findwatch\target\classes\mapper\WatchCodeMapper.xml]'
2025-07-07 15:00:25.666 [main] DEBUG [] org.mybatis.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\devSpace\ideaWorkspace\findwatch\target\classes\mapper\WatchMapper.xml]'
2025-07-07 15:00:26.297 [main] WARN  [] o.s.b.a.freemarker.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-07 15:00:26.319 [main] INFO  [] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 6 endpoint(s) beneath base path '/actuator'
2025-07-07 15:00:26.348 [main] WARN  [] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8081 is already in use
2025-07-07 15:00:26.366 [main] INFO  [] org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-07 15:00:26.378 [main] INFO  [] o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-07 15:00:26.391 [main] ERROR [] o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8081 was already in use.

Action:

Identify and stop the process that's listening on port 8081 or configure this application to listen on another port.

