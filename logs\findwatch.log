2025-07-10 13:51:08.112 [main] INFO  [] com.xbj.FindWatchApplication - Starting FindWatchApplication using Java 1.8.0_411 on DESKTOP-AFF0TER with PID 29052 (D:\devSpace\ideaWorkspace\findwatch\target\classes started by YY in D:\devSpace\ideaWorkspace\findwatch)
2025-07-10 13:51:08.122 [main] DEBUG [] com.xbj.FindWatchApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-10 13:51:08.123 [main] INFO  [] com.xbj.FindWatchApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-10 13:51:09.362 [main] INFO  [] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-10 13:51:09.365 [main] INFO  [] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-10 13:51:09.391 [main] INFO  [] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
2025-07-10 13:51:09.495 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\devSpace\ideaWorkspace\findwatch\target\classes\com\xbj\dao\UserMapper.class]
2025-07-10 13:51:09.495 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\devSpace\ideaWorkspace\findwatch\target\classes\com\xbj\dao\WatchCodeMapper.class]
2025-07-10 13:51:09.495 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\devSpace\ideaWorkspace\findwatch\target\classes\com\xbj\dao\WatchMapper.class]
2025-07-10 13:51:09.496 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.xbj.dao.UserMapper' mapperInterface
2025-07-10 13:51:09.499 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-07-10 13:51:09.499 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'watchCodeMapper' and 'com.xbj.dao.WatchCodeMapper' mapperInterface
2025-07-10 13:51:09.500 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'watchCodeMapper'.
2025-07-10 13:51:09.500 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'watchMapper' and 'com.xbj.dao.WatchMapper' mapperInterface
2025-07-10 13:51:09.500 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'watchMapper'.
2025-07-10 13:51:10.158 [main] INFO  [] o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8083 (http)
2025-07-10 13:51:10.172 [main] INFO  [] org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-10 13:51:10.172 [main] INFO  [] org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-10 13:51:10.398 [main] INFO  [] o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-10 13:51:10.398 [main] INFO  [] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2215 ms
2025-07-10 13:51:11.034 [main] DEBUG [] org.mybatis.spring.SqlSessionFactoryBean - Parsed configuration file: 'class path resource [mybatis-config.xml]'
2025-07-10 13:51:11.155 [main] DEBUG [] org.mybatis.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\devSpace\ideaWorkspace\findwatch\target\classes\mapper\UserMapper.xml]'
2025-07-10 13:51:11.169 [main] DEBUG [] org.mybatis.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\devSpace\ideaWorkspace\findwatch\target\classes\mapper\WatchCodeMapper.xml]'
2025-07-10 13:51:11.184 [main] DEBUG [] org.mybatis.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\devSpace\ideaWorkspace\findwatch\target\classes\mapper\WatchMapper.xml]'
2025-07-10 13:51:12.950 [main] WARN  [] o.s.b.a.freemarker.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-10 13:51:12.984 [main] INFO  [] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 6 endpoint(s) beneath base path '/actuator'
2025-07-10 13:51:13.049 [main] INFO  [] o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8083 (http) with context path ''
2025-07-10 13:51:13.065 [main] INFO  [] com.xbj.FindWatchApplication - Started FindWatchApplication in 5.549 seconds (JVM running for 7.469)
2025-07-10 13:51:15.012 [RMI TCP Connection(1)-*************] INFO  [] com.zaxxer.hikari.HikariDataSource - FindWatchHikariCP - Starting...
2025-07-10 13:51:15.016 [RMI TCP Connection(3)-*************] INFO  [] o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-10 13:51:15.016 [RMI TCP Connection(3)-*************] INFO  [] org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-10 13:51:15.021 [RMI TCP Connection(3)-*************] INFO  [] org.springframework.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-07-10 13:51:16.309 [RMI TCP Connection(1)-*************] INFO  [] com.zaxxer.hikari.HikariDataSource - FindWatchHikariCP - Start completed.
