2025-07-09 15:51:58.960 [main] INFO  [] com.xbj.FindWatchApplication - Starting FindWatchApplication using Java 1.8.0_411 on DESKTOP-AFF0TER with PID 37572 (D:\devSpace\ideaWorkspace\findwatch\target\classes started by YY in D:\devSpace\ideaWorkspace\findwatch)
2025-07-09 15:51:58.968 [main] DEBUG [] com.xbj.FindWatchApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-09 15:51:58.968 [main] INFO  [] com.xbj.FindWatchApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-09 15:51:59.894 [main] INFO  [] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-09 15:51:59.897 [main] INFO  [] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-09 15:51:59.919 [main] INFO  [] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 Redis repository interfaces.
2025-07-09 15:51:59.996 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\devSpace\ideaWorkspace\findwatch\target\classes\com\xbj\dao\UserMapper.class]
2025-07-09 15:51:59.997 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\devSpace\ideaWorkspace\findwatch\target\classes\com\xbj\dao\WatchCodeMapper.class]
2025-07-09 15:51:59.997 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\devSpace\ideaWorkspace\findwatch\target\classes\com\xbj\dao\WatchMapper.class]
2025-07-09 15:51:59.998 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.xbj.dao.UserMapper' mapperInterface
2025-07-09 15:52:00.000 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-07-09 15:52:00.000 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'watchCodeMapper' and 'com.xbj.dao.WatchCodeMapper' mapperInterface
2025-07-09 15:52:00.000 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'watchCodeMapper'.
2025-07-09 15:52:00.000 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'watchMapper' and 'com.xbj.dao.WatchMapper' mapperInterface
2025-07-09 15:52:00.000 [main] DEBUG [] org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'watchMapper'.
2025-07-09 15:52:01.351 [main] INFO  [] o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8082 (http)
2025-07-09 15:52:01.359 [main] INFO  [] org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 15:52:01.359 [main] INFO  [] org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-09 15:52:01.556 [main] INFO  [] o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-09 15:52:01.557 [main] INFO  [] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2539 ms
2025-07-09 15:52:01.911 [main] DEBUG [] org.mybatis.spring.SqlSessionFactoryBean - Parsed configuration file: 'class path resource [mybatis-config.xml]'
2025-07-09 15:52:01.964 [main] DEBUG [] org.mybatis.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\devSpace\ideaWorkspace\findwatch\target\classes\mapper\UserMapper.xml]'
2025-07-09 15:52:01.975 [main] DEBUG [] org.mybatis.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\devSpace\ideaWorkspace\findwatch\target\classes\mapper\WatchCodeMapper.xml]'
2025-07-09 15:52:01.988 [main] DEBUG [] org.mybatis.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\devSpace\ideaWorkspace\findwatch\target\classes\mapper\WatchMapper.xml]'
2025-07-09 15:52:02.680 [main] WARN  [] o.s.b.a.freemarker.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-09 15:52:02.700 [main] INFO  [] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 6 endpoint(s) beneath base path '/actuator'
2025-07-09 15:52:02.732 [main] WARN  [] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8082 is already in use
2025-07-09 15:52:02.748 [main] INFO  [] org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-09 15:52:02.759 [main] INFO  [] o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-09 15:52:02.776 [main] ERROR [] o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8082 was already in use.

Action:

Identify and stop the process that's listening on port 8082 or configure this application to listen on another port.

