<template>
  <div class="dashboard">
    <nav class="navbar">
      <div class="nav-brand">
        <h2>寻表记</h2>
      </div>
      <div class="nav-menu">
        <router-link to="/dashboard" class="nav-item active">首页</router-link>
        <router-link to="/watches" class="nav-item">手表管理</router-link>
        <router-link to="/brands" class="nav-item">品牌管理</router-link>
        <router-link to="/profile" class="nav-item">个人信息</router-link>
        <button @click="logout" class="logout-btn">退出</button>
      </div>
    </nav>

    <div class="main-content">
      <div class="welcome-section">
        <h1>欢迎使用寻表记管理系统</h1>
        <p>专业的手表收藏与管理平台</p>
      </div>

      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">⌚</div>
          <div class="stat-info">
            <h3>{{ stats.totalWatches }}</h3>
            <p>手表总数</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">🏷️</div>
          <div class="stat-info">
            <h3>{{ stats.totalBrands }}</h3>
            <p>品牌数量</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">👥</div>
          <div class="stat-info">
            <h3>{{ stats.totalUsers }}</h3>
            <p>用户数量</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">💰</div>
          <div class="stat-info">
            <h3>¥{{ stats.totalValue.toLocaleString() }}</h3>
            <p>总价值</p>
          </div>
        </div>
      </div>

      <div class="quick-actions">
        <h2>快速操作</h2>
        <div class="action-grid">
          <router-link to="/watches" class="action-card">
            <div class="action-icon">⌚</div>
            <h3>管理手表</h3>
            <p>查看、添加、编辑手表信息</p>
          </router-link>
          
          <router-link to="/brands" class="action-card">
            <div class="action-icon">🏷️</div>
            <h3>管理品牌</h3>
            <p>管理手表品牌信息</p>
          </router-link>
          
          <router-link to="/profile" class="action-card">
            <div class="action-icon">👤</div>
            <h3>个人资料</h3>
            <p>查看和编辑个人信息</p>
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Dashboard',
  data() {
    return {
      stats: {
        totalWatches: 0,
        totalBrands: 0,
        totalUsers: 0,
        totalValue: 0
      }
    }
  },
  async mounted() {
    await this.loadStats()
  },
  methods: {
    async loadStats() {
      try {
        // 模拟数据，实际应该从API获取
        this.stats = {
          totalWatches: 156,
          totalBrands: 25,
          totalUsers: 8,
          totalValue: 2580000
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    },
    logout() {
      localStorage.removeItem('userToken')
      localStorage.removeItem('userInfo')
      this.$router.push('/login')
    }
  }
}
</script>

<style scoped>
.dashboard {
  min-height: 100vh;
}

.navbar {
  background: white;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
}

.nav-brand h2 {
  color: #333;
  margin: 0;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 20px;
}

.nav-item {
  text-decoration: none;
  color: #666;
  padding: 8px 16px;
  border-radius: 4px;
  transition: all 0.3s;
}

.nav-item:hover,
.nav-item.active {
  color: #667eea;
  background: #f0f2ff;
}

.logout-btn {
  background: #ff4757;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.3s;
}

.logout-btn:hover {
  background: #ff3742;
}

.main-content {
  padding: 40px 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-section {
  text-align: center;
  margin-bottom: 40px;
}

.welcome-section h1 {
  color: #333;
  margin-bottom: 10px;
  font-size: 32px;
}

.welcome-section p {
  color: #666;
  font-size: 16px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.stat-card {
  background: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 20px;
}

.stat-icon {
  font-size: 40px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f2ff;
  border-radius: 50%;
}

.stat-info h3 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 24px;
}

.stat-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.quick-actions h2 {
  color: #333;
  margin-bottom: 20px;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.action-card {
  background: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  text-decoration: none;
  color: inherit;
  transition: transform 0.3s, box-shadow 0.3s;
  text-align: center;
}

.action-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.action-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.action-card h3 {
  color: #333;
  margin-bottom: 10px;
}

.action-card p {
  color: #666;
  margin: 0;
}
</style>
