import { createRouter, createWebHistory } from 'vue-router'
import Login from '../views/Login.vue'
import Dashboard from '../views/Dashboard.vue'
import WatchList from '../views/WatchList.vue'
import BrandList from '../views/BrandList.vue'
import UserProfile from '../views/UserProfile.vue'
import Test from '../views/Test.vue'

const routes = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: Login
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: { requiresAuth: true }
  },
  {
    path: '/watches',
    name: 'WatchList',
    component: WatchList,
    meta: { requiresAuth: true }
  },
  {
    path: '/brands',
    name: 'BrandList',
    component: BrandList,
    meta: { requiresAuth: true }
  },
  {
    path: '/profile',
    name: 'UserProfile',
    component: UserProfile,
    meta: { requiresAuth: true }
  },
  {
    path: '/test',
    name: 'Test',
    component: Test
  }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
})

// 增强的路由守卫
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('userToken')
  const userInfo = localStorage.getItem('userInfo')

  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    if (!token || !userInfo) {
      // 没有token或用户信息，跳转到登录页
      next('/login')
      return
    }

    // 简单的token格式验证（可以根据实际token格式调整）
    try {
      const parsedUserInfo = JSON.parse(userInfo)
      if (!parsedUserInfo.username && !parsedUserInfo.openid) {
        // 用户信息格式不正确
        localStorage.removeItem('userToken')
        localStorage.removeItem('userInfo')
        next('/login')
        return
      }
    } catch (error) {
      // 用户信息解析失败
      localStorage.removeItem('userToken')
      localStorage.removeItem('userInfo')
      next('/login')
      return
    }
  }

  // 如果已登录用户访问登录页，重定向到首页
  if (to.path === '/login' && token && userInfo) {
    next('/dashboard')
    return
  }

  next()
})

export default router
