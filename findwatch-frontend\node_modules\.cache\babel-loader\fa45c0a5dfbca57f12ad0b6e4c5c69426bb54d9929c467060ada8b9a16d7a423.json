{"ast": null, "code": "export default {\n  name: 'Test',\n  data() {\n    return {\n      loading: false,\n      loginLoading: false,\n      connectionResult: null,\n      loginResult: null,\n      testUsername: 'admin',\n      testPassword: '123456',\n      frontendUrl: window.location.origin,\n      backendUrl: 'http://localhost:8083',\n      apiBaseUrl: this.$http.defaults.baseURL\n    };\n  },\n  methods: {\n    async testBackendConnection() {\n      this.loading = true;\n      this.connectionResult = null;\n      try {\n        const response = await this.$http.get('/test/hello');\n        this.connectionResult = {\n          success: true,\n          message: response.data.message || '连接成功',\n          data: response.data.data\n        };\n      } catch (error) {\n        this.connectionResult = {\n          success: false,\n          message: error.message || '连接失败',\n          data: error.response?.data\n        };\n      } finally {\n        this.loading = false;\n      }\n    },\n    async testLogin() {\n      this.loginLoading = true;\n      this.loginResult = null;\n      try {\n        const response = await this.$http.post('/test/login', {\n          username: this.testUsername,\n          password: this.testPassword\n        });\n        this.loginResult = {\n          success: true,\n          message: response.data.message || '登录成功',\n          data: response.data.data\n        };\n      } catch (error) {\n        this.loginResult = {\n          success: false,\n          message: error.response?.data?.message || error.message || '登录失败',\n          data: error.response?.data\n        };\n      } finally {\n        this.loginLoading = false;\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "loading", "loginLoading", "connectionResult", "loginResult", "testUsername", "testPassword", "frontendUrl", "window", "location", "origin", "backendUrl", "apiBaseUrl", "$http", "defaults", "baseURL", "methods", "testBackendConnection", "response", "get", "success", "message", "error", "testLogin", "post", "username", "password"], "sources": ["D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\Test.vue"], "sourcesContent": ["<template>\n  <div class=\"test-container\">\n    <h2>前后端连接测试</h2>\n    \n    <div class=\"test-section\">\n      <h3>1. 测试后端连接</h3>\n      <button @click=\"testBackendConnection\" :disabled=\"loading\">\n        {{ loading ? '测试中...' : '测试连接' }}\n      </button>\n      <div v-if=\"connectionResult\" class=\"result\">\n        <p><strong>状态:</strong> {{ connectionResult.success ? '成功' : '失败' }}</p>\n        <p><strong>消息:</strong> {{ connectionResult.message }}</p>\n        <p v-if=\"connectionResult.data\"><strong>数据:</strong> {{ connectionResult.data }}</p>\n      </div>\n    </div>\n\n    <div class=\"test-section\">\n      <h3>2. 测试登录接口</h3>\n      <div class=\"login-form\">\n        <input v-model=\"testUsername\" placeholder=\"用户名 (admin)\" />\n        <input v-model=\"testPassword\" type=\"password\" placeholder=\"密码 (123456)\" />\n        <button @click=\"testLogin\" :disabled=\"loginLoading\">\n          {{ loginLoading ? '登录中...' : '测试登录' }}\n        </button>\n      </div>\n      <div v-if=\"loginResult\" class=\"result\">\n        <p><strong>状态:</strong> {{ loginResult.success ? '成功' : '失败' }}</p>\n        <p><strong>消息:</strong> {{ loginResult.message }}</p>\n        <div v-if=\"loginResult.data\">\n          <p><strong>Token:</strong> {{ loginResult.data.token }}</p>\n          <p><strong>用户名:</strong> {{ loginResult.data.username }}</p>\n          <p><strong>昵称:</strong> {{ loginResult.data.nickname }}</p>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"test-section\">\n      <h3>3. 网络信息</h3>\n      <p><strong>前端地址:</strong> {{ frontendUrl }}</p>\n      <p><strong>后端地址:</strong> {{ backendUrl }}</p>\n      <p><strong>API基础URL:</strong> {{ apiBaseUrl }}</p>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Test',\n  data() {\n    return {\n      loading: false,\n      loginLoading: false,\n      connectionResult: null,\n      loginResult: null,\n      testUsername: 'admin',\n      testPassword: '123456',\n      frontendUrl: window.location.origin,\n      backendUrl: 'http://localhost:8083',\n      apiBaseUrl: this.$http.defaults.baseURL\n    }\n  },\n  methods: {\n    async testBackendConnection() {\n      this.loading = true;\n      this.connectionResult = null;\n      \n      try {\n        const response = await this.$http.get('/test/hello');\n        this.connectionResult = {\n          success: true,\n          message: response.data.message || '连接成功',\n          data: response.data.data\n        };\n      } catch (error) {\n        this.connectionResult = {\n          success: false,\n          message: error.message || '连接失败',\n          data: error.response?.data\n        };\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    async testLogin() {\n      this.loginLoading = true;\n      this.loginResult = null;\n      \n      try {\n        const response = await this.$http.post('/test/login', {\n          username: this.testUsername,\n          password: this.testPassword\n        });\n        \n        this.loginResult = {\n          success: true,\n          message: response.data.message || '登录成功',\n          data: response.data.data\n        };\n      } catch (error) {\n        this.loginResult = {\n          success: false,\n          message: error.response?.data?.message || error.message || '登录失败',\n          data: error.response?.data\n        };\n      } finally {\n        this.loginLoading = false;\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.test-container {\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 20px;\n}\n\n.test-section {\n  margin-bottom: 30px;\n  padding: 20px;\n  border: 1px solid #ddd;\n  border-radius: 8px;\n}\n\n.login-form {\n  margin: 10px 0;\n}\n\n.login-form input {\n  margin-right: 10px;\n  padding: 8px;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n}\n\nbutton {\n  padding: 10px 20px;\n  background-color: #007bff;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n}\n\nbutton:disabled {\n  background-color: #ccc;\n  cursor: not-allowed;\n}\n\nbutton:hover:not(:disabled) {\n  background-color: #0056b3;\n}\n\n.result {\n  margin-top: 15px;\n  padding: 15px;\n  background-color: #f8f9fa;\n  border-radius: 4px;\n  border-left: 4px solid #007bff;\n}\n\nh2, h3 {\n  color: #333;\n}\n</style>\n"], "mappings": "AA8CA,eAAe;EACbA,IAAI,EAAE,MAAM;EACZC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,OAAO,EAAE,KAAK;MACdC,YAAY,EAAE,KAAK;MACnBC,gBAAgB,EAAE,IAAI;MACtBC,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE,OAAO;MACrBC,YAAY,EAAE,QAAQ;MACtBC,WAAW,EAAEC,MAAM,CAACC,QAAQ,CAACC,MAAM;MACnCC,UAAU,EAAE,uBAAuB;MACnCC,UAAU,EAAE,IAAI,CAACC,KAAK,CAACC,QAAQ,CAACC;IAClC;EACF,CAAC;EACDC,OAAO,EAAE;IACP,MAAMC,qBAAqBA,CAAA,EAAG;MAC5B,IAAI,CAAChB,OAAM,GAAI,IAAI;MACnB,IAAI,CAACE,gBAAe,GAAI,IAAI;MAE5B,IAAI;QACF,MAAMe,QAAO,GAAI,MAAM,IAAI,CAACL,KAAK,CAACM,GAAG,CAAC,aAAa,CAAC;QACpD,IAAI,CAAChB,gBAAe,GAAI;UACtBiB,OAAO,EAAE,IAAI;UACbC,OAAO,EAAEH,QAAQ,CAAClB,IAAI,CAACqB,OAAM,IAAK,MAAM;UACxCrB,IAAI,EAAEkB,QAAQ,CAAClB,IAAI,CAACA;QACtB,CAAC;MACH,EAAE,OAAOsB,KAAK,EAAE;QACd,IAAI,CAACnB,gBAAe,GAAI;UACtBiB,OAAO,EAAE,KAAK;UACdC,OAAO,EAAEC,KAAK,CAACD,OAAM,IAAK,MAAM;UAChCrB,IAAI,EAAEsB,KAAK,CAACJ,QAAQ,EAAElB;QACxB,CAAC;MACH,UAAU;QACR,IAAI,CAACC,OAAM,GAAI,KAAK;MACtB;IACF,CAAC;IAED,MAAMsB,SAASA,CAAA,EAAG;MAChB,IAAI,CAACrB,YAAW,GAAI,IAAI;MACxB,IAAI,CAACE,WAAU,GAAI,IAAI;MAEvB,IAAI;QACF,MAAMc,QAAO,GAAI,MAAM,IAAI,CAACL,KAAK,CAACW,IAAI,CAAC,aAAa,EAAE;UACpDC,QAAQ,EAAE,IAAI,CAACpB,YAAY;UAC3BqB,QAAQ,EAAE,IAAI,CAACpB;QACjB,CAAC,CAAC;QAEF,IAAI,CAACF,WAAU,GAAI;UACjBgB,OAAO,EAAE,IAAI;UACbC,OAAO,EAAEH,QAAQ,CAAClB,IAAI,CAACqB,OAAM,IAAK,MAAM;UACxCrB,IAAI,EAAEkB,QAAQ,CAAClB,IAAI,CAACA;QACtB,CAAC;MACH,EAAE,OAAOsB,KAAK,EAAE;QACd,IAAI,CAAClB,WAAU,GAAI;UACjBgB,OAAO,EAAE,KAAK;UACdC,OAAO,EAAEC,KAAK,CAACJ,QAAQ,EAAElB,IAAI,EAAEqB,OAAM,IAAKC,KAAK,CAACD,OAAM,IAAK,MAAM;UACjErB,IAAI,EAAEsB,KAAK,CAACJ,QAAQ,EAAElB;QACxB,CAAC;MACH,UAAU;QACR,IAAI,CAACE,YAAW,GAAI,KAAK;MAC3B;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}