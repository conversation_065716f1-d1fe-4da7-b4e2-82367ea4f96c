package com.xbj.test;

import sun.misc.Launcher;

import java.net.URL;
import java.security.Provider;

public class ClassLoaderTest1 {
    public static void main(String[] args) {
        System.out.println("***********启动类加载器**************");
        //获取BootstrapClassLoader能够加载的api路径
        URL[] urLs = Launcher.getBootstrapClassPath().getURLs();
        for (URL ele:urLs){
            System.out.println(ele.toExternalForm());
        }
        //从这些api路径下，随意选择一个类，看看它的类加载器是谁？===》引导类加载器
        ClassLoader classLoader = Provider.class.getClassLoader();
        System.out.println(classLoader);//null  引导类加载器

        System.out.println("***********扩展类加载器**************");
        String extDirs = System.getProperty("java.ext.dirs");
        for (String path : extDirs.split(";")) {
            System.out.println(path);
        }

        //从上面任意选择一个类，来看看它的类加载器是什么？======》扩展类加载器
        ClassLoader classLoader1 = String.class.getClassLoader();
        System.out.println(classLoader1);//sun.misc.Launcher$ExtClassLoader@12edcd21


    }
}
