package com.xbj.controller;

import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.xbj.constant.Constants;
import com.xbj.entity.User;
import com.xbj.entity.UserExample;
import com.xbj.service.UserService;
import com.xbj.service.WeixinService;
import com.xbj.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @create 2020-01-13 15:17
 */
@RestController
@RequestMapping("/login")
public class LoginController {

    private final static Logger logger = LoggerFactory.getLogger(LoginController.class);

    private final static String MESSAGE_SUCCESS = "OK";
    /**
     * 记录手机号码发送的次数
     */
    private static final Map<String,Map<String,Object>> PHONE_FREQUENCY = new ConcurrentHashMap<>();
    private static final Map<String,Map<String,Object>> IP_FREQUENCY = new ConcurrentHashMap<>();
    private final UserService userService;
    private final RedisUtil redisUtil;
    private final WeixinService weixinService;

    @Autowired
    public LoginController(UserService userService, RedisUtil redisUtil, WeixinService weixinService) {
        this.userService = userService;
        this.redisUtil = redisUtil;
        this.weixinService = weixinService;
    }

    @PostMapping("/getSession")
    public String login(@RequestBody String wxCode) {
        JSONObject object = JSONObject.parseObject(wxCode);
        return checkIsLogin(object.get("wxCode").toString());
    }

    @PostMapping("/getPhone")
    public boolean getPhoneAndUpdateUser(@RequestBody Map<String,Object> map){
        boolean result = false;
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String token = request.getHeader("authorization");
        Map res = JWTUtils.verifyTokenGetResult(token, "lookforwatch");
        boolean isSuccess = Boolean.parseBoolean(res.get("isSuccess").toString());
        if (isSuccess){
            String iv = map.get("iv").toString();
            String encryptedData = map.get("encryptedData").toString();
            String session_key = res.get("session_key").toString();
            String userInfo = WexiDecode.decrypt(session_key, iv, encryptedData);
            JSONObject user = JSONObject.parseObject(userInfo);
            userService.updateByOpenId(user.getString("purePhoneNumber"),user.getString("countryCode"),res.get("openid").toString());
            result = true;
        }
        return result;
    }

    @PostMapping("/registerUser")
    public User registerUser(@RequestBody User user) {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String token = request.getHeader("authorization");
        Map<String,Object> res = JWTUtils.verifyTokenGetResult(token, "lookforwatch");
        boolean isSuccess = Boolean.parseBoolean(res.get("isSuccess").toString());
        if (isSuccess) {
            String openid = res.get("openid").toString();
            User resUser = userService.getUserByUnionId(openid);
            if (resUser!=null){
                return resUser;
            }else {
                user.setId(UUID.randomUUID().toString());
                user.setGender("1".equals(user.getGender()) ? "男" : "女");
                user.setOpenid(openid);
                user.setData1("");
                userService.insertSelective(user);
                redisUtil.set(Constants.USER_KEY+user.getOpenid(),"");
            }

        }
        return user;
    }

    @PostMapping("/sendMsg")
    public Map<String,Object> sendMsg(@RequestBody User user){
        String phoneNum = user.getPhone();
        HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
        String token = request.getHeader("authorization");
        Map<String,Object> res = JWTUtils.verifyTokenGetResult(token, "lookforwatch");
        boolean isSuccess = Boolean.parseBoolean(res.get("isSuccess").toString());
        Map<String,Object> resMap = new HashMap<>();
        resMap.put("isSuccess",false);
        if (isSuccess && isaLegalUser(res.get("openid").toString())){
            if (!phoneIsExist(user)){
                try {
                    if (!redisUtil.hasKey(Constants.YZM_KEY+phoneNum)){
                        this.sendMsg(phoneNum,resMap,request);
                    }else {
                        JSONObject phoneCode = redisUtil.getObjectString(Constants.YZM_KEY + phoneNum);
                        //如果间隔时间大于1分钟，可以继续发送验证码
                        if (checkIsOutRequestTimes(phoneCode.getString("time"))){
                            this.sendMsg(phoneNum,resMap,request);
                        }else {
                            resMap.put("errMsg","请等待倒计时结束，再次发送！");
                        }
                    }

                }catch (Exception e){
                    logger.error("调用短信服务出现异常：",e);
                }
            }else {
                resMap.put("errMsg","该号码已存在，请使用其他号码！");
            }
        }else {
            resMap.put("errMsg","您还未登录，请登录后再试！");
        }
        return resMap;
    }

    @PostMapping("/checkCodeAndUpdateInfo")
    public Map<String,Object> checkCodeIsRight(@RequestBody User user,@RequestParam String code){
        HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
        Map<String,Object> map = new HashMap<>(16);
        map.put("isSuccess",false);
        String token = request.getHeader("authorization");
        Map<String,Object> res = JWTUtils.verifyTokenGetResult(token, "lookforwatch");
        boolean isSuccess = Boolean.parseBoolean(res.get("isSuccess").toString());
        if (isSuccess && isaLegalUser(res.get("openid").toString())){
            if (redisUtil.hasKey(Constants.YZM_KEY+user.getPhone())){
                JSONObject phoneCode = redisUtil.getObjectString(Constants.YZM_KEY + user.getPhone());
                String sysCode = phoneCode.getString("code");
                if (sysCode.equals(code)){
                    //更新数据库信息
                    UserExample userExample = new UserExample();
                    UserExample.Criteria or = userExample.or();
                    or.andOpenidEqualTo(res.get("openid").toString());
                    userService.updateByExampleSelective(user,userExample);
                    map.put("isSuccess",true);
                    //设置该手机号码已经被使用
                    redisUtil.setEx(Constants.USER_KEY+user.getPhone(),"true",1,TimeUnit.DAYS);
                }else {
                    map.put("errMsg","验证码错误!");
                }
            }else {
                map.put("errMsg","未发送验证码或已超时!");
            }
        }else {
            map.put("errMsg","请登陆后重试!");
        }
        return map;
    }

    private boolean phoneIsExist(User user) {
        String phone = user.getPhone();
        UserExample userExample = new UserExample();
        UserExample.Criteria or = userExample.or();
        or.andPhoneEqualTo(phone);
        return redisUtil.hasKey(Constants.USER_KEY+user.getPhone()) || userService.selectByExample(userExample).size()>0;
    }

    private boolean isaLegalUser(String openId) {
        //如果缓存中存在 或者 数据库可以查到，则是有效用户
        return redisUtil.hasKey(Constants.USER_KEY+openId) || userService.getUserByUnionId(openId)!=null;
    }

    private void sendMsg(String phoneNum,Map<String,Object> resMap,HttpServletRequest request) throws Exception{
        boolean canSendMsg = isCanSendMsg(phoneNum+"_limit", resMap, request);
        if (canSendMsg){
            //随机生成验证码
            String code = AliyunSmsUtils.getNewCode();
            SendSmsResponse smsResponse = AliyunSmsUtils.sendSms(phoneNum, code);
            String responseCode = smsResponse.getCode();
            if (LoginController.MESSAGE_SUCCESS.equals(responseCode)){
                resMap.put("isSuccess",true);
                Map<String,String> codeInfo = new HashMap<>();
                codeInfo.put("code",code);
                codeInfo.put("time",TimeUtils.dateFormat(new Date()));
                //生成手机号 和 验证码的关系
                redisUtil.setExpireObject(Constants.YZM_KEY+phoneNum,codeInfo,5,TimeUnit.MINUTES);
                //统计某个手机号码一天发送了多少次短信验证码
                setCodeTimesLimit(phoneNum+"_limit");
                String realIp = HttpsUtils.getRequestRealIp(request);
                //统计某个ip一天发送了多少次短信验证码
                setCodeTimesLimit(realIp);
            }
        }

    }

    private void setCodeTimesLimit(String phoneNum) {
        if (!redisUtil.hasKey(Constants.YZM_KEY+phoneNum)){
            Map<String,Object> map = new HashMap<>(16);
            map.put("times",1);
            map.put("date", TimeUtils.dateFormat10(new Date()));
            redisUtil.setExpireObject(Constants.YZM_KEY+phoneNum,map,1, TimeUnit.DAYS);
        }else {
            JSONObject ipObject = redisUtil.getObjectString(Constants.YZM_KEY + phoneNum);
            int times = ipObject.getIntValue("times")+1;
            ipObject.put("times",times);
            //更新ip/手机号发送短信的次数值，不更新过期时间。
            redisUtil.setValueNotUpdateDate(Constants.YZM_KEY+phoneNum,ipObject.toJSONString(),0);
        }
    }

    /**
     * 校验ip，手机号，获取验证码的次数是否超过限制。
     * @param phoneNum 手机号
     * @param resMap resMap
     * @param request request
     * @return boolean
     */
    private boolean isCanSendMsg(String phoneNum, Map<String,Object> resMap, HttpServletRequest request) {
        boolean canSendMsg = true;
        if (redisUtil.hasKey(Constants.YZM_KEY+phoneNum)){
            JSONObject phoneObject = redisUtil.getObjectString(Constants.YZM_KEY + phoneNum);
            int times = phoneObject.getIntValue("times");
            if(times>Constants.MAX_REQUEST_PHONE_TIMES_ONE_DAY){
                resMap.put("isSuccess",false);
                resMap.put("errMsg","当前手机号超过最大请求次数！");
                canSendMsg = false;
            }
        }
        String realIp = HttpsUtils.getRequestRealIp(request);
        if (redisUtil.hasKey(Constants.YZM_KEY+realIp)){
            JSONObject phoneObject = redisUtil.getObjectString(Constants.YZM_KEY + realIp);
            int times = phoneObject.getIntValue("times");
            if(times>Constants.MAX_REQUEST_IP_TIMES_ONE_DAY){
                resMap.put("isSuccess",false);
                resMap.put("errMsg","当前ip超过最大请求次数！");
                canSendMsg = false;
            }
        }
        return canSendMsg;
    }

    /**
     *
     * @param begin 开始时间
     * @return true 可以再次发送验证码。false 不能发送
     */
    private boolean checkIsOutRequestTimes(String begin) throws Exception{
        Date date = TimeUtils.StringToDate(begin);
        long l = TimeUtils.dateDiff(date, new Date());
        return l> Constants.TIME_INTERVAL;
    }

    private boolean getUserByOpenId(String userId){
        UserExample example = new UserExample();
        UserExample.Criteria or = example.or();
        or.andIdEqualTo(userId);
        return userService.selectByExample(example).size()>0;
    }

    private String checkIsLogin(String wxCode) {
        String res = "";
        if (!StringUtils.isEmpty(wxCode)) {
            JSONObject sessionKeyAndOpenId = weixinService.getSessionKeyAndOpenId(wxCode);
            //用户唯一标识
            String openid = sessionKeyAndOpenId.get("openid").toString();
            //会话密钥
            String session_key = sessionKeyAndOpenId.get("session_key").toString();
            /*//用户在开放平台的唯一标识符，在满足 UnionID 下发条件的情况下会返回，详见 UnionID 机制说明。
            //String unionid = sessionKeyAndOpenId.get("unionid").toString();
            //错误码
            //String errcode = sessionKeyAndOpenId.get("errcode").toString();
            //错误信息
            //String errmsg = sessionKeyAndOpenId.get("errmsg").toString();
            //String sessionid = MD5Utils.MD5Encode(openid + session_key, "utf8");*/
            Map<String,Object> map = new HashMap<>();
            map.put("openid",openid);
            map.put("session_key",session_key);
            res = JWTUtils.generateHMAC256Token(map, "lookforwatch");
        }
        return res;
    }

    /*@RequestMapping("/test")
    public String testSomething(HttpServletRequest request){
        try {
            String str = getRequestPostStr(request);
            System.out.println(str);
        } catch (IOException e) {
            e.printStackTrace();
        }

        return "sss";
    }
    public String getRequestPostStr(HttpServletRequest request) throws IOException{
        byte buffer[] = getRequestPostBytes(request);
        String charEncoding = request.getCharacterEncoding();
        if(charEncoding == null){
            charEncoding = "UTF-8";
        }
        return new String(buffer,charEncoding);
    }
    public byte[] getRequestPostBytes(HttpServletRequest request) throws IOException {
        int contentLength = request.getContentLength();
        if(contentLength<0){
            return null;
        }
        byte[] buffer = new byte[contentLength];
        for(int i = 0;i<contentLength;){
            int readlen = request.getInputStream().read(buffer,i,contentLength - i);
            if(readlen == -1){
                break;
            }
            i += readlen;
        }
        return buffer;
    }*/
}
