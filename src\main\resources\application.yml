server:
  port: 8083
  servlet:
    context-path: /
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  application:
    name: findwatch
  
  # 数据源配置
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************************************************************
    username: findwatch
    password: FLZX3000C_ysyhl9t
    hikari:
      minimum-idle: 10
      maximum-pool-size: 30
      idle-timeout: 3600000
      max-lifetime: 7200000
      connection-timeout: 10000
      connection-test-query: SELECT 1
      pool-name: FindWatchHikariCP
  
  # Redis配置
  redis:
    host: *************
    port: 6379
    password: FLZX3000C_ysyhl9t
    timeout: 2000ms
    jedis:
      pool:
        max-active: 50
        max-idle: 30
        min-idle: 10
        max-wait: 2000ms
  
  # 文件上传配置
  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      max-request-size: 100MB
      file-size-threshold: 2KB
  
  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null

# MyBatis配置
mybatis:
  config-location: classpath:mybatis-config.xml
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.xbj.entity

# 微信小程序配置
weixin:
  url: https://api.weixin.qq.com/sns/jscode2session
  appId: wxbb5f90fa8f2dcd87
  appSecret: fa2989dea60eaab1040d8d5809c150ca

# 阿里云短信配置
aliyun:
  sms:
    access-key-id: ${ALIYUN_ACCESS_KEY_ID:}
    access-key-secret: ${ALIYUN_ACCESS_KEY_SECRET:}
    region-id: cn-hangzhou
    sign-name: 寻表记
    template-code: SMS_123456789

# 日志配置
logging:
  level:
    com.xbj: debug
    org.springframework: info
    org.mybatis: debug
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-}] %logger{50} - %msg%n'
    file: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-}] %logger{50} - %msg%n'
  file:
    name: logs/findwatch.log
    max-size: 100MB
    max-history: 30

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,env,configprops
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
      show-components: always
  metrics:
    export:
      prometheus:
        enabled: true
    tags:
      application: findwatch
      environment: ${spring.profiles.active:dev}

# 应用信息
info:
  app:
    name: 寻表记
    description: 手表信息查询系统
    version: 1.0.0
    encoding: UTF-8
    java:
      version: ${java.version}

# Swagger/OpenAPI配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
    operations-sorter: method
    tags-sorter: alpha
    try-it-out-enabled: true
