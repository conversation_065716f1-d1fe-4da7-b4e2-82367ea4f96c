{"ast": null, "code": "import { createApp } from 'vue';\nimport App from './App.vue';\nimport router from './router';\nimport axios from 'axios';\n\n// 配置axios\naxios.defaults.baseURL = 'http://localhost:8082/api';\naxios.defaults.timeout = 10000;\n\n// 请求拦截器\naxios.interceptors.request.use(config => {\n  // 可以在这里添加token等认证信息\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 响应拦截器\naxios.interceptors.response.use(response => {\n  return response.data;\n}, error => {\n  console.error('API请求错误:', error);\n  return Promise.reject(error);\n});\nconst app = createApp(App);\napp.config.globalProperties.$http = axios;\napp.use(router);\napp.mount('#app');", "map": {"version": 3, "names": ["createApp", "App", "router", "axios", "defaults", "baseURL", "timeout", "interceptors", "request", "use", "config", "error", "Promise", "reject", "response", "data", "console", "app", "globalProperties", "$http", "mount"], "sources": ["D:/devSpace/ideaWorkspace/findwatch/findwatch-frontend/src/main.js"], "sourcesContent": ["import { createApp } from 'vue'\nimport App from './App.vue'\nimport router from './router'\nimport axios from 'axios'\n\n// 配置axios\naxios.defaults.baseURL = 'http://localhost:8082/api'\naxios.defaults.timeout = 10000\n\n// 请求拦截器\naxios.interceptors.request.use(\n  config => {\n    // 可以在这里添加token等认证信息\n    return config\n  },\n  error => {\n    return Promise.reject(error)\n  }\n)\n\n// 响应拦截器\naxios.interceptors.response.use(\n  response => {\n    return response.data\n  },\n  error => {\n    console.error('API请求错误:', error)\n    return Promise.reject(error)\n  }\n)\n\nconst app = createApp(App)\napp.config.globalProperties.$http = axios\napp.use(router)\napp.mount('#app')\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,KAAK;AAC/B,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,OAAO;;AAEzB;AACAA,KAAK,CAACC,QAAQ,CAACC,OAAO,GAAG,2BAA2B;AACpDF,KAAK,CAACC,QAAQ,CAACE,OAAO,GAAG,KAAK;;AAE9B;AACAH,KAAK,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CAC5BC,MAAM,IAAI;EACR;EACA,OAAOA,MAAM;AACf,CAAC,EACDC,KAAK,IAAI;EACP,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAR,KAAK,CAACI,YAAY,CAACO,QAAQ,CAACL,GAAG,CAC7BK,QAAQ,IAAI;EACV,OAAOA,QAAQ,CAACC,IAAI;AACtB,CAAC,EACDJ,KAAK,IAAI;EACPK,OAAO,CAACL,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;EAChC,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,MAAMM,GAAG,GAAGjB,SAAS,CAACC,GAAG,CAAC;AAC1BgB,GAAG,CAACP,MAAM,CAACQ,gBAAgB,CAACC,KAAK,GAAGhB,KAAK;AACzCc,GAAG,CAACR,GAAG,CAACP,MAAM,CAAC;AACfe,GAAG,CAACG,KAAK,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}