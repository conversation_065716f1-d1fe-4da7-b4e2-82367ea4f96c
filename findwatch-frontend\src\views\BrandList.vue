<template>
  <div class="brand-list">
    <nav class="navbar">
      <div class="nav-brand">
        <h2>寻表记</h2>
      </div>
      <div class="nav-menu">
        <router-link to="/dashboard" class="nav-item">首页</router-link>
        <router-link to="/watches" class="nav-item">手表管理</router-link>
        <router-link to="/brands" class="nav-item active">品牌管理</router-link>
        <router-link to="/profile" class="nav-item">个人信息</router-link>
        <button @click="logout" class="logout-btn">退出</button>
      </div>
    </nav>

    <div class="main-content">
      <div class="page-header">
        <h1>品牌管理</h1>
        <button @click="showAddDialog = true" class="add-btn">添加品牌</button>
      </div>

      <div class="search-bar">
        <input
          type="text"
          v-model="searchQuery"
          placeholder="搜索品牌名称..."
          class="search-input"
        />
        <button @click="loadBrands" class="search-btn">搜索</button>
      </div>

      <div class="brand-table" v-if="brands.length > 0">
        <table>
          <thead>
            <tr>
              <th>品牌名称</th>
              <th>国家/地区</th>
              <th>成立年份</th>
              <th>描述</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="brand in filteredBrands" :key="brand.id">
              <td class="brand-name">{{ brand.name }}</td>
              <td>{{ brand.country || '-' }}</td>
              <td>{{ brand.foundedYear || '-' }}</td>
              <td class="description">{{ brand.description || '-' }}</td>
              <td class="actions">
                <button @click="editBrand(brand)" class="edit-btn">编辑</button>
                <button @click="deleteBrand(brand.id)" class="delete-btn">删除</button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div v-else class="empty-state">
        <p>暂无品牌数据</p>
        <button @click="loadBrands" class="reload-btn">重新加载</button>
      </div>
    </div>

    <!-- 添加/编辑对话框 -->
    <div v-if="showAddDialog || showEditDialog" class="dialog-overlay" @click="closeDialogs">
      <div class="dialog" @click.stop>
        <h2>{{ showAddDialog ? '添加品牌' : '编辑品牌' }}</h2>
        <form @submit.prevent="saveBrand">
          <div class="form-group">
            <label>品牌名称</label>
            <input type="text" v-model="currentBrand.name" required />
          </div>
          <div class="form-group">
            <label>国家/地区</label>
            <input type="text" v-model="currentBrand.country" />
          </div>
          <div class="form-group">
            <label>成立年份</label>
            <input type="number" v-model="currentBrand.foundedYear" />
          </div>
          <div class="form-group">
            <label>描述</label>
            <textarea v-model="currentBrand.description" rows="3"></textarea>
          </div>
          <div class="dialog-actions">
            <button type="button" @click="closeDialogs" class="cancel-btn">取消</button>
            <button type="submit" class="save-btn">保存</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BrandList',
  data() {
    return {
      brands: [],
      searchQuery: '',
      showAddDialog: false,
      showEditDialog: false,
      currentBrand: {
        id: null,
        name: '',
        country: '',
        foundedYear: null,
        description: ''
      }
    }
  },
  computed: {
    filteredBrands() {
      if (!this.searchQuery) return this.brands
      return this.brands.filter(brand =>
        brand.name.toLowerCase().includes(this.searchQuery.toLowerCase())
      )
    }
  },
  async mounted() {
    await this.loadBrands()
  },
  methods: {
    async loadBrands() {
      try {
        // 修复接口路径：使用正确的后端接口获取品牌数据
        const response = await this.$http.get('/v1/watches/brands')
        this.brands = response.data || []
      } catch (error) {
        console.error('加载品牌列表失败:', error)
        // 使用模拟数据
        this.brands = [
          {
            id: 1,
            name: '劳力士',
            country: '瑞士',
            foundedYear: 1905,
            description: '世界著名的奢华手表品牌'
          },
          {
            id: 2,
            name: '欧米茄',
            country: '瑞士',
            foundedYear: 1848,
            description: '瑞士著名手表制造商'
          },
          {
            id: 3,
            name: '百达翡丽',
            country: '瑞士',
            foundedYear: 1839,
            description: '世界顶级奢华手表品牌'
          },
          {
            id: 4,
            name: '卡地亚',
            country: '法国',
            foundedYear: 1847,
            description: '法国著名奢侈品牌'
          },
          {
            id: 5,
            name: '积家',
            country: '瑞士',
            foundedYear: 1833,
            description: '瑞士高级制表品牌'
          }
        ]
      }
    },
    editBrand(brand) {
      this.currentBrand = { ...brand }
      this.showEditDialog = true
    },
    async deleteBrand(id) {
      if (confirm('确定要删除这个品牌吗？')) {
        try {
          // 修复接口路径：使用品牌管理接口
          await this.$http.delete(`/v1/watches/brands/${id}`)
          await this.loadBrands()
        } catch (error) {
          console.error('删除品牌失败:', error)
          alert('删除失败，请重试')
        }
      }
    },
    async saveBrand() {
      try {
        const brandData = {
          ...this.currentBrand,
          type: 'brand'
        }

        if (this.showAddDialog) {
          // 修复接口路径：使用品牌管理接口
          await this.$http.post('/v1/watches/brands', brandData)
        } else {
          // 修复接口路径：使用品牌管理接口
          await this.$http.put(`/v1/watches/brands/${this.currentBrand.id}`, brandData)
        }
        await this.loadBrands()
        this.closeDialogs()
      } catch (error) {
        console.error('保存品牌失败:', error)
        alert('保存失败，请重试')
      }
    },
    closeDialogs() {
      this.showAddDialog = false
      this.showEditDialog = false
      this.currentBrand = {
        id: null,
        name: '',
        country: '',
        foundedYear: null,
        description: ''
      }
    },
    logout() {
      localStorage.removeItem('userToken')
      localStorage.removeItem('userInfo')
      this.$router.push('/login')
    }
  }
}
</script>

<style scoped>
.brand-list {
  min-height: 100vh;
}

.navbar {
  background: white;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
}

.nav-brand h2 {
  color: #333;
  margin: 0;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 20px;
}

.nav-item {
  text-decoration: none;
  color: #666;
  padding: 8px 16px;
  border-radius: 4px;
  transition: all 0.3s;
}

.nav-item:hover,
.nav-item.active {
  color: #667eea;
  background: #f0f2ff;
}

.logout-btn {
  background: #ff4757;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.main-content {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.add-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
}

.search-bar {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.search-input {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.search-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
}

.brand-table {
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  overflow: hidden;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  padding: 15px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.brand-name {
  font-weight: 500;
  color: #667eea;
}

.description {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.actions {
  display: flex;
  gap: 10px;
}

.edit-btn {
  background: #2ed573;
  color: white;
  border: none;
  padding: 5px 15px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
}

.delete-btn {
  background: #ff4757;
  color: white;
  border: none;
  padding: 5px 15px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #666;
}

.reload-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  margin-top: 10px;
}

.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.dialog {
  background: white;
  padding: 30px;
  border-radius: 10px;
  width: 90%;
  max-width: 500px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  color: #333;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-family: inherit;
}

.dialog-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 20px;
}

.cancel-btn {
  background: #ccc;
  color: #333;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
}

.save-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
}
</style>
