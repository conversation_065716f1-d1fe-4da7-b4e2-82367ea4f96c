package com.xbj.test;

import javax.swing.*;
import java.awt.*;

/**
 * <AUTHOR>
 */
public class ScreenWatermark extends J<PERSON>rame {

    public ScreenWatermark() {
        super("Screen Watermark -yy");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(1000, 500);
        setLocationRelativeTo(null);
        setUndecorated(true);
        setBackground(new Color(0, 0, 0, 0));
        setAlwaysOnTop(true);
        setOpacity(0.5f);
    }

    @Override
    public void paint(Graphics g) {
        Graphics2D g2d = (Graphics2D)g;
        Font font = new Font("Arial", Font.PLAIN, 50);
        g2d.setFont(font);
        g2d.setColor(Color.RED);
        g2d.drawString("Screen Watermark -yy", getWidth() - 600, getHeight() - 50);
    }

    public static void main(String[] args) {
        ScreenWatermark watermark = new ScreenWatermark();
        watermark.setVisible(true);
    }
}
