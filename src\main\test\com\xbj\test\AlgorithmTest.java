package com.xbj.test;

import com.example.AggregatedSignature;
import com.example.VerfiableEncryption;
import redis.clients.jedis.Jedis;

import java.security.KeyPair;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * <AUTHOR>
 * @date 2024/1/22 18:21
 **/
public class AlgorithmTest {
    public static void main(String[] args) {
        testHashFieldset();
    }
    public static void test1(){
        try {
            String message = "Message 1";
            // 模拟多个参与者生成签名
            List<byte[]> signatures = new ArrayList<>();
            KeyPair keyPair1 = AggregatedSignature.generateKeyPair();
            byte[] signature1 = AggregatedSignature.sign(message, keyPair1.getPrivate());
            signatures.add(signature1);

            KeyPair keyPair2 = AggregatedSignature.generateKeyPair();
            byte[] signature2 = AggregatedSignature.sign(message, keyPair2.getPrivate());
            signatures.add(signature2);

            // 将多个签名聚合
            byte[] aggregatedSignature = AggregatedSignature.aggregateSignatures(signatures);

            // 模拟验证聚合签名
            boolean isSignatureValid = AggregatedSignature.verifySignature(message, aggregatedSignature, keyPair1.getPublic(), keyPair2.getPublic());
            System.out.println("Aggregated Signature is valid: " + isSignatureValid);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    public static void test2(){
        try {
            // 生成公私钥对
            KeyPair keyPair = VerfiableEncryption.generateKeyPair();
            // 模拟发送方
            String originalMessage = "Hello, World!";
            byte[] encryptedMessage = VerfiableEncryption.encrypt(originalMessage, keyPair.getPublic());

            // 模拟接收方
            String decryptedMessage = VerfiableEncryption.decrypt(encryptedMessage, keyPair.getPrivate());
            System.out.println("Decrypted Message: " + decryptedMessage);

            // 模拟发送方对消息进行数字签名
            byte[] signature = VerfiableEncryption.sign(originalMessage, keyPair.getPrivate());

            // 模拟接收方验证签名
            boolean isSignatureValid = VerfiableEncryption.verifySignature(originalMessage, signature, keyPair.getPublic());
            System.out.println("Signature is valid: " + isSignatureValid);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    public static void test3(){
        String a = "{\"combineField\":\"\",\"combineFlag\":1,\"combineType\":0,\"combineValue\":\"1\",\"createTime\":1713264323801,\"createUser\":\"yangyang\",\"deleteFlag\":0,\"handleAdvise\":\"排查相应终端存储数据内容\",\"id\":1,\"matchType\":1,\"modifyTime\":1713264323801,\"modifyUser\":\"yangyang\",\"reportFlag\":0,\"riskRuleId\":1,\"rules\":\"[{\\\"matchType\\\":\\\"=\\\",\\\"name\\\":\\\"stMedia\\\",\\\"type\\\":\\\"string\\\",\\\"value\\\":\\\"terminal\\\"},{\\\"matchType\\\":\\\"=\\\",\\\"name\\\":\\\"stPosition\\\",\\\"type\\\":\\\"string\\\",\\\"value\\\":\\\"1\\\"},{\\\"matchType\\\":\\\"=\\\",\\\"name\\\":\\\"stDataSecMeasures\\\",\\\"type\\\":\\\"string\\\",\\\"value\\\":\\\"0\\\"}]\",\"targetObjectType\":0}";
        System.out.println(a.length());
    }

    public static void testHashLength() {
        String host = "r-bp1zrbnwicujsd7h6upd.redis.rds.aliyuncs.com"; // Redis服务器主机名
        int port = 6379; // Redis服务器端口号
        String password = "FLZX3000C_ysyhl9t"; // 替换为您的Redis密码

        Jedis jedis = new Jedis(host, port);
        jedis.auth(password); // 验证密码

        // 连接成功后，可以执行其他操作
        System.out.println("Connected to Redis server");
        String key = "your_hash_key"; // 替换为您要查看的Hash结构的key名称
        List<String> hvals = jedis.hvals(key);// 获取Hash结构中所有字段的值

        int totalLength = 0;
        for (String value : hvals) {
            totalLength += value.length(); // 计算每个字段值的长度并累加
        }

        System.out.println("Total length of all values in Hash structure: " + totalLength);
        jedis.close();
    }

    public static void testHashFieldRead() {
        String host = "r-bp1zrbnwicujsd7h6upd.redis.rds.aliyuncs.com"; // Redis服务器主机名
        int port = 6379; // Redis服务器端口号
        String password = "FLZX3000C_ysyhl9t"; // 替换为您的Redis密码

        Jedis jedis = new Jedis(host, port);
        jedis.auth(password); // 验证密码

        // 连接成功后，可以执行其他操作
        System.out.println("Connected to Redis server");
        String key = "sjaqw:test:user1005640680888162";
        int i = generateRandomNumber(1, 2);
        String fieldKey = "field" + i;
        String fieldValue = jedis.hget(key, fieldKey);
        String result = String.format("key:%s,filed_key:%s,field_value:%s", key, fieldKey, fieldValue);
        System.out.println(result);
        jedis.close();
    }

    public static void testHashFieldset() {
        String host = "r-bp1zrbnwicujsd7h6upd.redis.rds.aliyuncs.com"; // Redis服务器主机名
        int port = 6379; // Redis服务器端口号
        String password = "FLZX3000C_ysyhl9t"; // 替换为您的Redis密码

        Jedis jedis = new Jedis(host, port);
        jedis.auth(password); // 验证密码

        // 连接成功后，可以执行其他操作
        System.out.println("Connected to Redis server");
        String key = "sjaqw:test:user1005640680888162";
        String fieldKey = "dsmc:common:interfaceAsset";
        String fieldValue = "$7\"%X#@k+$~%9r>R7&-p6G1$Lu;6 ?;$1,x?C'9@;,'.4&r)_k3No;9d;Cg4W'11j&48(2\":Fk.0:3I{?4*8-\"!Z#\",l>R;#?:$";
        Long hset = jedis.hset(key, fieldKey, fieldValue);
        System.out.println(hset);
        jedis.close();
    }


    public static int generateRandomNumber(int min, int max) {
        Random random = new Random();
        return random.nextInt(max - min + 1) + min;
    }
}
