{"ast": null, "code": "import { createRouter, createWebHistory } from 'vue-router';\nimport Login from '../views/Login.vue';\nimport Dashboard from '../views/Dashboard.vue';\nimport WatchList from '../views/WatchList.vue';\nimport BrandList from '../views/BrandList.vue';\nimport UserProfile from '../views/UserProfile.vue';\nimport Test from '../views/Test.vue';\nconst routes = [{\n  path: '/',\n  redirect: '/login'\n}, {\n  path: '/login',\n  name: 'Login',\n  component: Login\n}, {\n  path: '/dashboard',\n  name: 'Dashboard',\n  component: Dashboard,\n  meta: {\n    requiresAuth: true\n  }\n}, {\n  path: '/watches',\n  name: 'WatchList',\n  component: WatchList,\n  meta: {\n    requiresAuth: true\n  }\n}, {\n  path: '/brands',\n  name: 'BrandList',\n  component: BrandList,\n  meta: {\n    requiresAuth: true\n  }\n}, {\n  path: '/profile',\n  name: 'UserProfile',\n  component: UserProfile,\n  meta: {\n    requiresAuth: true\n  }\n}, {\n  path: '/test',\n  name: 'Test',\n  component: Test\n}];\nconst router = createRouter({\n  history: createWebHistory(process.env.BASE_URL),\n  routes\n});\n\n// 增强的路由守卫\nrouter.beforeEach((to, from, next) => {\n  const token = localStorage.getItem('userToken');\n  const userInfo = localStorage.getItem('userInfo');\n\n  // 检查是否需要认证\n  if (to.meta.requiresAuth) {\n    if (!token || !userInfo) {\n      // 没有token或用户信息，跳转到登录页\n      next('/login');\n      return;\n    }\n\n    // 简单的token格式验证（可以根据实际token格式调整）\n    try {\n      const parsedUserInfo = JSON.parse(userInfo);\n      if (!parsedUserInfo.username && !parsedUserInfo.openid) {\n        // 用户信息格式不正确\n        localStorage.removeItem('userToken');\n        localStorage.removeItem('userInfo');\n        next('/login');\n        return;\n      }\n    } catch (error) {\n      // 用户信息解析失败\n      localStorage.removeItem('userToken');\n      localStorage.removeItem('userInfo');\n      next('/login');\n      return;\n    }\n  }\n\n  // 如果已登录用户访问登录页，重定向到首页\n  if (to.path === '/login' && token && userInfo) {\n    next('/dashboard');\n    return;\n  }\n  next();\n});\nexport default router;", "map": {"version": 3, "names": ["createRouter", "createWebHistory", "<PERSON><PERSON>", "Dashboard", "WatchList", "BrandList", "UserProfile", "Test", "routes", "path", "redirect", "name", "component", "meta", "requiresAuth", "router", "history", "process", "env", "BASE_URL", "beforeEach", "to", "from", "next", "token", "localStorage", "getItem", "userInfo", "parsedUserInfo", "JSON", "parse", "username", "openid", "removeItem", "error"], "sources": ["D:/devSpace/ideaWorkspace/findwatch/findwatch-frontend/src/router/index.js"], "sourcesContent": ["import { createRouter, createWebHistory } from 'vue-router'\nimport Login from '../views/Login.vue'\nimport Dashboard from '../views/Dashboard.vue'\nimport WatchList from '../views/WatchList.vue'\nimport BrandList from '../views/BrandList.vue'\nimport UserProfile from '../views/UserProfile.vue'\nimport Test from '../views/Test.vue'\n\nconst routes = [\n  {\n    path: '/',\n    redirect: '/login'\n  },\n  {\n    path: '/login',\n    name: 'Login',\n    component: Login\n  },\n  {\n    path: '/dashboard',\n    name: 'Dashboard',\n    component: Dashboard,\n    meta: { requiresAuth: true }\n  },\n  {\n    path: '/watches',\n    name: 'WatchList',\n    component: WatchList,\n    meta: { requiresAuth: true }\n  },\n  {\n    path: '/brands',\n    name: 'BrandList',\n    component: BrandList,\n    meta: { requiresAuth: true }\n  },\n  {\n    path: '/profile',\n    name: 'UserProfile',\n    component: UserProfile,\n    meta: { requiresAuth: true }\n  },\n  {\n    path: '/test',\n    name: 'Test',\n    component: Test\n  }\n]\n\nconst router = createRouter({\n  history: createWebHistory(process.env.BASE_URL),\n  routes\n})\n\n// 增强的路由守卫\nrouter.beforeEach((to, from, next) => {\n  const token = localStorage.getItem('userToken')\n  const userInfo = localStorage.getItem('userInfo')\n\n  // 检查是否需要认证\n  if (to.meta.requiresAuth) {\n    if (!token || !userInfo) {\n      // 没有token或用户信息，跳转到登录页\n      next('/login')\n      return\n    }\n\n    // 简单的token格式验证（可以根据实际token格式调整）\n    try {\n      const parsedUserInfo = JSON.parse(userInfo)\n      if (!parsedUserInfo.username && !parsedUserInfo.openid) {\n        // 用户信息格式不正确\n        localStorage.removeItem('userToken')\n        localStorage.removeItem('userInfo')\n        next('/login')\n        return\n      }\n    } catch (error) {\n      // 用户信息解析失败\n      localStorage.removeItem('userToken')\n      localStorage.removeItem('userInfo')\n      next('/login')\n      return\n    }\n  }\n\n  // 如果已登录用户访问登录页，重定向到首页\n  if (to.path === '/login' && token && userInfo) {\n    next('/dashboard')\n    return\n  }\n\n  next()\n})\n\nexport default router\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,gBAAgB,QAAQ,YAAY;AAC3D,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,IAAI,MAAM,mBAAmB;AAEpC,MAAMC,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,GAAG;EACTC,QAAQ,EAAE;AACZ,CAAC,EACD;EACED,IAAI,EAAE,QAAQ;EACdE,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEV;AACb,CAAC,EACD;EACEO,IAAI,EAAE,YAAY;EAClBE,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAET,SAAS;EACpBU,IAAI,EAAE;IAAEC,YAAY,EAAE;EAAK;AAC7B,CAAC,EACD;EACEL,IAAI,EAAE,UAAU;EAChBE,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAER,SAAS;EACpBS,IAAI,EAAE;IAAEC,YAAY,EAAE;EAAK;AAC7B,CAAC,EACD;EACEL,IAAI,EAAE,SAAS;EACfE,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEP,SAAS;EACpBQ,IAAI,EAAE;IAAEC,YAAY,EAAE;EAAK;AAC7B,CAAC,EACD;EACEL,IAAI,EAAE,UAAU;EAChBE,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEN,WAAW;EACtBO,IAAI,EAAE;IAAEC,YAAY,EAAE;EAAK;AAC7B,CAAC,EACD;EACEL,IAAI,EAAE,OAAO;EACbE,IAAI,EAAE,MAAM;EACZC,SAAS,EAAEL;AACb,CAAC,CACF;AAED,MAAMQ,MAAM,GAAGf,YAAY,CAAC;EAC1BgB,OAAO,EAAEf,gBAAgB,CAACgB,OAAO,CAACC,GAAG,CAACC,QAAQ,CAAC;EAC/CX;AACF,CAAC,CAAC;;AAEF;AACAO,MAAM,CAACK,UAAU,CAAC,CAACC,EAAE,EAAEC,IAAI,EAAEC,IAAI,KAAK;EACpC,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;EAC/C,MAAMC,QAAQ,GAAGF,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;;EAEjD;EACA,IAAIL,EAAE,CAACR,IAAI,CAACC,YAAY,EAAE;IACxB,IAAI,CAACU,KAAK,IAAI,CAACG,QAAQ,EAAE;MACvB;MACAJ,IAAI,CAAC,QAAQ,CAAC;MACd;IACF;;IAEA;IACA,IAAI;MACF,MAAMK,cAAc,GAAGC,IAAI,CAACC,KAAK,CAACH,QAAQ,CAAC;MAC3C,IAAI,CAACC,cAAc,CAACG,QAAQ,IAAI,CAACH,cAAc,CAACI,MAAM,EAAE;QACtD;QACAP,YAAY,CAACQ,UAAU,CAAC,WAAW,CAAC;QACpCR,YAAY,CAACQ,UAAU,CAAC,UAAU,CAAC;QACnCV,IAAI,CAAC,QAAQ,CAAC;QACd;MACF;IACF,CAAC,CAAC,OAAOW,KAAK,EAAE;MACd;MACAT,YAAY,CAACQ,UAAU,CAAC,WAAW,CAAC;MACpCR,YAAY,CAACQ,UAAU,CAAC,UAAU,CAAC;MACnCV,IAAI,CAAC,QAAQ,CAAC;MACd;IACF;EACF;;EAEA;EACA,IAAIF,EAAE,CAACZ,IAAI,KAAK,QAAQ,IAAIe,KAAK,IAAIG,QAAQ,EAAE;IAC7CJ,IAAI,CAAC,YAAY,CAAC;IAClB;EACF;EAEAA,IAAI,CAAC,CAAC;AACR,CAAC,CAAC;AAEF,eAAeR,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}