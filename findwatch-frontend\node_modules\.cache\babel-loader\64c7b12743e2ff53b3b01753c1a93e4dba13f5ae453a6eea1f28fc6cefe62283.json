{"ast": null, "code": "import { createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"dashboard\"\n};\nconst _hoisted_2 = {\n  class: \"navbar\"\n};\nconst _hoisted_3 = {\n  class: \"nav-menu\"\n};\nconst _hoisted_4 = {\n  class: \"main-content\"\n};\nconst _hoisted_5 = {\n  class: \"stats-grid\"\n};\nconst _hoisted_6 = {\n  class: \"stat-card\"\n};\nconst _hoisted_7 = {\n  class: \"stat-info\"\n};\nconst _hoisted_8 = {\n  class: \"stat-card\"\n};\nconst _hoisted_9 = {\n  class: \"stat-info\"\n};\nconst _hoisted_10 = {\n  class: \"stat-card\"\n};\nconst _hoisted_11 = {\n  class: \"stat-info\"\n};\nconst _hoisted_12 = {\n  class: \"stat-card\"\n};\nconst _hoisted_13 = {\n  class: \"stat-info\"\n};\nconst _hoisted_14 = {\n  class: \"quick-actions\"\n};\nconst _hoisted_15 = {\n  class: \"action-grid\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_router_link = _resolveComponent(\"router-link\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"nav\", _hoisted_2, [_cache[5] || (_cache[5] = _createElementVNode(\"div\", {\n    class: \"nav-brand\"\n  }, [_createElementVNode(\"h2\", null, \"寻表记\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_router_link, {\n    to: \"/dashboard\",\n    class: \"nav-item active\"\n  }, {\n    default: _withCtx(() => _cache[1] || (_cache[1] = [_createTextVNode(\"首页\")])),\n    _: 1 /* STABLE */,\n    __: [1]\n  }), _createVNode(_component_router_link, {\n    to: \"/watches\",\n    class: \"nav-item\"\n  }, {\n    default: _withCtx(() => _cache[2] || (_cache[2] = [_createTextVNode(\"手表管理\")])),\n    _: 1 /* STABLE */,\n    __: [2]\n  }), _createVNode(_component_router_link, {\n    to: \"/brands\",\n    class: \"nav-item\"\n  }, {\n    default: _withCtx(() => _cache[3] || (_cache[3] = [_createTextVNode(\"品牌管理\")])),\n    _: 1 /* STABLE */,\n    __: [3]\n  }), _createVNode(_component_router_link, {\n    to: \"/profile\",\n    class: \"nav-item\"\n  }, {\n    default: _withCtx(() => _cache[4] || (_cache[4] = [_createTextVNode(\"个人信息\")])),\n    _: 1 /* STABLE */,\n    __: [4]\n  }), _createElementVNode(\"button\", {\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.logout && $options.logout(...args)),\n    class: \"logout-btn\"\n  }, \"退出\")])]), _createElementVNode(\"div\", _hoisted_4, [_cache[18] || (_cache[18] = _createElementVNode(\"div\", {\n    class: \"welcome-section\"\n  }, [_createElementVNode(\"h1\", null, \"欢迎使用寻表记管理系统\"), _createElementVNode(\"p\", null, \"专业的手表收藏与管理平台\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_cache[7] || (_cache[7] = _createElementVNode(\"div\", {\n    class: \"stat-icon\"\n  }, \"⌚\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"h3\", null, _toDisplayString($data.stats.totalWatches), 1 /* TEXT */), _cache[6] || (_cache[6] = _createElementVNode(\"p\", null, \"手表总数\", -1 /* CACHED */))])]), _createElementVNode(\"div\", _hoisted_8, [_cache[9] || (_cache[9] = _createElementVNode(\"div\", {\n    class: \"stat-icon\"\n  }, \"🏷️\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"h3\", null, _toDisplayString($data.stats.totalBrands), 1 /* TEXT */), _cache[8] || (_cache[8] = _createElementVNode(\"p\", null, \"品牌数量\", -1 /* CACHED */))])]), _createElementVNode(\"div\", _hoisted_10, [_cache[11] || (_cache[11] = _createElementVNode(\"div\", {\n    class: \"stat-icon\"\n  }, \"👥\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"h3\", null, _toDisplayString($data.stats.totalUsers), 1 /* TEXT */), _cache[10] || (_cache[10] = _createElementVNode(\"p\", null, \"用户数量\", -1 /* CACHED */))])]), _createElementVNode(\"div\", _hoisted_12, [_cache[13] || (_cache[13] = _createElementVNode(\"div\", {\n    class: \"stat-icon\"\n  }, \"💰\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"h3\", null, \"¥\" + _toDisplayString($data.stats.totalValue.toLocaleString()), 1 /* TEXT */), _cache[12] || (_cache[12] = _createElementVNode(\"p\", null, \"总价值\", -1 /* CACHED */))])])]), _createElementVNode(\"div\", _hoisted_14, [_cache[17] || (_cache[17] = _createElementVNode(\"h2\", null, \"快速操作\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_15, [_createVNode(_component_router_link, {\n    to: \"/watches\",\n    class: \"action-card\"\n  }, {\n    default: _withCtx(() => _cache[14] || (_cache[14] = [_createElementVNode(\"div\", {\n      class: \"action-icon\"\n    }, \"⌚\", -1 /* CACHED */), _createElementVNode(\"h3\", null, \"管理手表\", -1 /* CACHED */), _createElementVNode(\"p\", null, \"查看、添加、编辑手表信息\", -1 /* CACHED */)])),\n    _: 1 /* STABLE */,\n    __: [14]\n  }), _createVNode(_component_router_link, {\n    to: \"/brands\",\n    class: \"action-card\"\n  }, {\n    default: _withCtx(() => _cache[15] || (_cache[15] = [_createElementVNode(\"div\", {\n      class: \"action-icon\"\n    }, \"🏷️\", -1 /* CACHED */), _createElementVNode(\"h3\", null, \"管理品牌\", -1 /* CACHED */), _createElementVNode(\"p\", null, \"管理手表品牌信息\", -1 /* CACHED */)])),\n    _: 1 /* STABLE */,\n    __: [15]\n  }), _createVNode(_component_router_link, {\n    to: \"/profile\",\n    class: \"action-card\"\n  }, {\n    default: _withCtx(() => _cache[16] || (_cache[16] = [_createElementVNode(\"div\", {\n      class: \"action-icon\"\n    }, \"👤\", -1 /* CACHED */), _createElementVNode(\"h3\", null, \"个人资料\", -1 /* CACHED */), _createElementVNode(\"p\", null, \"查看和编辑个人信息\", -1 /* CACHED */)])),\n    _: 1 /* STABLE */,\n    __: [16]\n  })])])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_createVNode", "_component_router_link", "to", "_cache", "onClick", "args", "$options", "logout", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_toDisplayString", "$data", "stats", "totalWatches", "_hoisted_8", "_hoisted_9", "totalBrands", "_hoisted_10", "_hoisted_11", "totalUsers", "_hoisted_12", "_hoisted_13", "totalValue", "toLocaleString", "_hoisted_14", "_hoisted_15"], "sources": ["D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\Dashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard\">\n    <nav class=\"navbar\">\n      <div class=\"nav-brand\">\n        <h2>寻表记</h2>\n      </div>\n      <div class=\"nav-menu\">\n        <router-link to=\"/dashboard\" class=\"nav-item active\">首页</router-link>\n        <router-link to=\"/watches\" class=\"nav-item\">手表管理</router-link>\n        <router-link to=\"/brands\" class=\"nav-item\">品牌管理</router-link>\n        <router-link to=\"/profile\" class=\"nav-item\">个人信息</router-link>\n        <button @click=\"logout\" class=\"logout-btn\">退出</button>\n      </div>\n    </nav>\n\n    <div class=\"main-content\">\n      <div class=\"welcome-section\">\n        <h1>欢迎使用寻表记管理系统</h1>\n        <p>专业的手表收藏与管理平台</p>\n      </div>\n\n      <div class=\"stats-grid\">\n        <div class=\"stat-card\">\n          <div class=\"stat-icon\">⌚</div>\n          <div class=\"stat-info\">\n            <h3>{{ stats.totalWatches }}</h3>\n            <p>手表总数</p>\n          </div>\n        </div>\n        \n        <div class=\"stat-card\">\n          <div class=\"stat-icon\">🏷️</div>\n          <div class=\"stat-info\">\n            <h3>{{ stats.totalBrands }}</h3>\n            <p>品牌数量</p>\n          </div>\n        </div>\n        \n        <div class=\"stat-card\">\n          <div class=\"stat-icon\">👥</div>\n          <div class=\"stat-info\">\n            <h3>{{ stats.totalUsers }}</h3>\n            <p>用户数量</p>\n          </div>\n        </div>\n        \n        <div class=\"stat-card\">\n          <div class=\"stat-icon\">💰</div>\n          <div class=\"stat-info\">\n            <h3>¥{{ stats.totalValue.toLocaleString() }}</h3>\n            <p>总价值</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"quick-actions\">\n        <h2>快速操作</h2>\n        <div class=\"action-grid\">\n          <router-link to=\"/watches\" class=\"action-card\">\n            <div class=\"action-icon\">⌚</div>\n            <h3>管理手表</h3>\n            <p>查看、添加、编辑手表信息</p>\n          </router-link>\n          \n          <router-link to=\"/brands\" class=\"action-card\">\n            <div class=\"action-icon\">🏷️</div>\n            <h3>管理品牌</h3>\n            <p>管理手表品牌信息</p>\n          </router-link>\n          \n          <router-link to=\"/profile\" class=\"action-card\">\n            <div class=\"action-icon\">👤</div>\n            <h3>个人资料</h3>\n            <p>查看和编辑个人信息</p>\n          </router-link>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Dashboard',\n  data() {\n    return {\n      stats: {\n        totalWatches: 0,\n        totalBrands: 0,\n        totalUsers: 0,\n        totalValue: 0\n      }\n    }\n  },\n  async mounted() {\n    await this.loadStats()\n  },\n  methods: {\n    async loadStats() {\n      try {\n        // 模拟数据，实际应该从API获取\n        this.stats = {\n          totalWatches: 156,\n          totalBrands: 25,\n          totalUsers: 8,\n          totalValue: 2580000\n        }\n      } catch (error) {\n        console.error('加载统计数据失败:', error)\n      }\n    },\n    logout() {\n      localStorage.removeItem('userToken')\n      localStorage.removeItem('userInfo')\n      this.$router.push('/login')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.dashboard {\n  min-height: 100vh;\n}\n\n.navbar {\n  background: white;\n  padding: 0 20px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  height: 60px;\n}\n\n.nav-brand h2 {\n  color: #333;\n  margin: 0;\n}\n\n.nav-menu {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n}\n\n.nav-item {\n  text-decoration: none;\n  color: #666;\n  padding: 8px 16px;\n  border-radius: 4px;\n  transition: all 0.3s;\n}\n\n.nav-item:hover,\n.nav-item.active {\n  color: #667eea;\n  background: #f0f2ff;\n}\n\n.logout-btn {\n  background: #ff4757;\n  color: white;\n  border: none;\n  padding: 8px 16px;\n  border-radius: 4px;\n  cursor: pointer;\n  transition: background 0.3s;\n}\n\n.logout-btn:hover {\n  background: #ff3742;\n}\n\n.main-content {\n  padding: 40px 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.welcome-section {\n  text-align: center;\n  margin-bottom: 40px;\n}\n\n.welcome-section h1 {\n  color: #333;\n  margin-bottom: 10px;\n  font-size: 32px;\n}\n\n.welcome-section p {\n  color: #666;\n  font-size: 16px;\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n  margin-bottom: 40px;\n}\n\n.stat-card {\n  background: white;\n  padding: 30px;\n  border-radius: 10px;\n  box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n  display: flex;\n  align-items: center;\n  gap: 20px;\n}\n\n.stat-icon {\n  font-size: 40px;\n  width: 60px;\n  height: 60px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: #f0f2ff;\n  border-radius: 50%;\n}\n\n.stat-info h3 {\n  margin: 0 0 5px 0;\n  color: #333;\n  font-size: 24px;\n}\n\n.stat-info p {\n  margin: 0;\n  color: #666;\n  font-size: 14px;\n}\n\n.quick-actions h2 {\n  color: #333;\n  margin-bottom: 20px;\n}\n\n.action-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n}\n\n.action-card {\n  background: white;\n  padding: 30px;\n  border-radius: 10px;\n  box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n  text-decoration: none;\n  color: inherit;\n  transition: transform 0.3s, box-shadow 0.3s;\n  text-align: center;\n}\n\n.action-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 5px 20px rgba(0,0,0,0.15);\n}\n\n.action-icon {\n  font-size: 48px;\n  margin-bottom: 15px;\n}\n\n.action-card h3 {\n  color: #333;\n  margin-bottom: 10px;\n}\n\n.action-card p {\n  color: #666;\n  margin: 0;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAQ;;EAIZA,KAAK,EAAC;AAAU;;EASlBA,KAAK,EAAC;AAAc;;EAMlBA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAW;;EAMnBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAW;;EAMnBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAW;;EAMnBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAW;;EAOrBA,KAAK,EAAC;AAAe;;EAEnBA,KAAK,EAAC;AAAa;;;uBAxD9BC,mBAAA,CA6EM,OA7ENC,UA6EM,GA5EJC,mBAAA,CAWM,OAXNC,UAWM,G,0BAVJD,mBAAA,CAEM;IAFDH,KAAK,EAAC;EAAW,IACpBG,mBAAA,CAAY,YAAR,KAAG,E,qBAETA,mBAAA,CAMM,OANNE,UAMM,GALJC,YAAA,CAAqEC,sBAAA;IAAxDC,EAAE,EAAC,YAAY;IAACR,KAAK,EAAC;;sBAAkB,MAAES,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;MACvDH,YAAA,CAA8DC,sBAAA;IAAjDC,EAAE,EAAC,UAAU;IAACR,KAAK,EAAC;;sBAAW,MAAIS,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;MAChDH,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC,SAAS;IAACR,KAAK,EAAC;;sBAAW,MAAIS,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;MAC/CH,YAAA,CAA8DC,sBAAA;IAAjDC,EAAE,EAAC,UAAU;IAACR,KAAK,EAAC;;sBAAW,MAAIS,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;MAChDN,mBAAA,CAAsD;IAA7CO,OAAK,EAAAD,MAAA,QAAAA,MAAA,UAAAE,IAAA,KAAEC,QAAA,CAAAC,MAAA,IAAAD,QAAA,CAAAC,MAAA,IAAAF,IAAA,CAAM;IAAEX,KAAK,EAAC;KAAa,IAAE,E,KAIjDG,mBAAA,CA8DM,OA9DNW,UA8DM,G,4BA7DJX,mBAAA,CAGM;IAHDH,KAAK,EAAC;EAAiB,IAC1BG,mBAAA,CAAoB,YAAhB,aAAW,GACfA,mBAAA,CAAmB,WAAhB,cAAY,E,qBAGjBA,mBAAA,CAgCM,OAhCNY,UAgCM,GA/BJZ,mBAAA,CAMM,OANNa,UAMM,G,0BALJb,mBAAA,CAA8B;IAAzBH,KAAK,EAAC;EAAW,GAAC,GAAC,qBACxBG,mBAAA,CAGM,OAHNc,UAGM,GAFJd,mBAAA,CAAiC,YAAAe,gBAAA,CAA1BC,KAAA,CAAAC,KAAK,CAACC,YAAY,kB,0BACzBlB,mBAAA,CAAW,WAAR,MAAI,oB,KAIXA,mBAAA,CAMM,OANNmB,UAMM,G,0BALJnB,mBAAA,CAAgC;IAA3BH,KAAK,EAAC;EAAW,GAAC,KAAG,qBAC1BG,mBAAA,CAGM,OAHNoB,UAGM,GAFJpB,mBAAA,CAAgC,YAAAe,gBAAA,CAAzBC,KAAA,CAAAC,KAAK,CAACI,WAAW,kB,0BACxBrB,mBAAA,CAAW,WAAR,MAAI,oB,KAIXA,mBAAA,CAMM,OANNsB,WAMM,G,4BALJtB,mBAAA,CAA+B;IAA1BH,KAAK,EAAC;EAAW,GAAC,IAAE,qBACzBG,mBAAA,CAGM,OAHNuB,WAGM,GAFJvB,mBAAA,CAA+B,YAAAe,gBAAA,CAAxBC,KAAA,CAAAC,KAAK,CAACO,UAAU,kB,4BACvBxB,mBAAA,CAAW,WAAR,MAAI,oB,KAIXA,mBAAA,CAMM,OANNyB,WAMM,G,4BALJzB,mBAAA,CAA+B;IAA1BH,KAAK,EAAC;EAAW,GAAC,IAAE,qBACzBG,mBAAA,CAGM,OAHN0B,WAGM,GAFJ1B,mBAAA,CAAiD,YAA7C,GAAC,GAAAe,gBAAA,CAAGC,KAAA,CAAAC,KAAK,CAACU,UAAU,CAACC,cAAc,oB,4BACvC5B,mBAAA,CAAU,WAAP,KAAG,oB,OAKZA,mBAAA,CAqBM,OArBN6B,WAqBM,G,4BApBJ7B,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CAkBM,OAlBN8B,WAkBM,GAjBJ3B,YAAA,CAIcC,sBAAA;IAJDC,EAAE,EAAC,UAAU;IAACR,KAAK,EAAC;;sBAC/B,MAAgCS,MAAA,SAAAA,MAAA,QAAhCN,mBAAA,CAAgC;MAA3BH,KAAK,EAAC;IAAa,GAAC,GAAC,oBAC1BG,mBAAA,CAAa,YAAT,MAAI,oBACRA,mBAAA,CAAmB,WAAhB,cAAY,mB;;;MAGjBG,YAAA,CAIcC,sBAAA;IAJDC,EAAE,EAAC,SAAS;IAACR,KAAK,EAAC;;sBAC9B,MAAkCS,MAAA,SAAAA,MAAA,QAAlCN,mBAAA,CAAkC;MAA7BH,KAAK,EAAC;IAAa,GAAC,KAAG,oBAC5BG,mBAAA,CAAa,YAAT,MAAI,oBACRA,mBAAA,CAAe,WAAZ,UAAQ,mB;;;MAGbG,YAAA,CAIcC,sBAAA;IAJDC,EAAE,EAAC,UAAU;IAACR,KAAK,EAAC;;sBAC/B,MAAiCS,MAAA,SAAAA,MAAA,QAAjCN,mBAAA,CAAiC;MAA5BH,KAAK,EAAC;IAAa,GAAC,IAAE,oBAC3BG,mBAAA,CAAa,YAAT,MAAI,oBACRA,mBAAA,CAAgB,WAAb,WAAS,mB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}