package com.xbj.util;


import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class JWTUtils {
    private  static final Logger logger = LoggerFactory.getLogger(JWTUtils.class);

    public static String generateHMAC256Token(Map<String,Object> map, String key){
        Algorithm algorithm = Algorithm.HMAC256(key);
        return JWT.create()
//                .withExpiresAt(new Date()) 设置过期的时间
                .withHeader(map)
                .sign(algorithm);
    }

    public static Map<String,Object> verifyTokenGetResult(String token, String key){
        Map<String,Object> res = new HashMap<>(16);
        res.put("isSuccess",false);
        if (!StringUtils.isEmpty(token) && !StringUtils.isEmpty(key)){
            try {
                Algorithm algorithm = Algorithm.HMAC256(key);
                JWTVerifier verifier = JWT.require(algorithm).build();
                DecodedJWT jwt = verifier.verify(token);
                res.put("openid",jwt.getHeaderClaim("openid").asString());
                res.put("session_key",jwt.getHeaderClaim("session_key").asString());
                res.put("isSuccess",true);
            }catch (JWTVerificationException e){
                logger.error("解析token出现问题：{}",e.getMessage());
            }
        }
        return res;
    }

    public static boolean verifyTokenIsRight(String token, String key){
        boolean res = false;
        if (!StringUtils.isEmpty(token) && !StringUtils.isEmpty(key)){
            try {
                Algorithm algorithm = Algorithm.HMAC256(key);
                JWTVerifier verifier = JWT.require(algorithm).build();
                verifier.verify(token);
                res = true;
            }catch (JWTVerificationException e){
                logger.error("验证token出现问题：{}",e.getMessage());
            }
        }
        return res;
    }
    public static void main(String[] args) {
        /*Map<String,Object> map = new HashMap<>();
        map.put("openid","ceshi!!!");
        map.put("session_key","session_key");
        String token = JWTUtils.generateHMAC256Token(map, "lookforwatch");
        Map<String,Object> verfiyToken = JWTUtils.verfiyTokenGetResult(token, "lookforwatch");
        if (Boolean.parseBoolean(verfiyToken.get("isSuccess").toString())){
            System.out.println("验证通过");
        }*/
        /*logger.error("test:{}","test11");
        logger.info("test:{}","test11");
        logger.debug("test:{}","test11");*/
        printOut(76234);
    }

    public static void printOut(int num){
        if (num>=10) {
            printOut(num/10);
        }
        printDigital(num%10);
    }

    public static void printDigital(int num){
        System.out.println("-"+num);
    }

    public static <T> T findMax(T[] arr,Comparator<? super T> cmp){
        int maxIndex = 0;
        for (int i = 0; i < arr.length; i++) {
            if (cmp.compare(arr[i],arr[maxIndex])>0){
                maxIndex = i;
            }
        }
        return arr[maxIndex];
    }
}
class A implements Comparator<String> {

    @Override
    public int compare(String a, String b) {
        return a.compareToIgnoreCase(b);
    }
}
class TestProgram{
    public static void main(String[] args) {
        String dateStr1 = "2021-08-10 10:00:00";
        Date date1 = DateUtil.parse(dateStr1);

        String dateStr2 = "2021-12-16 10:00:00";
        Date date2 = DateUtil.parse(dateStr2);

        //相差一个月，31天
        long betweenDay = DateUtil.between(date1, date2, DateUnit.DAY);
        System.out.println("deadline:"+betweenDay+"天");
    }
}
