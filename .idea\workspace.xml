<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AnalysisUIOptions">
    <option name="GROUP_BY_SEVERITY" value="true" />
  </component>
  <component name="ArtifactsWorkspaceSettings">
    <artifacts-to-build>
      <artifact name="MVCDemo:war" />
    </artifacts-to-build>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="b1c22e2b-3b51-4902-b385-9246f83e11f1" name="Default Changelist" comment="增加若干测试类">
      <change beforePath="$PROJECT_DIR$/README.md" beforeDir="false" afterPath="$PROJECT_DIR$/README.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/logs/findwatch.log" beforeDir="false" afterPath="$PROJECT_DIR$/logs/findwatch.log" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/xbj/controller/AuthController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/xbj/controller/AuthController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application.yml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CodeInsightWorkspaceSettings">
    <option name="optimizeImportsOnTheFly" value="true" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="HTML File" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitSEFilterConfiguration">
    <file-type-list>
      <filtered-out-file-type name="LOCAL_BRANCH" />
      <filtered-out-file-type name="REMOTE_BRANCH" />
      <filtered-out-file-type name="TAG" />
      <filtered-out-file-type name="COMMIT_BY_MESSAGE" />
    </file-type-list>
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/pom.xml" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/src/main/resources/jdbc.properties" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/xbj/controller/LoginController.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/xbj/util/RedisUtil.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/xbj/util/HttpsUtils.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/xbj/entity/WatchCodeExample.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/xbj/util/AliyunSmsUtils.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/src/main/resources/redis.properties" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/xbj/test/StackTest.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/src/main/test/com/xbj/test/Test1.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/src/main/resources/weixin.properties" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="LogFilters">
    <option name="FILTER_ERRORS" value="false" />
    <option name="FILTER_WARNINGS" value="false" />
    <option name="FILTER_INFO" value="false" />
    <option name="FILTER_DEBUG" value="false" />
    <option name="CUSTOM_FILTER" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\devInstall\apache-maven-3.9.7" />
        <option name="localRepository" value="D:\DevSpace\mavenRes" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\devInstall\apache-maven-3.9.7\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedIndex" value="2" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="***************************" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Application.AlgorithmTest.executor": "Run",
    "Application.ClassLoaderTest1.executor": "Run",
    "Application.GenerateDBDocument.executor": "Run",
    "JUnit.TestSomething.testSM4.executor": "Run",
    "Maven.findwatch [clean].executor": "Run",
    "Maven.findwatch [compile].executor": "Run",
    "Maven.findwatch [package].executor": "Run",
    "Maven.findwatch [test].executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "Spring Boot.FindWatchApplication.executor": "Debug",
    "WebServerToolWindowFactoryState": "false",
    "git-widget-placeholder": "feature/convert2springboot-20250707",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/devSpace/ideaWorkspace/findwatch/src/main/resources/lib",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Modules",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.3275862",
    "settings.editor.selected.configurable": "MavenSettings",
    "spring.configuration.checksum": "bdf3bc192b60c35dffe4fb7dc7b49638",
    "ts.external.directory.path": "D:\\devInstall\\IntelliJ IDEA 2024.1.2\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "mysql"
    ]
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\devSpace\ideaWorkspace\findwatch\src\main\resources\lib" />
      <recent name="C:\DevelopmentSpace\ideaWorkspace\MVCDemo\src\main\lib" />
      <recent name="C:\DevelopmentSpace\ideaWorkspace\MVCDemo\src\main\test\com\xbj\test" />
      <recent name="C:\DevelopmentSpace\ideaWorkspace\MVCDemo\src\main\resources" />
      <recent name="C:\DevelopmentSpace\ideaWorkspace\MVCDemo\sql" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.xbj.test" />
    </key>
  </component>
  <component name="RestClientSettings">
    <option name="REQUEST_HISTORY">
      <list>
        <RestClientRequest>
          <option name="httpMethod" value="POST" />
          <option name="urlBase" value="http://localhost:8080/findwatch/login/test" />
          <option name="urlPath" value="" />
          <option name="headers">
            <list>
              <KeyValuePair>
                <option name="key" value="&quot;ssqqwqesssssssss&quot;" />
                <option name="value" value="" />
              </KeyValuePair>
            </list>
          </option>
          <option name="textToSend" value="" />
          <option name="filesToSend" value="" />
        </RestClientRequest>
        <RestClientRequest>
          <option name="httpMethod" value="POST" />
          <option name="urlBase" value="http://localhost:8080/findwatch/login/test" />
          <option name="urlPath" value="" />
          <option name="headers">
            <list>
              <KeyValuePair>
                <option name="key" value="&quot;ssqqwqesssss&quot;" />
                <option name="value" value="" />
              </KeyValuePair>
            </list>
          </option>
          <option name="textToSend" value="" />
          <option name="filesToSend" value="" />
        </RestClientRequest>
        <RestClientRequest>
          <option name="httpMethod" value="POST" />
          <option name="urlBase" value="http://localhost:8080/findwatch/user/updateUser" />
          <option name="urlPath" value="" />
          <option name="headers">
            <list>
              <KeyValuePair>
                <option name="key" value="Content-Type" />
                <option name="value" value="application/json" />
              </KeyValuePair>
            </list>
          </option>
          <option name="haveTextToSend" value="true" />
          <option name="textToSend" value="{&#10;  &quot;id&quot;: &quot;qqq&quot;,&#10;  &quot;price&quot;: &quot;sss&quot;,&#10;}" />
          <option name="filesToSend" value="" />
        </RestClientRequest>
        <RestClientRequest>
          <option name="httpMethod" value="POST" />
          <option name="urlBase" value="http://localhost:8080/findwatch/user/updateUser" />
          <option name="urlPath" value="" />
          <option name="headers">
            <list>
              <KeyValuePair>
                <option name="key" value="Content-Type" />
                <option name="value" value="application/json" />
              </KeyValuePair>
            </list>
          </option>
          <option name="haveTextToSend" value="true" />
          <option name="textToSend" value="{&#10;  &quot;id&quot;: &quot;qqq&quot;,&#10;  &quot;price&quot;: &quot;sss&quot;&quot;&#10;}" />
          <option name="filesToSend" value="" />
        </RestClientRequest>
        <RestClientRequest>
          <option name="httpMethod" value="POST" />
          <option name="urlBase" value="http://localhost:8080/findwatch/user/updateUser" />
          <option name="urlPath" value="" />
          <option name="headers">
            <list>
              <KeyValuePair>
                <option name="key" value="Content-Type" />
                <option name="value" value="application/json" />
              </KeyValuePair>
            </list>
          </option>
          <option name="haveTextToSend" value="true" />
          <option name="textToSend" value="{&#10;  &quot;id&quot;: &quot;qqq&quot;,&#10;  &quot;price&quot;: &quot;sss&quot;&#10;}" />
          <option name="filesToSend" value="" />
        </RestClientRequest>
      </list>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.FindWatchApplication">
    <configuration name="AlgorithmTest" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.xbj.test.AlgorithmTest" />
      <module name="findwatch" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.xbj.test.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ClassLoaderTest1" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.xbj.test.ClassLoaderTest1" />
      <module name="findwatch" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.xbj.test.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="DateTest" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.xbj.test.DateTest" />
      <module name="findwatch" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.xbj.test.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="GenerateDBDocument" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.xbj.test.GenerateDBDocument" />
      <module name="findwatch" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.xbj.test.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="TestSomething.testSM4" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="findwatch" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.xbj.test.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.xbj.test" />
      <option name="MAIN_CLASS_NAME" value="com.xbj.test.TestSomething" />
      <option name="METHOD_NAME" value="testSM4" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="FindWatchApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="findwatch" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.xbj.FindWatchApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Tomcat 7.0.96" type="#com.intellij.j2ee.web.tomcat.TomcatRunConfigurationFactory" factoryName="Local" APPLICATION_SERVER_NAME="Tomcat 7.0.96" ALTERNATIVE_JRE_ENABLED="false" nameIsGenerated="true">
      <option name="COMMON_VM_ARGUMENTS" value="-Dfile.encoding=UTF-8" />
      <option name="UPDATING_POLICY" value="restart-server" />
      <deployment>
        <artifact name="MVCDemo:war">
          <settings>
            <option name="CONTEXT_PATH" value="/MVCDemo_war" />
          </settings>
        </artifact>
      </deployment>
      <server-settings>
        <option name="BASE_DIRECTORY_NAME" value="3c260ab2-53c4-4ec8-8031-856be7348cc2" />
      </server-settings>
      <predefined_log_file enabled="true" id="Tomcat" />
      <predefined_log_file enabled="true" id="Tomcat Catalina" />
      <predefined_log_file id="Tomcat Manager" />
      <predefined_log_file id="Tomcat Host Manager" />
      <predefined_log_file id="Tomcat Localhost Access" />
      <RunnerSettings RunnerId="Debug">
        <option name="DEBUG_PORT" value="53169" />
      </RunnerSettings>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Cover">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Debug">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Profile">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Run">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <method v="2">
        <option name="Make" enabled="true" />
        <option name="BuildArtifacts" enabled="true">
          <artifact name="findwatch:war exploded" />
          <artifact name="MVCDemo:war" />
        </option>
      </method>
    </configuration>
    <list>
      <item itemvalue="Application.GenerateDBDocument" />
      <item itemvalue="Application.ClassLoaderTest1" />
      <item itemvalue="Application.AlgorithmTest" />
      <item itemvalue="Application.DateTest" />
      <item itemvalue="JUnit.TestSomething.testSM4" />
      <item itemvalue="Spring Boot.FindWatchApplication" />
      <item itemvalue="Tomcat Server.Tomcat 7.0.96" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Application.GenerateDBDocument" />
        <item itemvalue="Application.AlgorithmTest" />
        <item itemvalue="Application.ClassLoaderTest1" />
        <item itemvalue="JUnit.TestSomething.testSM4" />
        <item itemvalue="Application.DateTest" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SpringBeansView">
    <option name="beanDetailsProportion" value="0.3" />
  </component>
  <component name="SvnConfiguration">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="b1c22e2b-3b51-4902-b385-9246f83e11f1" name="Default Changelist" comment="" />
      <created>1601572033514</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1601572033514</updated>
      <workItem from="1601572035313" duration="671000" />
      <workItem from="1601572836190" duration="119000" />
      <workItem from="1601572967994" duration="20000" />
      <workItem from="1607005997597" duration="71000" />
      <workItem from="1607006248285" duration="74000" />
      <workItem from="1607151995504" duration="643000" />
      <workItem from="1607153189857" duration="7518000" />
      <workItem from="1607167717965" duration="4425000" />
      <workItem from="1607908975485" duration="4058000" />
      <workItem from="1607913780146" duration="2080000" />
      <workItem from="1607917209897" duration="1821000" />
      <workItem from="1607994184913" duration="19000" />
      <workItem from="1610008004094" duration="245000" />
      <workItem from="1610008264882" duration="18000" />
      <workItem from="1610008299858" duration="1506000" />
      <workItem from="1610009902104" duration="1005000" />
      <workItem from="1610011277842" duration="1929000" />
      <workItem from="1610070495540" duration="4467000" />
      <workItem from="1611111655090" duration="111000" />
      <workItem from="1611111786296" duration="89000" />
      <workItem from="1611111894636" duration="88000" />
      <workItem from="1611111999531" duration="226000" />
      <workItem from="1611112243599" duration="1522000" />
      <workItem from="1611113958428" duration="1424000" />
      <workItem from="1611124663958" duration="5502000" />
      <workItem from="1611198021623" duration="14971000" />
      <workItem from="1612336093343" duration="8221000" />
      <workItem from="1612858762496" duration="5090000" />
      <workItem from="1613609030166" duration="252000" />
      <workItem from="1613609300728" duration="3890000" />
      <workItem from="1614153102538" duration="286000" />
      <workItem from="1614153399939" duration="90000" />
      <workItem from="1614153509107" duration="336000" />
      <workItem from="1615880615416" duration="702000" />
      <workItem from="1616048486346" duration="676000" />
      <workItem from="1617257872281" duration="7952000" />
      <workItem from="1617346367837" duration="3853000" />
      <workItem from="1618192776810" duration="1152000" />
      <workItem from="1618541544464" duration="3113000" />
      <workItem from="1624332154576" duration="3455000" />
      <workItem from="1624353669996" duration="1277000" />
      <workItem from="1624409378250" duration="5084000" />
      <workItem from="1628584077007" duration="822000" />
      <workItem from="1632453884198" duration="2665000" />
      <workItem from="1635390001976" duration="2526000" />
      <workItem from="1635919044694" duration="13352000" />
      <workItem from="1635996119670" duration="2956000" />
      <workItem from="1636016125015" duration="3203000" />
      <workItem from="1636600538050" duration="713000" />
      <workItem from="1636601288671" duration="716000" />
      <workItem from="1636609932614" duration="2185000" />
      <workItem from="1636683802252" duration="1101000" />
      <workItem from="1636684945107" duration="108000" />
      <workItem from="1638243089132" duration="2124000" />
      <workItem from="1638262138247" duration="15332000" />
      <workItem from="1638424346478" duration="598000" />
      <workItem from="1638492928360" duration="170000" />
      <workItem from="1638499317876" duration="11954000" />
      <workItem from="1640677969931" duration="9118000" />
      <workItem from="1640912333734" duration="14890000" />
      <workItem from="1641257828474" duration="2399000" />
      <workItem from="1641977305384" duration="1994000" />
      <workItem from="1644484534390" duration="1864000" />
      <workItem from="1644539723007" duration="3846000" />
      <workItem from="1646300470203" duration="220000" />
      <workItem from="1647239641590" duration="514000" />
      <workItem from="1648460242458" duration="1413000" />
      <workItem from="1650764874641" duration="4354000" />
      <workItem from="1653957856610" duration="4930000" />
      <workItem from="1654053884630" duration="1972000" />
      <workItem from="1655799253727" duration="2157000" />
      <workItem from="1655858874995" duration="511000" />
      <workItem from="1655859545356" duration="350000" />
      <workItem from="1655861710159" duration="994000" />
      <workItem from="1657002207482" duration="668000" />
      <workItem from="1657009282052" duration="1378000" />
      <workItem from="1657179109456" duration="30000" />
      <workItem from="1657612696801" duration="4230000" />
      <workItem from="1657764993270" duration="17000" />
      <workItem from="1659334549270" duration="2417000" />
      <workItem from="1659428876439" duration="2146000" />
      <workItem from="1659948240230" duration="14000" />
      <workItem from="1661412626136" duration="696000" />
      <workItem from="1661821739251" duration="423000" />
      <workItem from="1661822219779" duration="5141000" />
      <workItem from="1662357844180" duration="40000" />
      <workItem from="1680744393061" duration="3724000" />
      <workItem from="1681266346446" duration="4767000" />
      <workItem from="1683707521513" duration="1760000" />
      <workItem from="1685004827809" duration="998000" />
      <workItem from="1685693887978" duration="23000" />
      <workItem from="1686131139735" duration="19838000" />
      <workItem from="1687244970730" duration="2716000" />
      <workItem from="1687922209919" duration="6200000" />
      <workItem from="1689325407294" duration="193000" />
      <workItem from="1689326642820" duration="84000" />
      <workItem from="1690875058654" duration="712000" />
      <workItem from="1697771893644" duration="15432000" />
      <workItem from="1698111078488" duration="346000" />
      <workItem from="1698126763904" duration="846000" />
      <workItem from="1703814974163" duration="6880000" />
      <workItem from="1704157279009" duration="1126000" />
      <workItem from="1705918807905" duration="1213000" />
      <workItem from="1705989774577" duration="1959000" />
      <workItem from="1705997833353" duration="601000" />
      <workItem from="1708936127469" duration="1447000" />
      <workItem from="1709523395091" duration="1402000" />
      <workItem from="1709710364277" duration="1020000" />
      <workItem from="1716345555336" duration="9134000" />
      <workItem from="1718158044452" duration="2494000" />
      <workItem from="1718171916899" duration="1094000" />
      <workItem from="1718239815620" duration="1782000" />
      <workItem from="1718439966459" duration="635000" />
      <workItem from="1718702532466" duration="1449000" />
      <workItem from="1718721252010" duration="147000" />
      <workItem from="1718721406576" duration="1619000" />
      <workItem from="1718776259894" duration="105000" />
      <workItem from="1718776405662" duration="1507000" />
      <workItem from="1718778634876" duration="2327000" />
      <workItem from="1718844999542" duration="5708000" />
      <workItem from="1719212496437" duration="1214000" />
      <workItem from="1719284557011" duration="598000" />
      <workItem from="1719285179427" duration="2356000" />
      <workItem from="1719469186616" duration="3392000" />
      <workItem from="1719817782765" duration="4961000" />
      <workItem from="1720241085046" duration="625000" />
      <workItem from="1744617892618" duration="105000" />
      <workItem from="1749520229910" duration="6264000" />
      <workItem from="1751870761318" duration="2967000" />
      <workItem from="1751959007806" duration="2511000" />
    </task>
    <task id="LOCAL-00001" summary="验证码接口修改">
      <created>1607172934549</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1607172934549</updated>
    </task>
    <task id="LOCAL-00002" summary="pom修改">
      <created>1610010181375</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1610010181375</updated>
    </task>
    <task id="LOCAL-00003" summary="RedisUtil 修改">
      <created>1611113688983</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1611113688983</updated>
    </task>
    <task id="LOCAL-00004" summary="sql修改">
      <created>1611230408494</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1611230408494</updated>
    </task>
    <task id="LOCAL-00005" summary="添加修改信息方法">
      <created>1611230436361</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1611230436361</updated>
    </task>
    <task id="LOCAL-00006" summary="jar包版本修改">
      <created>1611541105491</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1611541105491</updated>
    </task>
    <task id="LOCAL-00007" summary="pom change">
      <created>1612343012695</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1612343012695</updated>
    </task>
    <task id="LOCAL-00008" summary="新增数据库备份文件">
      <created>1636601120543</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1636601120543</updated>
    </task>
    <task id="LOCAL-00009" summary="1">
      <created>1636601152122</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1636601152122</updated>
    </task>
    <task id="LOCAL-00010" summary="1">
      <created>1636685035980</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1636685035980</updated>
    </task>
    <task id="LOCAL-00011" summary="日志框架调整">
      <option name="closed" value="true" />
      <created>1703835263962</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1703835263962</updated>
    </task>
    <task id="LOCAL-00012" summary="日志框架调整">
      <option name="closed" value="true" />
      <created>1703835287959</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1703835287959</updated>
    </task>
    <task id="LOCAL-00013" summary="增加若干测试类">
      <option name="closed" value="true" />
      <created>1703835327871</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1703835327871</updated>
    </task>
    <task id="LOCAL-00014" summary="增加若干测试类">
      <option name="closed" value="true" />
      <created>1703835338543</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1703835338543</updated>
    </task>
    <task id="LOCAL-00015" summary="增加若干测试类">
      <option name="closed" value="true" />
      <created>1703835372842</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1703835372842</updated>
    </task>
    <task id="LOCAL-00016" summary="增加若干测试类">
      <option name="closed" value="true" />
      <created>1718721826621</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1718721826621</updated>
    </task>
    <option name="localTasksCounter" value="17" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="SHOW_DIRTY_RECURSIVELY" value="true" />
    <MESSAGE value="验证码接口修改" />
    <MESSAGE value="pom修改" />
    <MESSAGE value="RedisUtil 修改" />
    <MESSAGE value="sql修改" />
    <MESSAGE value="添加修改信息方法" />
    <MESSAGE value="jar包版本修改" />
    <MESSAGE value="pom change" />
    <MESSAGE value="新增数据库备份文件" />
    <MESSAGE value="1" />
    <MESSAGE value="日志框架调整" />
    <MESSAGE value="增加若干测试类" />
    <option name="LAST_COMMIT_MESSAGE" value="增加若干测试类" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$PROJECT_DIR$/../../mavenRes/org/springframework/spring-webmvc/4.3.25.RELEASE/spring-webmvc-4.3.25.RELEASE-sources.jar!/org/springframework/web/servlet/mvc/support/DefaultHandlerExceptionResolver.java</url>
          <line>109</line>
          <option name="timeStamp" value="3" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <pin-to-top-manager>
      <pinned-members>
        <PinnedItemInfo parentTag="com.xbj.learn.Node" memberName="left" />
      </pinned-members>
    </pin-to-top-manager>
    <watches-manager>
      <configuration name="Application">
        <watch expression="threadTree.root" language="JAVA" />
        <watch expression="p" language="JAVA" />
      </configuration>
    </watches-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>