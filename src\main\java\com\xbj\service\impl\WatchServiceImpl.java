package com.xbj.service.impl;

import com.xbj.dao.WatchMapper;
import com.xbj.entity.SearchInfo;
import com.xbj.entity.Watch;
import com.xbj.entity.WatchExample;
import com.xbj.service.WatchService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-12-23 16:01
 */
@Service
public class WatchServiceImpl implements WatchService {

    @Autowired
    private WatchMapper dao;

    public long countByExample(WatchExample example) {
        return dao.countByExample(example);
    }

    public int deleteByExample(WatchExample example) {
        return dao.deleteByExample(example);
    }

    public int deleteByPrimaryKey(String id) {
        return dao.deleteByPrimaryKey(id);
    }

    public int insert(Watch record) {
        return dao.insert(record);
    }

    public int insertSelective(Watch record) {
        return dao.insertSelective(record);
    }

    public List<Watch> selectByExample(WatchExample example) {
        return dao.selectByExample(example);
    }

    public Watch selectByPrimaryKey(String id) {
        return dao.selectByPrimaryKey(id);
    }

    public int updateByExampleSelective(Watch record, WatchExample example) {
        return dao.updateByExampleSelective(record,example);
    }

    public int updateByExample(Watch record, WatchExample example) {
        return dao.updateByExample(record,example );
    }

    public int updateByPrimaryKeySelective(Watch record) {
        return dao.updateByPrimaryKeySelective(record);
    }

    public int updateByPrimaryKey(Watch record) {
        return dao.updateByPrimaryKey(record);
    }

    public List<Watch> selectByCondition(SearchInfo searchInfo) {
        return dao.selectByCondition(searchInfo);
    }

    @Override
    public List<Watch> selectSeriesByBrandName(String brandName) {
        return dao.selectSeriesByBrandName(brandName);
    }
}
