{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nexport default {\n  name: 'WatchList',\n  data() {\n    return {\n      watches: [],\n      searchQuery: '',\n      showAddDialog: false,\n      showEditDialog: false,\n      currentWatch: {\n        id: null,\n        name: '',\n        brand: '',\n        model: '',\n        price: null\n      }\n    };\n  },\n  computed: {\n    filteredWatches() {\n      if (!this.searchQuery) return this.watches;\n      return this.watches.filter(watch => watch.name.toLowerCase().includes(this.searchQuery.toLowerCase()) || watch.brand.toLowerCase().includes(this.searchQuery.toLowerCase()));\n    }\n  },\n  async mounted() {\n    await this.loadWatches();\n  },\n  methods: {\n    async loadWatches() {\n      try {\n        const response = await this.$http.get('/watch/list');\n        this.watches = response.data || [];\n      } catch (error) {\n        console.error('加载手表列表失败:', error);\n        // 使用模拟数据\n        this.watches = [{\n          id: 1,\n          name: 'Submariner',\n          brand: 'Rolex',\n          model: '116610LN',\n          price: 65000\n        }, {\n          id: 2,\n          name: 'Speedmaster',\n          brand: 'Omega',\n          model: '311.30.42.30.01.005',\n          price: 35000\n        }];\n      }\n    },\n    editWatch(watch) {\n      this.currentWatch = {\n        ...watch\n      };\n      this.showEditDialog = true;\n    },\n    async deleteWatch(id) {\n      if (confirm('确定要删除这个手表吗？')) {\n        try {\n          await this.$http.delete(`/watch/${id}`);\n          await this.loadWatches();\n        } catch (error) {\n          console.error('删除手表失败:', error);\n          alert('删除失败，请重试');\n        }\n      }\n    },\n    async saveWatch() {\n      try {\n        if (this.showAddDialog) {\n          await this.$http.post('/watch', this.currentWatch);\n        } else {\n          await this.$http.put(`/watch/${this.currentWatch.id}`, this.currentWatch);\n        }\n        await this.loadWatches();\n        this.closeDialogs();\n      } catch (error) {\n        console.error('保存手表失败:', error);\n        alert('保存失败，请重试');\n      }\n    },\n    closeDialogs() {\n      this.showAddDialog = false;\n      this.showEditDialog = false;\n      this.currentWatch = {\n        id: null,\n        name: '',\n        brand: '',\n        model: '',\n        price: null\n      };\n    },\n    logout() {\n      localStorage.removeItem('userToken');\n      localStorage.removeItem('userInfo');\n      this.$router.push('/login');\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "watches", "searchQuery", "showAddDialog", "showEditDialog", "currentWatch", "id", "brand", "model", "price", "computed", "filteredWatches", "filter", "watch", "toLowerCase", "includes", "mounted", "loadWatches", "methods", "response", "$http", "get", "error", "console", "editWatch", "deleteWatch", "confirm", "delete", "alert", "saveWatch", "post", "put", "closeDialogs", "logout", "localStorage", "removeItem", "$router", "push"], "sources": ["D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\WatchList.vue"], "sourcesContent": ["<template>\n  <div class=\"watch-list\">\n    <nav class=\"navbar\">\n      <div class=\"nav-brand\">\n        <h2>寻表记</h2>\n      </div>\n      <div class=\"nav-menu\">\n        <router-link to=\"/dashboard\" class=\"nav-item\">首页</router-link>\n        <router-link to=\"/watches\" class=\"nav-item active\">手表管理</router-link>\n        <router-link to=\"/brands\" class=\"nav-item\">品牌管理</router-link>\n        <router-link to=\"/profile\" class=\"nav-item\">个人信息</router-link>\n        <button @click=\"logout\" class=\"logout-btn\">退出</button>\n      </div>\n    </nav>\n\n    <div class=\"main-content\">\n      <div class=\"page-header\">\n        <h1>手表管理</h1>\n        <button @click=\"showAddDialog = true\" class=\"add-btn\">添加手表</button>\n      </div>\n\n      <div class=\"search-bar\">\n        <input\n          type=\"text\"\n          v-model=\"searchQuery\"\n          placeholder=\"搜索手表名称、品牌...\"\n          class=\"search-input\"\n        />\n        <button @click=\"loadWatches\" class=\"search-btn\">搜索</button>\n      </div>\n\n      <div class=\"watch-grid\" v-if=\"watches.length > 0\">\n        <div v-for=\"watch in filteredWatches\" :key=\"watch.id\" class=\"watch-card\">\n          <div class=\"watch-image\">\n            <img :src=\"watch.image || '/placeholder-watch.jpg'\" :alt=\"watch.name\" />\n          </div>\n          <div class=\"watch-info\">\n            <h3>{{ watch.name }}</h3>\n            <p class=\"brand\">{{ watch.brand }}</p>\n            <p class=\"model\">型号: {{ watch.model }}</p>\n            <p class=\"price\">¥{{ watch.price?.toLocaleString() || '暂无价格' }}</p>\n            <div class=\"watch-actions\">\n              <button @click=\"editWatch(watch)\" class=\"edit-btn\">编辑</button>\n              <button @click=\"deleteWatch(watch.id)\" class=\"delete-btn\">删除</button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div v-else class=\"empty-state\">\n        <p>暂无手表数据</p>\n        <button @click=\"loadWatches\" class=\"reload-btn\">重新加载</button>\n      </div>\n    </div>\n\n    <!-- 添加/编辑对话框 -->\n    <div v-if=\"showAddDialog || showEditDialog\" class=\"dialog-overlay\" @click=\"closeDialogs\">\n      <div class=\"dialog\" @click.stop>\n        <h2>{{ showAddDialog ? '添加手表' : '编辑手表' }}</h2>\n        <form @submit.prevent=\"saveWatch\">\n          <div class=\"form-group\">\n            <label>手表名称</label>\n            <input type=\"text\" v-model=\"currentWatch.name\" required />\n          </div>\n          <div class=\"form-group\">\n            <label>品牌</label>\n            <input type=\"text\" v-model=\"currentWatch.brand\" required />\n          </div>\n          <div class=\"form-group\">\n            <label>型号</label>\n            <input type=\"text\" v-model=\"currentWatch.model\" />\n          </div>\n          <div class=\"form-group\">\n            <label>价格</label>\n            <input type=\"number\" v-model=\"currentWatch.price\" />\n          </div>\n          <div class=\"dialog-actions\">\n            <button type=\"button\" @click=\"closeDialogs\" class=\"cancel-btn\">取消</button>\n            <button type=\"submit\" class=\"save-btn\">保存</button>\n          </div>\n        </form>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'WatchList',\n  data() {\n    return {\n      watches: [],\n      searchQuery: '',\n      showAddDialog: false,\n      showEditDialog: false,\n      currentWatch: {\n        id: null,\n        name: '',\n        brand: '',\n        model: '',\n        price: null\n      }\n    }\n  },\n  computed: {\n    filteredWatches() {\n      if (!this.searchQuery) return this.watches\n      return this.watches.filter(watch =>\n        watch.name.toLowerCase().includes(this.searchQuery.toLowerCase()) ||\n        watch.brand.toLowerCase().includes(this.searchQuery.toLowerCase())\n      )\n    }\n  },\n  async mounted() {\n    await this.loadWatches()\n  },\n  methods: {\n    async loadWatches() {\n      try {\n        const response = await this.$http.get('/watch/list')\n        this.watches = response.data || []\n      } catch (error) {\n        console.error('加载手表列表失败:', error)\n        // 使用模拟数据\n        this.watches = [\n          {\n            id: 1,\n            name: 'Submariner',\n            brand: 'Rolex',\n            model: '116610LN',\n            price: 65000\n          },\n          {\n            id: 2,\n            name: 'Speedmaster',\n            brand: 'Omega',\n            model: '311.30.42.30.01.005',\n            price: 35000\n          }\n        ]\n      }\n    },\n    editWatch(watch) {\n      this.currentWatch = { ...watch }\n      this.showEditDialog = true\n    },\n    async deleteWatch(id) {\n      if (confirm('确定要删除这个手表吗？')) {\n        try {\n          await this.$http.delete(`/watch/${id}`)\n          await this.loadWatches()\n        } catch (error) {\n          console.error('删除手表失败:', error)\n          alert('删除失败，请重试')\n        }\n      }\n    },\n    async saveWatch() {\n      try {\n        if (this.showAddDialog) {\n          await this.$http.post('/watch', this.currentWatch)\n        } else {\n          await this.$http.put(`/watch/${this.currentWatch.id}`, this.currentWatch)\n        }\n        await this.loadWatches()\n        this.closeDialogs()\n      } catch (error) {\n        console.error('保存手表失败:', error)\n        alert('保存失败，请重试')\n      }\n    },\n    closeDialogs() {\n      this.showAddDialog = false\n      this.showEditDialog = false\n      this.currentWatch = {\n        id: null,\n        name: '',\n        brand: '',\n        model: '',\n        price: null\n      }\n    },\n    logout() {\n      localStorage.removeItem('userToken')\n      localStorage.removeItem('userInfo')\n      this.$router.push('/login')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.watch-list {\n  min-height: 100vh;\n}\n\n.navbar {\n  background: white;\n  padding: 0 20px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  height: 60px;\n}\n\n.nav-brand h2 {\n  color: #333;\n  margin: 0;\n}\n\n.nav-menu {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n}\n\n.nav-item {\n  text-decoration: none;\n  color: #666;\n  padding: 8px 16px;\n  border-radius: 4px;\n  transition: all 0.3s;\n}\n\n.nav-item:hover,\n.nav-item.active {\n  color: #667eea;\n  background: #f0f2ff;\n}\n\n.logout-btn {\n  background: #ff4757;\n  color: white;\n  border: none;\n  padding: 8px 16px;\n  border-radius: 4px;\n  cursor: pointer;\n}\n\n.main-content {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.add-btn {\n  background: #667eea;\n  color: white;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 5px;\n  cursor: pointer;\n}\n\n.search-bar {\n  display: flex;\n  gap: 10px;\n  margin-bottom: 20px;\n}\n\n.search-input {\n  flex: 1;\n  padding: 10px;\n  border: 1px solid #ddd;\n  border-radius: 5px;\n}\n\n.search-btn {\n  background: #667eea;\n  color: white;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 5px;\n  cursor: pointer;\n}\n\n.watch-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 20px;\n}\n\n.watch-card {\n  background: white;\n  border-radius: 10px;\n  box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n  overflow: hidden;\n}\n\n.watch-image {\n  height: 200px;\n  background: #f5f5f5;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.watch-image img {\n  max-width: 100%;\n  max-height: 100%;\n  object-fit: cover;\n}\n\n.watch-info {\n  padding: 20px;\n}\n\n.watch-info h3 {\n  margin: 0 0 10px 0;\n  color: #333;\n}\n\n.brand {\n  color: #667eea;\n  font-weight: 500;\n  margin: 5px 0;\n}\n\n.model, .price {\n  margin: 5px 0;\n  color: #666;\n}\n\n.watch-actions {\n  display: flex;\n  gap: 10px;\n  margin-top: 15px;\n}\n\n.edit-btn {\n  background: #2ed573;\n  color: white;\n  border: none;\n  padding: 5px 15px;\n  border-radius: 3px;\n  cursor: pointer;\n}\n\n.delete-btn {\n  background: #ff4757;\n  color: white;\n  border: none;\n  padding: 5px 15px;\n  border-radius: 3px;\n  cursor: pointer;\n}\n\n.empty-state {\n  text-align: center;\n  padding: 40px;\n  color: #666;\n}\n\n.reload-btn {\n  background: #667eea;\n  color: white;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 5px;\n  cursor: pointer;\n  margin-top: 10px;\n}\n\n.dialog-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0,0,0,0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n}\n\n.dialog {\n  background: white;\n  padding: 30px;\n  border-radius: 10px;\n  width: 90%;\n  max-width: 500px;\n}\n\n.form-group {\n  margin-bottom: 15px;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 5px;\n  color: #333;\n}\n\n.form-group input {\n  width: 100%;\n  padding: 10px;\n  border: 1px solid #ddd;\n  border-radius: 5px;\n}\n\n.dialog-actions {\n  display: flex;\n  gap: 10px;\n  justify-content: flex-end;\n  margin-top: 20px;\n}\n\n.cancel-btn {\n  background: #ccc;\n  color: #333;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 5px;\n  cursor: pointer;\n}\n\n.save-btn {\n  background: #667eea;\n  color: white;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 5px;\n  cursor: pointer;\n}\n</style>\n"], "mappings": ";;;AAuFA,eAAe;EACbA,IAAI,EAAE,WAAW;EACjBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,OAAO,EAAE,EAAE;MACXC,WAAW,EAAE,EAAE;MACfC,aAAa,EAAE,KAAK;MACpBC,cAAc,EAAE,KAAK;MACrBC,YAAY,EAAE;QACZC,EAAE,EAAE,IAAI;QACRP,IAAI,EAAE,EAAE;QACRQ,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE;MACT;IACF;EACF,CAAC;EACDC,QAAQ,EAAE;IACRC,eAAeA,CAAA,EAAG;MAChB,IAAI,CAAC,IAAI,CAACT,WAAW,EAAE,OAAO,IAAI,CAACD,OAAM;MACzC,OAAO,IAAI,CAACA,OAAO,CAACW,MAAM,CAACC,KAAI,IAC7BA,KAAK,CAACd,IAAI,CAACe,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,IAAI,CAACb,WAAW,CAACY,WAAW,CAAC,CAAC,KAChED,KAAK,CAACN,KAAK,CAACO,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,IAAI,CAACb,WAAW,CAACY,WAAW,CAAC,CAAC,CACnE;IACF;EACF,CAAC;EACD,MAAME,OAAOA,CAAA,EAAG;IACd,MAAM,IAAI,CAACC,WAAW,CAAC;EACzB,CAAC;EACDC,OAAO,EAAE;IACP,MAAMD,WAAWA,CAAA,EAAG;MAClB,IAAI;QACF,MAAME,QAAO,GAAI,MAAM,IAAI,CAACC,KAAK,CAACC,GAAG,CAAC,aAAa;QACnD,IAAI,CAACpB,OAAM,GAAIkB,QAAQ,CAACnB,IAAG,IAAK,EAAC;MACnC,EAAE,OAAOsB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC;QACA,IAAI,CAACrB,OAAM,GAAI,CACb;UACEK,EAAE,EAAE,CAAC;UACLP,IAAI,EAAE,YAAY;UAClBQ,KAAK,EAAE,OAAO;UACdC,KAAK,EAAE,UAAU;UACjBC,KAAK,EAAE;QACT,CAAC,EACD;UACEH,EAAE,EAAE,CAAC;UACLP,IAAI,EAAE,aAAa;UACnBQ,KAAK,EAAE,OAAO;UACdC,KAAK,EAAE,qBAAqB;UAC5BC,KAAK,EAAE;QACT,EACF;MACF;IACF,CAAC;IACDe,SAASA,CAACX,KAAK,EAAE;MACf,IAAI,CAACR,YAAW,GAAI;QAAE,GAAGQ;MAAM;MAC/B,IAAI,CAACT,cAAa,GAAI,IAAG;IAC3B,CAAC;IACD,MAAMqB,WAAWA,CAACnB,EAAE,EAAE;MACpB,IAAIoB,OAAO,CAAC,aAAa,CAAC,EAAE;QAC1B,IAAI;UACF,MAAM,IAAI,CAACN,KAAK,CAACO,MAAM,CAAC,UAAUrB,EAAE,EAAE;UACtC,MAAM,IAAI,CAACW,WAAW,CAAC;QACzB,EAAE,OAAOK,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;UAC9BM,KAAK,CAAC,UAAU;QAClB;MACF;IACF,CAAC;IACD,MAAMC,SAASA,CAAA,EAAG;MAChB,IAAI;QACF,IAAI,IAAI,CAAC1B,aAAa,EAAE;UACtB,MAAM,IAAI,CAACiB,KAAK,CAACU,IAAI,CAAC,QAAQ,EAAE,IAAI,CAACzB,YAAY;QACnD,OAAO;UACL,MAAM,IAAI,CAACe,KAAK,CAACW,GAAG,CAAC,UAAU,IAAI,CAAC1B,YAAY,CAACC,EAAE,EAAE,EAAE,IAAI,CAACD,YAAY;QAC1E;QACA,MAAM,IAAI,CAACY,WAAW,CAAC;QACvB,IAAI,CAACe,YAAY,CAAC;MACpB,EAAE,OAAOV,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;QAC9BM,KAAK,CAAC,UAAU;MAClB;IACF,CAAC;IACDI,YAAYA,CAAA,EAAG;MACb,IAAI,CAAC7B,aAAY,GAAI,KAAI;MACzB,IAAI,CAACC,cAAa,GAAI,KAAI;MAC1B,IAAI,CAACC,YAAW,GAAI;QAClBC,EAAE,EAAE,IAAI;QACRP,IAAI,EAAE,EAAE;QACRQ,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE;MACT;IACF,CAAC;IACDwB,MAAMA,CAAA,EAAG;MACPC,YAAY,CAACC,UAAU,CAAC,WAAW;MACnCD,YAAY,CAACC,UAAU,CAAC,UAAU;MAClC,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,QAAQ;IAC5B;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}