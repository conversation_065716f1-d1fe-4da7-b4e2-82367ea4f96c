{"ast": null, "code": "import { createRouter, createWebHistory } from 'vue-router';\nimport Login from '../views/Login.vue';\nimport Dashboard from '../views/Dashboard.vue';\nimport WatchList from '../views/WatchList.vue';\nimport BrandList from '../views/BrandList.vue';\nimport UserProfile from '../views/UserProfile.vue';\nconst routes = [{\n  path: '/',\n  redirect: '/login'\n}, {\n  path: '/login',\n  name: 'Login',\n  component: Login\n}, {\n  path: '/dashboard',\n  name: 'Dashboard',\n  component: Dashboard,\n  meta: {\n    requiresAuth: true\n  }\n}, {\n  path: '/watches',\n  name: 'WatchList',\n  component: WatchList,\n  meta: {\n    requiresAuth: true\n  }\n}, {\n  path: '/brands',\n  name: 'BrandList',\n  component: BrandList,\n  meta: {\n    requiresAuth: true\n  }\n}, {\n  path: '/profile',\n  name: 'UserProfile',\n  component: UserProfile,\n  meta: {\n    requiresAuth: true\n  }\n}];\nconst router = createRouter({\n  history: createWebHistory(process.env.BASE_URL),\n  routes\n});\n\n// 路由守卫\nrouter.beforeEach((to, from, next) => {\n  const isLoggedIn = localStorage.getItem('userToken');\n  if (to.meta.requiresAuth && !isLoggedIn) {\n    next('/login');\n  } else if (to.path === '/login' && isLoggedIn) {\n    next('/dashboard');\n  } else {\n    next();\n  }\n});\nexport default router;", "map": {"version": 3, "names": ["createRouter", "createWebHistory", "<PERSON><PERSON>", "Dashboard", "WatchList", "BrandList", "UserProfile", "routes", "path", "redirect", "name", "component", "meta", "requiresAuth", "router", "history", "process", "env", "BASE_URL", "beforeEach", "to", "from", "next", "isLoggedIn", "localStorage", "getItem"], "sources": ["D:/devSpace/ideaWorkspace/findwatch/findwatch-frontend/src/router/index.js"], "sourcesContent": ["import { createRouter, createWebHistory } from 'vue-router'\nimport Login from '../views/Login.vue'\nimport Dashboard from '../views/Dashboard.vue'\nimport WatchList from '../views/WatchList.vue'\nimport BrandList from '../views/BrandList.vue'\nimport UserProfile from '../views/UserProfile.vue'\n\nconst routes = [\n  {\n    path: '/',\n    redirect: '/login'\n  },\n  {\n    path: '/login',\n    name: 'Login',\n    component: Login\n  },\n  {\n    path: '/dashboard',\n    name: 'Dashboard',\n    component: Dashboard,\n    meta: { requiresAuth: true }\n  },\n  {\n    path: '/watches',\n    name: 'WatchList',\n    component: WatchList,\n    meta: { requiresAuth: true }\n  },\n  {\n    path: '/brands',\n    name: 'BrandList',\n    component: BrandList,\n    meta: { requiresAuth: true }\n  },\n  {\n    path: '/profile',\n    name: 'UserProfile',\n    component: UserProfile,\n    meta: { requiresAuth: true }\n  }\n]\n\nconst router = createRouter({\n  history: createWebHistory(process.env.BASE_URL),\n  routes\n})\n\n// 路由守卫\nrouter.beforeEach((to, from, next) => {\n  const isLoggedIn = localStorage.getItem('userToken')\n  \n  if (to.meta.requiresAuth && !isLoggedIn) {\n    next('/login')\n  } else if (to.path === '/login' && isLoggedIn) {\n    next('/dashboard')\n  } else {\n    next()\n  }\n})\n\nexport default router\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,gBAAgB,QAAQ,YAAY;AAC3D,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,WAAW,MAAM,0BAA0B;AAElD,MAAMC,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,GAAG;EACTC,QAAQ,EAAE;AACZ,CAAC,EACD;EACED,IAAI,EAAE,QAAQ;EACdE,IAAI,EAAE,OAAO;EACbC,SAAS,EAAET;AACb,CAAC,EACD;EACEM,IAAI,EAAE,YAAY;EAClBE,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAER,SAAS;EACpBS,IAAI,EAAE;IAAEC,YAAY,EAAE;EAAK;AAC7B,CAAC,EACD;EACEL,IAAI,EAAE,UAAU;EAChBE,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEP,SAAS;EACpBQ,IAAI,EAAE;IAAEC,YAAY,EAAE;EAAK;AAC7B,CAAC,EACD;EACEL,IAAI,EAAE,SAAS;EACfE,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEN,SAAS;EACpBO,IAAI,EAAE;IAAEC,YAAY,EAAE;EAAK;AAC7B,CAAC,EACD;EACEL,IAAI,EAAE,UAAU;EAChBE,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEL,WAAW;EACtBM,IAAI,EAAE;IAAEC,YAAY,EAAE;EAAK;AAC7B,CAAC,CACF;AAED,MAAMC,MAAM,GAAGd,YAAY,CAAC;EAC1Be,OAAO,EAAEd,gBAAgB,CAACe,OAAO,CAACC,GAAG,CAACC,QAAQ,CAAC;EAC/CX;AACF,CAAC,CAAC;;AAEF;AACAO,MAAM,CAACK,UAAU,CAAC,CAACC,EAAE,EAAEC,IAAI,EAAEC,IAAI,KAAK;EACpC,MAAMC,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;EAEpD,IAAIL,EAAE,CAACR,IAAI,CAACC,YAAY,IAAI,CAACU,UAAU,EAAE;IACvCD,IAAI,CAAC,QAAQ,CAAC;EAChB,CAAC,MAAM,IAAIF,EAAE,CAACZ,IAAI,KAAK,QAAQ,IAAIe,UAAU,EAAE;IAC7CD,IAAI,CAAC,YAAY,CAAC;EACpB,CAAC,MAAM;IACLA,IAAI,CAAC,CAAC;EACR;AACF,CAAC,CAAC;AAEF,eAAeR,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}