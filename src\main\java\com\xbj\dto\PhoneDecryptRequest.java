package com.xbj.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotBlank;

/**
 * 手机号解密请求DTO
 */
@Schema(description = "手机号解密请求")
public class PhoneDecryptRequest {

    @Schema(description = "加密数据", required = true)
    @NotBlank(message = "加密数据不能为空")
    private String encryptedData;

    @Schema(description = "初始向量", required = true)
    @NotBlank(message = "初始向量不能为空")
    private String iv;

    public PhoneDecryptRequest() {
    }

    public PhoneDecryptRequest(String encryptedData, String iv) {
        this.encryptedData = encryptedData;
        this.iv = iv;
    }

    public String getEncryptedData() {
        return encryptedData;
    }

    public void setEncryptedData(String encryptedData) {
        this.encryptedData = encryptedData;
    }

    public String getIv() {
        return iv;
    }

    public void setIv(String iv) {
        this.iv = iv;
    }

    @Override
    public String toString() {
        return "PhoneDecryptRequest{" +
                "encryptedData='" + encryptedData + '\'' +
                ", iv='" + iv + '\'' +
                '}';
    }
}
