import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import axios from 'axios'

// 配置axios
axios.defaults.baseURL = 'http://localhost:8083/api'
axios.defaults.timeout = 10000

// 请求拦截器
axios.interceptors.request.use(
  config => {
    // 自动添加token到请求头
    const token = localStorage.getItem('userToken')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
axios.interceptors.response.use(
  response => {
    // 返回后端的ApiResponse数据
    return response.data
  },
  error => {
    console.error('API请求错误:', error)

    // 处理401未授权错误（token过期或无效）
    if (error.response && error.response.status === 401) {
      localStorage.removeItem('userToken')
      localStorage.removeItem('userInfo')
      router.push('/login')
      return Promise.reject(new Error('登录已过期，请重新登录'))
    }

    // 如果后端返回了错误响应，但状态码是200（业务错误）
    if (error.response && error.response.data) {
      return Promise.reject(error.response.data)
    }

    return Promise.reject(error)
  }
)

const app = createApp(App)
app.config.globalProperties.$http = axios
app.use(router)
app.mount('#app')
