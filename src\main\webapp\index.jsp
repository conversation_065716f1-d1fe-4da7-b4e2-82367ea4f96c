<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<html>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme() + "://"
            + request.getServerName() + ":" + request.getServerPort()
            + path + "/";
%>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<script src="<%=basePath%>/static/js/jquery-1.11.3.js"></script>
<script>
    $.ajax({
        type:"POST",
        url:"<%=basePath%>/login/test",
        data:"hello world",
        contentType:"application/json",
        success:function (res) {
            console.log(res);
        }
    })
</script>
<body>
<%--<form action="<%=basePath%>/fileController/upload" enctype="multipart/form-data" method="post" >
    pic:<input type="file" name="file1"><br/>
    <input type="submit" value="submit"/>
</form>--%>
hello 寻表记！

</body>
</html>
