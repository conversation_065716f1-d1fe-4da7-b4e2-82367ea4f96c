package com.xbj.test;

import java.io.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SQLFileRename1 {
    public static void main(String[] args) {
        try {
            // 读取SQL文件，指定字符编码为UTF-8
            File inputFile = new File("E:\\db_model.sql");
            BufferedReader reader = new BufferedReader(new InputStreamReader(new FileInputStream(inputFile), "UTF-8"));

            // 创建新的SQL文件
            File outputFile = new File("E:\\output.sql");
            BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(outputFile), "UTF-8"));

            String line;
            while ((line = reader.readLine()) != null) {
                String modifiedLine;
                if (line.contains("create")){
                    // 使用正则表达式查找并替换表名
                     modifiedLine = replaceTableNames(line);
                }else {
                    // 使用正则表达式查找并替换字段名
                    modifiedLine = replaceColumnNames(line);
                }
                writer.write(modifiedLine);
                writer.newLine();
            }

            reader.close();
            writer.close();

            System.out.println("SQL文件修改完成。修改后的文件已保存为output.sql。");
        } catch (IOException e) {
            e.printStackTrace();
        }
        /*String aa = "(app_ip, path)";
        String s = renameFirstLetterUppercase(aa);
        System.out.println(s);*/
    }

    private static String replaceTableNames(String input) {
        // 匹配表名，例如 "create table t_dsmc_alarm" 中的 "t_dsmc_alarm"
        Pattern pattern = Pattern.compile("(?i)create (table|index) (\\w+)");
        Matcher matcher = pattern.matcher(input);
        StringBuffer output = new StringBuffer();

        while (matcher.find()) {
            String oldTableName = matcher.group(2);
            String newTableName = renameFirstLetterUppercase(oldTableName);
            if (input.contains("table")){
                matcher.appendReplacement(output, "create table " + newTableName);
            }else {
                matcher.appendReplacement(output, "create index " + newTableName);
            }
        }
        matcher.appendTail(output);

        return output.toString();
    }

    private static String replaceColumnNames(String input) {
        String temp = input;
        StringBuffer output = new StringBuffer();
        if (temp.trim().startsWith("engine") || temp.trim().startsWith("collate")|| temp.trim().startsWith("primary")
                || temp.trim().startsWith("comment")  || temp.trim().startsWith("on")|| temp.trim().startsWith("row_format")|| temp.trim().startsWith("definer")){
            if (temp.trim().startsWith("on")){
                //去除首尾空格及“;”
                String s = temp.trim().substring(0,temp.trim().length()-1);
                //根据空格拆分
                String[] strings = s.split("\\s+",3);
                for (int i = 0; i < strings.length; i++) {
                    if (i!=0){
                        strings[i] = renameFirstLetterUppercase(strings[i]);
                    }
                }
                output.append("    "+String.join(" ",strings)+";");
            }else {
                output.append(input);
            }

        }else {
            // 匹配字段名，例如 "uid varchar(32) not null" 中的 "uid"
            Pattern pattern = Pattern.compile("(?i)(\\w+)\\s+");
            Matcher matcher = pattern.matcher(input);
            int i = 0;
            while (matcher.find()) {
                i++;
                if (i<=1){
                    String oldColumnName = matcher.group(1);
                    String newColumnName = renameFirstLetterUppercase(oldColumnName);
                    matcher.appendReplacement(output, newColumnName + " ");
                }else {
                    String oldColumnName = matcher.group(1);
                    matcher.appendReplacement(output, oldColumnName + " ");
                }
            }
            matcher.appendTail(output);
        }

        return output.toString();
    }

    private static String renameFirstLetterUppercase(String name) {
        boolean haskuohao = false;
        StringBuffer result = new StringBuffer();
        if (!name.startsWith("(")){
            String[] strs = name.split("_");
            for (int i1 = 0; i1 < strs.length; i1++) {
                String str = strs[i1];
                if (i1==0){
                    result.append(str).append("_");
                }else {
                    result.append(str.substring(0, 1).toUpperCase()).append(str.substring(1)).append("_");
                }
            }
            /*for (String str : strs) {
               result.append(str.substring(0, 1).toUpperCase()).append(str.substring(1)).append("_");
            }*/
            String substring = result.substring(0, result.length() - 1);
            result.setLength(0);
            result.append(substring);
        }else {
            result.append("(");
            name = name.substring(1, name.length() - 1);
            String[] strings = name.split(", ");
            for (String string : strings) {
                String[] strings1 = string.split("_");
                for (int i = 0; i < strings1.length; i++) {
                    String s = strings1[i];
                    if (i==0){
                        result.append(s).append("_");
                    }else {
                        result.append(s.substring(0, 1).toUpperCase()).append(s.substring(1)).append("_");
                    }
                }
                /*for (String s : strings1) {
                    result.append(s.substring(0, 1).toUpperCase()).append(s.substring(1)).append("_");
                }*/
                String substring = result.substring(0, result.length() - 1);
                result.setLength(0);
                result.append(substring);
                result.append(", ");
            }
            String substring = result.substring(0, result.length() - 2);
            result.setLength(0);
            result.append(substring);
            result.append(")");
        }

        return result.toString();
    }
}
