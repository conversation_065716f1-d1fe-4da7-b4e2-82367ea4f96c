{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nexport default {\n  name: 'BrandList',\n  data() {\n    return {\n      brands: [],\n      searchQuery: '',\n      showAddDialog: false,\n      showEditDialog: false,\n      currentBrand: {\n        id: null,\n        name: '',\n        country: '',\n        foundedYear: null,\n        description: ''\n      }\n    };\n  },\n  computed: {\n    filteredBrands() {\n      if (!this.searchQuery) return this.brands;\n      return this.brands.filter(brand => brand.name.toLowerCase().includes(this.searchQuery.toLowerCase()));\n    }\n  },\n  async mounted() {\n    await this.loadBrands();\n  },\n  methods: {\n    async loadBrands() {\n      try {\n        // 修复接口路径：使用正确的后端接口获取品牌数据\n        const response = await this.$http.get('/v1/watches/brands');\n        this.brands = response.data || [];\n      } catch (error) {\n        console.error('加载品牌列表失败:', error);\n        // 使用模拟数据\n        this.brands = [{\n          id: 1,\n          name: '劳力士',\n          country: '瑞士',\n          foundedYear: 1905,\n          description: '世界著名的奢华手表品牌'\n        }, {\n          id: 2,\n          name: '欧米茄',\n          country: '瑞士',\n          foundedYear: 1848,\n          description: '瑞士著名手表制造商'\n        }, {\n          id: 3,\n          name: '百达翡丽',\n          country: '瑞士',\n          foundedYear: 1839,\n          description: '世界顶级奢华手表品牌'\n        }, {\n          id: 4,\n          name: '卡地亚',\n          country: '法国',\n          foundedYear: 1847,\n          description: '法国著名奢侈品牌'\n        }, {\n          id: 5,\n          name: '积家',\n          country: '瑞士',\n          foundedYear: 1833,\n          description: '瑞士高级制表品牌'\n        }];\n      }\n    },\n    editBrand(brand) {\n      this.currentBrand = {\n        ...brand\n      };\n      this.showEditDialog = true;\n    },\n    async deleteBrand(id) {\n      if (confirm('确定要删除这个品牌吗？')) {\n        try {\n          await this.$http.delete(`/watch-code/${id}`);\n          await this.loadBrands();\n        } catch (error) {\n          console.error('删除品牌失败:', error);\n          alert('删除失败，请重试');\n        }\n      }\n    },\n    async saveBrand() {\n      try {\n        const brandData = {\n          ...this.currentBrand,\n          type: 'brand'\n        };\n        if (this.showAddDialog) {\n          await this.$http.post('/watch-code', brandData);\n        } else {\n          await this.$http.put(`/watch-code/${this.currentBrand.id}`, brandData);\n        }\n        await this.loadBrands();\n        this.closeDialogs();\n      } catch (error) {\n        console.error('保存品牌失败:', error);\n        alert('保存失败，请重试');\n      }\n    },\n    closeDialogs() {\n      this.showAddDialog = false;\n      this.showEditDialog = false;\n      this.currentBrand = {\n        id: null,\n        name: '',\n        country: '',\n        foundedYear: null,\n        description: ''\n      };\n    },\n    logout() {\n      localStorage.removeItem('userToken');\n      localStorage.removeItem('userInfo');\n      this.$router.push('/login');\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "brands", "searchQuery", "showAddDialog", "showEditDialog", "current<PERSON><PERSON>", "id", "country", "foundedYear", "description", "computed", "filteredBrands", "filter", "brand", "toLowerCase", "includes", "mounted", "loadBrands", "methods", "response", "$http", "get", "error", "console", "<PERSON><PERSON><PERSON>", "deleteBrand", "confirm", "delete", "alert", "saveBrand", "brandData", "type", "post", "put", "closeDialogs", "logout", "localStorage", "removeItem", "$router", "push"], "sources": ["D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\BrandList.vue"], "sourcesContent": ["<template>\n  <div class=\"brand-list\">\n    <nav class=\"navbar\">\n      <div class=\"nav-brand\">\n        <h2>寻表记</h2>\n      </div>\n      <div class=\"nav-menu\">\n        <router-link to=\"/dashboard\" class=\"nav-item\">首页</router-link>\n        <router-link to=\"/watches\" class=\"nav-item\">手表管理</router-link>\n        <router-link to=\"/brands\" class=\"nav-item active\">品牌管理</router-link>\n        <router-link to=\"/profile\" class=\"nav-item\">个人信息</router-link>\n        <button @click=\"logout\" class=\"logout-btn\">退出</button>\n      </div>\n    </nav>\n\n    <div class=\"main-content\">\n      <div class=\"page-header\">\n        <h1>品牌管理</h1>\n        <button @click=\"showAddDialog = true\" class=\"add-btn\">添加品牌</button>\n      </div>\n\n      <div class=\"search-bar\">\n        <input\n          type=\"text\"\n          v-model=\"searchQuery\"\n          placeholder=\"搜索品牌名称...\"\n          class=\"search-input\"\n        />\n        <button @click=\"loadBrands\" class=\"search-btn\">搜索</button>\n      </div>\n\n      <div class=\"brand-table\" v-if=\"brands.length > 0\">\n        <table>\n          <thead>\n            <tr>\n              <th>品牌名称</th>\n              <th>国家/地区</th>\n              <th>成立年份</th>\n              <th>描述</th>\n              <th>操作</th>\n            </tr>\n          </thead>\n          <tbody>\n            <tr v-for=\"brand in filteredBrands\" :key=\"brand.id\">\n              <td class=\"brand-name\">{{ brand.name }}</td>\n              <td>{{ brand.country || '-' }}</td>\n              <td>{{ brand.foundedYear || '-' }}</td>\n              <td class=\"description\">{{ brand.description || '-' }}</td>\n              <td class=\"actions\">\n                <button @click=\"editBrand(brand)\" class=\"edit-btn\">编辑</button>\n                <button @click=\"deleteBrand(brand.id)\" class=\"delete-btn\">删除</button>\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n\n      <div v-else class=\"empty-state\">\n        <p>暂无品牌数据</p>\n        <button @click=\"loadBrands\" class=\"reload-btn\">重新加载</button>\n      </div>\n    </div>\n\n    <!-- 添加/编辑对话框 -->\n    <div v-if=\"showAddDialog || showEditDialog\" class=\"dialog-overlay\" @click=\"closeDialogs\">\n      <div class=\"dialog\" @click.stop>\n        <h2>{{ showAddDialog ? '添加品牌' : '编辑品牌' }}</h2>\n        <form @submit.prevent=\"saveBrand\">\n          <div class=\"form-group\">\n            <label>品牌名称</label>\n            <input type=\"text\" v-model=\"currentBrand.name\" required />\n          </div>\n          <div class=\"form-group\">\n            <label>国家/地区</label>\n            <input type=\"text\" v-model=\"currentBrand.country\" />\n          </div>\n          <div class=\"form-group\">\n            <label>成立年份</label>\n            <input type=\"number\" v-model=\"currentBrand.foundedYear\" />\n          </div>\n          <div class=\"form-group\">\n            <label>描述</label>\n            <textarea v-model=\"currentBrand.description\" rows=\"3\"></textarea>\n          </div>\n          <div class=\"dialog-actions\">\n            <button type=\"button\" @click=\"closeDialogs\" class=\"cancel-btn\">取消</button>\n            <button type=\"submit\" class=\"save-btn\">保存</button>\n          </div>\n        </form>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'BrandList',\n  data() {\n    return {\n      brands: [],\n      searchQuery: '',\n      showAddDialog: false,\n      showEditDialog: false,\n      currentBrand: {\n        id: null,\n        name: '',\n        country: '',\n        foundedYear: null,\n        description: ''\n      }\n    }\n  },\n  computed: {\n    filteredBrands() {\n      if (!this.searchQuery) return this.brands\n      return this.brands.filter(brand =>\n        brand.name.toLowerCase().includes(this.searchQuery.toLowerCase())\n      )\n    }\n  },\n  async mounted() {\n    await this.loadBrands()\n  },\n  methods: {\n    async loadBrands() {\n      try {\n        // 修复接口路径：使用正确的后端接口获取品牌数据\n        const response = await this.$http.get('/v1/watches/brands')\n        this.brands = response.data || []\n      } catch (error) {\n        console.error('加载品牌列表失败:', error)\n        // 使用模拟数据\n        this.brands = [\n          {\n            id: 1,\n            name: '劳力士',\n            country: '瑞士',\n            foundedYear: 1905,\n            description: '世界著名的奢华手表品牌'\n          },\n          {\n            id: 2,\n            name: '欧米茄',\n            country: '瑞士',\n            foundedYear: 1848,\n            description: '瑞士著名手表制造商'\n          },\n          {\n            id: 3,\n            name: '百达翡丽',\n            country: '瑞士',\n            foundedYear: 1839,\n            description: '世界顶级奢华手表品牌'\n          },\n          {\n            id: 4,\n            name: '卡地亚',\n            country: '法国',\n            foundedYear: 1847,\n            description: '法国著名奢侈品牌'\n          },\n          {\n            id: 5,\n            name: '积家',\n            country: '瑞士',\n            foundedYear: 1833,\n            description: '瑞士高级制表品牌'\n          }\n        ]\n      }\n    },\n    editBrand(brand) {\n      this.currentBrand = { ...brand }\n      this.showEditDialog = true\n    },\n    async deleteBrand(id) {\n      if (confirm('确定要删除这个品牌吗？')) {\n        try {\n          await this.$http.delete(`/watch-code/${id}`)\n          await this.loadBrands()\n        } catch (error) {\n          console.error('删除品牌失败:', error)\n          alert('删除失败，请重试')\n        }\n      }\n    },\n    async saveBrand() {\n      try {\n        const brandData = {\n          ...this.currentBrand,\n          type: 'brand'\n        }\n        \n        if (this.showAddDialog) {\n          await this.$http.post('/watch-code', brandData)\n        } else {\n          await this.$http.put(`/watch-code/${this.currentBrand.id}`, brandData)\n        }\n        await this.loadBrands()\n        this.closeDialogs()\n      } catch (error) {\n        console.error('保存品牌失败:', error)\n        alert('保存失败，请重试')\n      }\n    },\n    closeDialogs() {\n      this.showAddDialog = false\n      this.showEditDialog = false\n      this.currentBrand = {\n        id: null,\n        name: '',\n        country: '',\n        foundedYear: null,\n        description: ''\n      }\n    },\n    logout() {\n      localStorage.removeItem('userToken')\n      localStorage.removeItem('userInfo')\n      this.$router.push('/login')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.brand-list {\n  min-height: 100vh;\n}\n\n.navbar {\n  background: white;\n  padding: 0 20px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  height: 60px;\n}\n\n.nav-brand h2 {\n  color: #333;\n  margin: 0;\n}\n\n.nav-menu {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n}\n\n.nav-item {\n  text-decoration: none;\n  color: #666;\n  padding: 8px 16px;\n  border-radius: 4px;\n  transition: all 0.3s;\n}\n\n.nav-item:hover,\n.nav-item.active {\n  color: #667eea;\n  background: #f0f2ff;\n}\n\n.logout-btn {\n  background: #ff4757;\n  color: white;\n  border: none;\n  padding: 8px 16px;\n  border-radius: 4px;\n  cursor: pointer;\n}\n\n.main-content {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.add-btn {\n  background: #667eea;\n  color: white;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 5px;\n  cursor: pointer;\n}\n\n.search-bar {\n  display: flex;\n  gap: 10px;\n  margin-bottom: 20px;\n}\n\n.search-input {\n  flex: 1;\n  padding: 10px;\n  border: 1px solid #ddd;\n  border-radius: 5px;\n}\n\n.search-btn {\n  background: #667eea;\n  color: white;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 5px;\n  cursor: pointer;\n}\n\n.brand-table {\n  background: white;\n  border-radius: 10px;\n  box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n  overflow: hidden;\n}\n\ntable {\n  width: 100%;\n  border-collapse: collapse;\n}\n\nth, td {\n  padding: 15px;\n  text-align: left;\n  border-bottom: 1px solid #eee;\n}\n\nth {\n  background: #f8f9fa;\n  font-weight: 600;\n  color: #333;\n}\n\n.brand-name {\n  font-weight: 500;\n  color: #667eea;\n}\n\n.description {\n  max-width: 200px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.actions {\n  display: flex;\n  gap: 10px;\n}\n\n.edit-btn {\n  background: #2ed573;\n  color: white;\n  border: none;\n  padding: 5px 15px;\n  border-radius: 3px;\n  cursor: pointer;\n  font-size: 12px;\n}\n\n.delete-btn {\n  background: #ff4757;\n  color: white;\n  border: none;\n  padding: 5px 15px;\n  border-radius: 3px;\n  cursor: pointer;\n  font-size: 12px;\n}\n\n.empty-state {\n  text-align: center;\n  padding: 40px;\n  color: #666;\n}\n\n.reload-btn {\n  background: #667eea;\n  color: white;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 5px;\n  cursor: pointer;\n  margin-top: 10px;\n}\n\n.dialog-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0,0,0,0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n}\n\n.dialog {\n  background: white;\n  padding: 30px;\n  border-radius: 10px;\n  width: 90%;\n  max-width: 500px;\n}\n\n.form-group {\n  margin-bottom: 15px;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 5px;\n  color: #333;\n}\n\n.form-group input,\n.form-group textarea {\n  width: 100%;\n  padding: 10px;\n  border: 1px solid #ddd;\n  border-radius: 5px;\n  font-family: inherit;\n}\n\n.dialog-actions {\n  display: flex;\n  gap: 10px;\n  justify-content: flex-end;\n  margin-top: 20px;\n}\n\n.cancel-btn {\n  background: #ccc;\n  color: #333;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 5px;\n  cursor: pointer;\n}\n\n.save-btn {\n  background: #667eea;\n  color: white;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 5px;\n  cursor: pointer;\n}\n</style>\n"], "mappings": ";;;AA+FA,eAAe;EACbA,IAAI,EAAE,WAAW;EACjBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,MAAM,EAAE,EAAE;MACVC,WAAW,EAAE,EAAE;MACfC,aAAa,EAAE,KAAK;MACpBC,cAAc,EAAE,KAAK;MACrBC,YAAY,EAAE;QACZC,EAAE,EAAE,IAAI;QACRP,IAAI,EAAE,EAAE;QACRQ,OAAO,EAAE,EAAE;QACXC,WAAW,EAAE,IAAI;QACjBC,WAAW,EAAE;MACf;IACF;EACF,CAAC;EACDC,QAAQ,EAAE;IACRC,cAAcA,CAAA,EAAG;MACf,IAAI,CAAC,IAAI,CAACT,WAAW,EAAE,OAAO,IAAI,CAACD,MAAK;MACxC,OAAO,IAAI,CAACA,MAAM,CAACW,MAAM,CAACC,KAAI,IAC5BA,KAAK,CAACd,IAAI,CAACe,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,IAAI,CAACb,WAAW,CAACY,WAAW,CAAC,CAAC,CAClE;IACF;EACF,CAAC;EACD,MAAME,OAAOA,CAAA,EAAG;IACd,MAAM,IAAI,CAACC,UAAU,CAAC;EACxB,CAAC;EACDC,OAAO,EAAE;IACP,MAAMD,UAAUA,CAAA,EAAG;MACjB,IAAI;QACF;QACA,MAAME,QAAO,GAAI,MAAM,IAAI,CAACC,KAAK,CAACC,GAAG,CAAC,oBAAoB;QAC1D,IAAI,CAACpB,MAAK,GAAIkB,QAAQ,CAACnB,IAAG,IAAK,EAAC;MAClC,EAAE,OAAOsB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC;QACA,IAAI,CAACrB,MAAK,GAAI,CACZ;UACEK,EAAE,EAAE,CAAC;UACLP,IAAI,EAAE,KAAK;UACXQ,OAAO,EAAE,IAAI;UACbC,WAAW,EAAE,IAAI;UACjBC,WAAW,EAAE;QACf,CAAC,EACD;UACEH,EAAE,EAAE,CAAC;UACLP,IAAI,EAAE,KAAK;UACXQ,OAAO,EAAE,IAAI;UACbC,WAAW,EAAE,IAAI;UACjBC,WAAW,EAAE;QACf,CAAC,EACD;UACEH,EAAE,EAAE,CAAC;UACLP,IAAI,EAAE,MAAM;UACZQ,OAAO,EAAE,IAAI;UACbC,WAAW,EAAE,IAAI;UACjBC,WAAW,EAAE;QACf,CAAC,EACD;UACEH,EAAE,EAAE,CAAC;UACLP,IAAI,EAAE,KAAK;UACXQ,OAAO,EAAE,IAAI;UACbC,WAAW,EAAE,IAAI;UACjBC,WAAW,EAAE;QACf,CAAC,EACD;UACEH,EAAE,EAAE,CAAC;UACLP,IAAI,EAAE,IAAI;UACVQ,OAAO,EAAE,IAAI;UACbC,WAAW,EAAE,IAAI;UACjBC,WAAW,EAAE;QACf,EACF;MACF;IACF,CAAC;IACDe,SAASA,CAACX,KAAK,EAAE;MACf,IAAI,CAACR,YAAW,GAAI;QAAE,GAAGQ;MAAM;MAC/B,IAAI,CAACT,cAAa,GAAI,IAAG;IAC3B,CAAC;IACD,MAAMqB,WAAWA,CAACnB,EAAE,EAAE;MACpB,IAAIoB,OAAO,CAAC,aAAa,CAAC,EAAE;QAC1B,IAAI;UACF,MAAM,IAAI,CAACN,KAAK,CAACO,MAAM,CAAC,eAAerB,EAAE,EAAE;UAC3C,MAAM,IAAI,CAACW,UAAU,CAAC;QACxB,EAAE,OAAOK,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;UAC9BM,KAAK,CAAC,UAAU;QAClB;MACF;IACF,CAAC;IACD,MAAMC,SAASA,CAAA,EAAG;MAChB,IAAI;QACF,MAAMC,SAAQ,GAAI;UAChB,GAAG,IAAI,CAACzB,YAAY;UACpB0B,IAAI,EAAE;QACR;QAEA,IAAI,IAAI,CAAC5B,aAAa,EAAE;UACtB,MAAM,IAAI,CAACiB,KAAK,CAACY,IAAI,CAAC,aAAa,EAAEF,SAAS;QAChD,OAAO;UACL,MAAM,IAAI,CAACV,KAAK,CAACa,GAAG,CAAC,eAAe,IAAI,CAAC5B,YAAY,CAACC,EAAE,EAAE,EAAEwB,SAAS;QACvE;QACA,MAAM,IAAI,CAACb,UAAU,CAAC;QACtB,IAAI,CAACiB,YAAY,CAAC;MACpB,EAAE,OAAOZ,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;QAC9BM,KAAK,CAAC,UAAU;MAClB;IACF,CAAC;IACDM,YAAYA,CAAA,EAAG;MACb,IAAI,CAAC/B,aAAY,GAAI,KAAI;MACzB,IAAI,CAACC,cAAa,GAAI,KAAI;MAC1B,IAAI,CAACC,YAAW,GAAI;QAClBC,EAAE,EAAE,IAAI;QACRP,IAAI,EAAE,EAAE;QACRQ,OAAO,EAAE,EAAE;QACXC,WAAW,EAAE,IAAI;QACjBC,WAAW,EAAE;MACf;IACF,CAAC;IACD0B,MAAMA,CAAA,EAAG;MACPC,YAAY,CAACC,UAAU,CAAC,WAAW;MACnCD,YAAY,CAACC,UAAU,CAAC,UAAU;MAClC,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,QAAQ;IAC5B;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}