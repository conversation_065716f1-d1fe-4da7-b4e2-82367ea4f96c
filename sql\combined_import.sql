-- 寻表记数据库完整导入脚本
-- 创建数据库（如果不存在）
USE findwatch;

-- 删除已存在的表（如果存在）
DROP TABLE IF EXISTS watch;
DROP TABLE IF EXISTS watch_code;
DROP TABLE IF EXISTS user;

-- 创建用户表
create table user
(
    id        varchar(40)             not null comment '主键或微信unid'
        primary key,
    openid    varchar(40)             not null,
    unionid   varchar(40)             null,
    nick_name varchar(100) default '' null comment '昵称',
    province  varchar(40)  default '' null comment '省份',
    country   varchar(40)  default '' null comment '国家',
    phone     varchar(20)  default '' null comment '手机号码',
    email     varchar(40)  default '' null comment '邮箱',
    adress    varchar(200) default '' null comment '住址',
    gender    varchar(4)   default '' null comment '性别',
    wx_number varchar(50)  default '' null comment '微信号',
    data1     varchar(255) default '' null comment '备用1',
    data2     varchar(255) default '' null comment '备用2',
    -- 添加传统认证字段
    username  varchar(50)  default '' null comment '用户名',
    password  varchar(255) default '' null comment '密码（加密后）',
    created_at timestamp default CURRENT_TIMESTAMP comment '创建时间',
    updated_at timestamp default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP comment '更新时间'
)
    comment '用户表';

-- 创建手表编码表
create table watch_code
(
    id         varchar(42)             not null comment '主键'
        primary key,
    code       char(2)                 not null comment '值',
    type       varchar(100)            not null comment '类型',
    name       varchar(100)            not null comment '名称',
    type_name  varchar(100) default '' null comment 'type对应名称',
    pic_url    varchar(255) default '' null comment '图片路径',
    start_with varchar(2)   default '' null
);

-- 创建手表表
create table watch
(
    id                   int auto_increment comment '编号'
        primary key,
    brand                varchar(80)  default '' null comment '手表品牌',
    series               varchar(200) default '' null comment '手表系列',
    model                varchar(80)  default '' null comment '手表型号',
    size                 varchar(20)  default '' null comment '手表尺寸',
    price_inland         varchar(20)  default '' null comment '大陆售价',
    core_type            varchar(80)  default '' null comment '机芯类型',
    core_model           varchar(80)  default '' null comment '机芯型号',
    bk_type              varchar(20)  default '' null comment '表壳材质（类型）',
    bd_type              varchar(100) default '' null comment '表带材质（类型）',
    bk_hor_size          varchar(100) default '' null comment '表壳直径',
    bp_color             varchar(100) default '' null comment '表盘颜色',
    ks                   varchar(10)  default '' null comment '款式',
    fb_date              varchar(40)  default '' null comment '发布时间',
    fz_fun               varchar(80)  default '' null comment '复杂功能',
    watch_url            varchar(80)  default '' null comment '图片路径',
    bkou_type            varchar(100) default '' null comment '表扣类型',
    use_hours            varchar(40)  default '' null comment '动力储备',
    water_depth          varchar(10)  default '' null comment '防水深度',
    price_in_hk          varchar(20)  default '' null comment '中国香港售价',
    price_in_italy       varchar(20)  default '' null comment '意大利售价',
    price_in_france      varchar(20)  default '' null comment '法国售价',
    price_in_spain       varchar(20)  default '' null comment '西班牙售价',
    price_in_switzerland varchar(20)  default '' null comment '瑞士售价',
    price_in_germany     varchar(20)  default '' null comment '德国售价',
    price_in_holland     varchar(20)  default '' null comment '荷兰售价'
)
    comment '手表';

-- 插入测试用户数据（包含传统认证用户）
INSERT INTO user (id, openid, unionid, nick_name, province, country, phone, email, adress, gender, wx_number, data1, data2, username, password) VALUES 
('admin-user-001', 'admin-openid', null, '管理员', 'Beijing', 'China', '13800138000', '<EMAIL>', '北京市朝阳区', '男', 'admin_wx', '', '', 'admin', '$2a$10$N.zmdr9k7uOCQb96VdodAOBGEByaWdnNdZH4/OhqJqGilzlHr6vMm');
-- 密码是: 123456 (BCrypt加密后)

-- 插入微信用户数据
INSERT INTO user (id, openid, unionid, nick_name, province, country, phone, email, adress, gender, wx_number, data1, data2) VALUES
('90223bd1-e74c-4cfe-8ca0-0256a4f8e787', 'o_P135KvtvInE7vEcO8aoi1nrFOE', null, '杨阳', 'Jiangsu', 'China', '13276646608', '', '', '男', 'hello', '', '86'),
('e2f4bf75-af66-46b6-9a46-0fee3d3e4e67', 'o_P135GtqAg7ofCHqptAaXnEsz90', null, '胡叔叔', 'Torino', 'Italy', '18852308464', '', '', '男', 'hushushu_', '', '86');

-- 插入手表编码数据
INSERT INTO watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES
('1', '1', 'brand', '劳力士/Rolex', '品牌', 'rolex.jpg', 'L'),
('2', '2', 'brand', '百达翡丽/Patek Philippe', '品牌', 'pp.jpg', 'B'),
('3', '3', 'brand', '爱彼/Audemars Piguet', '品牌', 'ap.jpg', 'A'),
('4', '4', 'brand', '积家/Jaeger-LeCoultre', '品牌', 'jlc.jpg', 'J'),
('5', '5', 'brand', '江诗丹顿/Vacheron Constantin', '品牌', 'vc.jpg', 'J'),
('6', '6', 'brand', '欧米茄/Omega', '品牌', 'omega.jpg', 'O'),
('7', '7', 'brand', '卡地亚/Cartier', '品牌', 'cartier.jpg', 'K'),
('8', '8', 'brand', '万国/IWC', '品牌', 'iwc.jpg', 'W'),
('9', '9', 'brand', '宝珀/Blancpain', '品牌', 'bp.jpg', 'B'),
('10', '10', 'brand', '百年灵/Breitling', '品牌', 'bnl.jpg', 'B'),
('11', '1', 'core_type', '石英', '机芯类型', '', ''),
('12', '2', 'core_type', '自动机械', '机芯类型', '', ''),
('13', '3', 'core_type', '手动机械', '机芯类型', '', ''),
('14', '4', 'core_type', '机械', '机械类型', '', ''),
('15', '1', 'bd_type', '金属', '表带材质', '', ''),
('16', '2', 'bd_type', '陶瓷', '表带材质', '', ''),
('17', '3', 'bd_type', '钛金属', '表带材质', '', ''),
('18', '4', 'bd_type', '仿皮', '表带材质', '', ''),
('19', '5', 'bd_type', '橡胶', '表带材质', '', ''),
('20', '6', 'bd_type', '牛皮', '表带材质', '', ''),
('21', '7', 'bd_type', '鳄鱼皮', '表带材质', '', '');

-- 插入手表数据（示例数据）
INSERT INTO watch (id, brand, series, model, size, price_inland, core_type, core_model, bk_type, bd_type, bk_hor_size, bp_color, ks, fb_date, fz_fun, watch_url, bkou_type, use_hours, water_depth, price_in_hk, price_in_italy, price_in_france, price_in_spain, price_in_switzerland, price_in_germany, price_in_holland) VALUES
(1, '劳力士/Rolex', 'Milgauss', '116400GV', '', '64,800', '自动机械', '劳力士3131型机芯', '钢', '钢', '40mm', '电光蓝色', '男士', '', '', 'm116400gv-0002.png', '摺叠蚝式带扣配5mm易调链节延展系统', '约48小时', '100m', '暂无', '暂无', '暂无', '暂无', '暂无', '暂无', '暂无'),
(2, '劳力士/Rolex', 'Milgauss', '116400GV', '', '64,800', '自动机械', '劳力士3131型机芯', '钢', '钢', '40mm', '黑色', '男士', '', '', 'm116400gv-0001.png', '摺叠蚝式带扣配5mm易调链节延展系统', '约48小时', '100m', '暂无', '暂无', '暂无', '暂无', '暂无', '暂无', '暂无'),
(3, '百达翡丽/Patek Philippe', 'Calatrava', '5196P', '', '280,000', '手动机械', '百达翡丽215 PS机芯', '铂金', '鳄鱼皮', '37mm', '白色', '男士', '2019', '小秒针', 'pp-5196p.png', '针扣', '约44小时', '30m', '暂无', '暂无', '暂无', '暂无', '暂无', '暂无', '暂无'),
(4, '欧米茄/Omega', 'Speedmaster', '311.***********.005', '', '32,800', '手动机械', '欧米茄1861机芯', '钢', '钢', '42mm', '黑色', '男士', '2019', '计时', 'omega-speedmaster.png', '折叠扣', '约48小时', '50m', '暂无', '暂无', '暂无', '暂无', '暂无', '暂无', '暂无'),
(5, '卡地亚/Cartier', 'Tank', 'WSTA0018', '', '45,500', '石英', '卡地亚690机芯', '钢', '皮革', '31mm', '银白色', '女士', '2020', '日期', 'cartier-tank.png', '针扣', '约2年', '30m', '暂无', '暂无', '暂无', '暂无', '暂无', '暂无', '暂无');
