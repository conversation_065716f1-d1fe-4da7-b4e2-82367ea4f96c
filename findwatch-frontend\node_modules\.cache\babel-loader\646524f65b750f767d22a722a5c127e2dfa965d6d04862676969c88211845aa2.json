{"ast": null, "code": "import { createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, toDisplayString as _toDisplayString, vModelText as _vModelText, withDirectives as _withDirectives, withModifiers as _withModifiers, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"user-profile\"\n};\nconst _hoisted_2 = {\n  class: \"navbar\"\n};\nconst _hoisted_3 = {\n  class: \"nav-menu\"\n};\nconst _hoisted_4 = {\n  class: \"main-content\"\n};\nconst _hoisted_5 = {\n  class: \"profile-container\"\n};\nconst _hoisted_6 = {\n  class: \"profile-header\"\n};\nconst _hoisted_7 = {\n  class: \"avatar\"\n};\nconst _hoisted_8 = [\"src\", \"alt\"];\nconst _hoisted_9 = {\n  class: \"user-basic\"\n};\nconst _hoisted_10 = {\n  class: \"user-id\"\n};\nconst _hoisted_11 = {\n  class: \"profile-content\"\n};\nconst _hoisted_12 = {\n  class: \"info-section\"\n};\nconst _hoisted_13 = {\n  class: \"form-row\"\n};\nconst _hoisted_14 = {\n  class: \"form-group\"\n};\nconst _hoisted_15 = {\n  class: \"form-group\"\n};\nconst _hoisted_16 = {\n  class: \"form-row\"\n};\nconst _hoisted_17 = {\n  class: \"form-group\"\n};\nconst _hoisted_18 = {\n  class: \"form-group\"\n};\nconst _hoisted_19 = {\n  class: \"form-row\"\n};\nconst _hoisted_20 = {\n  class: \"form-group\"\n};\nconst _hoisted_21 = {\n  class: \"form-group\"\n};\nconst _hoisted_22 = {\n  class: \"form-actions\"\n};\nconst _hoisted_23 = [\"disabled\"];\nconst _hoisted_24 = {\n  class: \"password-section\"\n};\nconst _hoisted_25 = {\n  class: \"form-group\"\n};\nconst _hoisted_26 = {\n  class: \"form-group\"\n};\nconst _hoisted_27 = {\n  class: \"form-group\"\n};\nconst _hoisted_28 = {\n  class: \"form-actions\"\n};\nconst _hoisted_29 = [\"disabled\"];\nconst _hoisted_30 = {\n  class: \"stats-section\"\n};\nconst _hoisted_31 = {\n  class: \"stats-grid\"\n};\nconst _hoisted_32 = {\n  class: \"stat-item\"\n};\nconst _hoisted_33 = {\n  class: \"stat-number\"\n};\nconst _hoisted_34 = {\n  class: \"stat-item\"\n};\nconst _hoisted_35 = {\n  class: \"stat-number\"\n};\nconst _hoisted_36 = {\n  class: \"stat-item\"\n};\nconst _hoisted_37 = {\n  class: \"stat-number\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_router_link = _resolveComponent(\"router-link\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"nav\", _hoisted_2, [_cache[16] || (_cache[16] = _createElementVNode(\"div\", {\n    class: \"nav-brand\"\n  }, [_createElementVNode(\"h2\", null, \"寻表记\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_router_link, {\n    to: \"/dashboard\",\n    class: \"nav-item\"\n  }, {\n    default: _withCtx(() => _cache[12] || (_cache[12] = [_createTextVNode(\"首页\")])),\n    _: 1 /* STABLE */,\n    __: [12]\n  }), _createVNode(_component_router_link, {\n    to: \"/watches\",\n    class: \"nav-item\"\n  }, {\n    default: _withCtx(() => _cache[13] || (_cache[13] = [_createTextVNode(\"手表管理\")])),\n    _: 1 /* STABLE */,\n    __: [13]\n  }), _createVNode(_component_router_link, {\n    to: \"/brands\",\n    class: \"nav-item\"\n  }, {\n    default: _withCtx(() => _cache[14] || (_cache[14] = [_createTextVNode(\"品牌管理\")])),\n    _: 1 /* STABLE */,\n    __: [14]\n  }), _createVNode(_component_router_link, {\n    to: \"/profile\",\n    class: \"nav-item active\"\n  }, {\n    default: _withCtx(() => _cache[15] || (_cache[15] = [_createTextVNode(\"个人信息\")])),\n    _: 1 /* STABLE */,\n    __: [15]\n  }), _createElementVNode(\"button\", {\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.logout && $options.logout(...args)),\n    class: \"logout-btn\"\n  }, \"退出\")])]), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"img\", {\n    src: $data.userInfo.avatar || '/default-avatar.png',\n    alt: $data.userInfo.nickname\n  }, null, 8 /* PROPS */, _hoisted_8)]), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"h1\", null, _toDisplayString($data.userInfo.nickname || $data.userInfo.username), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_10, \"用户ID: \" + _toDisplayString($data.userInfo.id), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, [_cache[23] || (_cache[23] = _createElementVNode(\"h2\", null, \"基本信息\", -1 /* CACHED */)), _createElementVNode(\"form\", {\n    onSubmit: _cache[7] || (_cache[7] = _withModifiers((...args) => $options.updateProfile && $options.updateProfile(...args), [\"prevent\"])),\n    class: \"profile-form\"\n  }, [_createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, [_cache[17] || (_cache[17] = _createElementVNode(\"label\", null, \"用户名\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $data.userInfo.username = $event),\n    readonly: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.userInfo.username]])]), _createElementVNode(\"div\", _hoisted_15, [_cache[18] || (_cache[18] = _createElementVNode(\"label\", null, \"昵称\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $data.userInfo.nickname = $event)\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.userInfo.nickname]])])]), _createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"div\", _hoisted_17, [_cache[19] || (_cache[19] = _createElementVNode(\"label\", null, \"邮箱\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"email\",\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $data.userInfo.email = $event)\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.userInfo.email]])]), _createElementVNode(\"div\", _hoisted_18, [_cache[20] || (_cache[20] = _createElementVNode(\"label\", null, \"手机号\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"tel\",\n    \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $data.userInfo.phone = $event)\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.userInfo.phone]])])]), _createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, [_cache[21] || (_cache[21] = _createElementVNode(\"label\", null, \"省份\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $data.userInfo.province = $event)\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.userInfo.province]])]), _createElementVNode(\"div\", _hoisted_21, [_cache[22] || (_cache[22] = _createElementVNode(\"label\", null, \"国家\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $data.userInfo.country = $event)\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.userInfo.country]])])]), _createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"button\", {\n    type: \"submit\",\n    class: \"save-btn\",\n    disabled: $data.saving\n  }, _toDisplayString($data.saving ? '保存中...' : '保存修改'), 9 /* TEXT, PROPS */, _hoisted_23)])], 32 /* NEED_HYDRATION */)]), _createElementVNode(\"div\", _hoisted_24, [_cache[27] || (_cache[27] = _createElementVNode(\"h2\", null, \"修改密码\", -1 /* CACHED */)), _createElementVNode(\"form\", {\n    onSubmit: _cache[11] || (_cache[11] = _withModifiers((...args) => $options.changePassword && $options.changePassword(...args), [\"prevent\"])),\n    class: \"password-form\"\n  }, [_createElementVNode(\"div\", _hoisted_25, [_cache[24] || (_cache[24] = _createElementVNode(\"label\", null, \"当前密码\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"password\",\n    \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $data.passwordForm.currentPassword = $event),\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.passwordForm.currentPassword]])]), _createElementVNode(\"div\", _hoisted_26, [_cache[25] || (_cache[25] = _createElementVNode(\"label\", null, \"新密码\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"password\",\n    \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $data.passwordForm.newPassword = $event),\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.passwordForm.newPassword]])]), _createElementVNode(\"div\", _hoisted_27, [_cache[26] || (_cache[26] = _createElementVNode(\"label\", null, \"确认新密码\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"password\",\n    \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $data.passwordForm.confirmPassword = $event),\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.passwordForm.confirmPassword]])]), _createElementVNode(\"div\", _hoisted_28, [_createElementVNode(\"button\", {\n    type: \"submit\",\n    class: \"change-password-btn\",\n    disabled: $data.changingPassword\n  }, _toDisplayString($data.changingPassword ? '修改中...' : '修改密码'), 9 /* TEXT, PROPS */, _hoisted_29)])], 32 /* NEED_HYDRATION */)]), _createElementVNode(\"div\", _hoisted_30, [_cache[31] || (_cache[31] = _createElementVNode(\"h2\", null, \"我的统计\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_31, [_createElementVNode(\"div\", _hoisted_32, [_createElementVNode(\"div\", _hoisted_33, _toDisplayString($data.userStats.watchCount), 1 /* TEXT */), _cache[28] || (_cache[28] = _createElementVNode(\"div\", {\n    class: \"stat-label\"\n  }, \"收藏手表\", -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_34, [_createElementVNode(\"div\", _hoisted_35, _toDisplayString($data.userStats.favoriteCount), 1 /* TEXT */), _cache[29] || (_cache[29] = _createElementVNode(\"div\", {\n    class: \"stat-label\"\n  }, \"关注品牌\", -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_36, [_createElementVNode(\"div\", _hoisted_37, _toDisplayString($data.userStats.loginDays), 1 /* TEXT */), _cache[30] || (_cache[30] = _createElementVNode(\"div\", {\n    class: \"stat-label\"\n  }, \"登录天数\", -1 /* CACHED */))])])])])])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_createVNode", "_component_router_link", "to", "_cache", "onClick", "args", "$options", "logout", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "src", "$data", "userInfo", "avatar", "alt", "nickname", "_hoisted_9", "_toDisplayString", "username", "_hoisted_10", "id", "_hoisted_11", "_hoisted_12", "onSubmit", "_withModifiers", "updateProfile", "_hoisted_13", "_hoisted_14", "type", "$event", "readonly", "_hoisted_15", "_hoisted_16", "_hoisted_17", "email", "_hoisted_18", "phone", "_hoisted_19", "_hoisted_20", "province", "_hoisted_21", "country", "_hoisted_22", "disabled", "saving", "_hoisted_23", "_hoisted_24", "changePassword", "_hoisted_25", "passwordForm", "currentPassword", "required", "_hoisted_26", "newPassword", "_hoisted_27", "confirmPassword", "_hoisted_28", "changingPassword", "_hoisted_29", "_hoisted_30", "_hoisted_31", "_hoisted_32", "_hoisted_33", "userStats", "watchCount", "_hoisted_34", "_hoisted_35", "favoriteCount", "_hoisted_36", "_hoisted_37", "loginDays"], "sources": ["D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\UserProfile.vue"], "sourcesContent": ["<template>\n  <div class=\"user-profile\">\n    <nav class=\"navbar\">\n      <div class=\"nav-brand\">\n        <h2>寻表记</h2>\n      </div>\n      <div class=\"nav-menu\">\n        <router-link to=\"/dashboard\" class=\"nav-item\">首页</router-link>\n        <router-link to=\"/watches\" class=\"nav-item\">手表管理</router-link>\n        <router-link to=\"/brands\" class=\"nav-item\">品牌管理</router-link>\n        <router-link to=\"/profile\" class=\"nav-item active\">个人信息</router-link>\n        <button @click=\"logout\" class=\"logout-btn\">退出</button>\n      </div>\n    </nav>\n\n    <div class=\"main-content\">\n      <div class=\"profile-container\">\n        <div class=\"profile-header\">\n          <div class=\"avatar\">\n            <img :src=\"userInfo.avatar || '/default-avatar.png'\" :alt=\"userInfo.nickname\" />\n          </div>\n          <div class=\"user-basic\">\n            <h1>{{ userInfo.nickname || userInfo.username }}</h1>\n            <p class=\"user-id\">用户ID: {{ userInfo.id }}</p>\n          </div>\n        </div>\n\n        <div class=\"profile-content\">\n          <div class=\"info-section\">\n            <h2>基本信息</h2>\n            <form @submit.prevent=\"updateProfile\" class=\"profile-form\">\n              <div class=\"form-row\">\n                <div class=\"form-group\">\n                  <label>用户名</label>\n                  <input type=\"text\" v-model=\"userInfo.username\" readonly />\n                </div>\n                <div class=\"form-group\">\n                  <label>昵称</label>\n                  <input type=\"text\" v-model=\"userInfo.nickname\" />\n                </div>\n              </div>\n              \n              <div class=\"form-row\">\n                <div class=\"form-group\">\n                  <label>邮箱</label>\n                  <input type=\"email\" v-model=\"userInfo.email\" />\n                </div>\n                <div class=\"form-group\">\n                  <label>手机号</label>\n                  <input type=\"tel\" v-model=\"userInfo.phone\" />\n                </div>\n              </div>\n              \n              <div class=\"form-row\">\n                <div class=\"form-group\">\n                  <label>省份</label>\n                  <input type=\"text\" v-model=\"userInfo.province\" />\n                </div>\n                <div class=\"form-group\">\n                  <label>国家</label>\n                  <input type=\"text\" v-model=\"userInfo.country\" />\n                </div>\n              </div>\n\n              <div class=\"form-actions\">\n                <button type=\"submit\" class=\"save-btn\" :disabled=\"saving\">\n                  {{ saving ? '保存中...' : '保存修改' }}\n                </button>\n              </div>\n            </form>\n          </div>\n\n          <div class=\"password-section\">\n            <h2>修改密码</h2>\n            <form @submit.prevent=\"changePassword\" class=\"password-form\">\n              <div class=\"form-group\">\n                <label>当前密码</label>\n                <input type=\"password\" v-model=\"passwordForm.currentPassword\" required />\n              </div>\n              <div class=\"form-group\">\n                <label>新密码</label>\n                <input type=\"password\" v-model=\"passwordForm.newPassword\" required />\n              </div>\n              <div class=\"form-group\">\n                <label>确认新密码</label>\n                <input type=\"password\" v-model=\"passwordForm.confirmPassword\" required />\n              </div>\n              <div class=\"form-actions\">\n                <button type=\"submit\" class=\"change-password-btn\" :disabled=\"changingPassword\">\n                  {{ changingPassword ? '修改中...' : '修改密码' }}\n                </button>\n              </div>\n            </form>\n          </div>\n\n          <div class=\"stats-section\">\n            <h2>我的统计</h2>\n            <div class=\"stats-grid\">\n              <div class=\"stat-item\">\n                <div class=\"stat-number\">{{ userStats.watchCount }}</div>\n                <div class=\"stat-label\">收藏手表</div>\n              </div>\n              <div class=\"stat-item\">\n                <div class=\"stat-number\">{{ userStats.favoriteCount }}</div>\n                <div class=\"stat-label\">关注品牌</div>\n              </div>\n              <div class=\"stat-item\">\n                <div class=\"stat-number\">{{ userStats.loginDays }}</div>\n                <div class=\"stat-label\">登录天数</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'UserProfile',\n  data() {\n    return {\n      userInfo: {\n        id: null,\n        username: '',\n        nickname: '',\n        email: '',\n        phone: '',\n        province: '',\n        country: '',\n        avatar: ''\n      },\n      passwordForm: {\n        currentPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n      },\n      userStats: {\n        watchCount: 0,\n        favoriteCount: 0,\n        loginDays: 0\n      },\n      saving: false,\n      changingPassword: false\n    }\n  },\n  async mounted() {\n    await this.loadUserInfo()\n    await this.loadUserStats()\n  },\n  methods: {\n    async loadUserInfo() {\n      try {\n        // 从localStorage获取用户信息\n        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')\n        this.userInfo = {\n          id: userInfo.id || 1,\n          username: userInfo.username || 'admin',\n          nickname: userInfo.nickname || '管理员',\n          email: '<EMAIL>',\n          phone: '13800138000',\n          province: '北京市',\n          country: '中国',\n          avatar: ''\n        }\n      } catch (error) {\n        console.error('加载用户信息失败:', error)\n      }\n    },\n    async loadUserStats() {\n      try {\n        // 模拟用户统计数据\n        this.userStats = {\n          watchCount: 25,\n          favoriteCount: 8,\n          loginDays: 156\n        }\n      } catch (error) {\n        console.error('加载用户统计失败:', error)\n      }\n    },\n    async updateProfile() {\n      this.saving = true\n      try {\n        // 这里应该调用API更新用户信息\n        await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用\n        \n        // 更新localStorage中的用户信息\n        localStorage.setItem('userInfo', JSON.stringify(this.userInfo))\n        \n        alert('个人信息更新成功')\n      } catch (error) {\n        console.error('更新个人信息失败:', error)\n        alert('更新失败，请重试')\n      } finally {\n        this.saving = false\n      }\n    },\n    async changePassword() {\n      if (this.passwordForm.newPassword !== this.passwordForm.confirmPassword) {\n        alert('新密码和确认密码不一致')\n        return\n      }\n      \n      if (this.passwordForm.newPassword.length < 6) {\n        alert('新密码长度不能少于6位')\n        return\n      }\n      \n      this.changingPassword = true\n      try {\n        // 这里应该调用API修改密码\n        await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用\n        \n        alert('密码修改成功')\n        this.passwordForm = {\n          currentPassword: '',\n          newPassword: '',\n          confirmPassword: ''\n        }\n      } catch (error) {\n        console.error('修改密码失败:', error)\n        alert('修改密码失败，请重试')\n      } finally {\n        this.changingPassword = false\n      }\n    },\n    logout() {\n      localStorage.removeItem('userToken')\n      localStorage.removeItem('userInfo')\n      this.$router.push('/login')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.user-profile {\n  min-height: 100vh;\n}\n\n.navbar {\n  background: white;\n  padding: 0 20px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  height: 60px;\n}\n\n.nav-brand h2 {\n  color: #333;\n  margin: 0;\n}\n\n.nav-menu {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n}\n\n.nav-item {\n  text-decoration: none;\n  color: #666;\n  padding: 8px 16px;\n  border-radius: 4px;\n  transition: all 0.3s;\n}\n\n.nav-item:hover,\n.nav-item.active {\n  color: #667eea;\n  background: #f0f2ff;\n}\n\n.logout-btn {\n  background: #ff4757;\n  color: white;\n  border: none;\n  padding: 8px 16px;\n  border-radius: 4px;\n  cursor: pointer;\n}\n\n.main-content {\n  padding: 20px;\n  max-width: 1000px;\n  margin: 0 auto;\n}\n\n.profile-container {\n  background: white;\n  border-radius: 10px;\n  box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n  overflow: hidden;\n}\n\n.profile-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 40px;\n  display: flex;\n  align-items: center;\n  gap: 30px;\n}\n\n.avatar {\n  width: 100px;\n  height: 100px;\n  border-radius: 50%;\n  overflow: hidden;\n  background: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.avatar img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.user-basic h1 {\n  margin: 0 0 10px 0;\n  font-size: 28px;\n}\n\n.user-id {\n  margin: 0;\n  opacity: 0.8;\n}\n\n.profile-content {\n  padding: 40px;\n}\n\n.info-section,\n.password-section,\n.stats-section {\n  margin-bottom: 40px;\n}\n\n.info-section h2,\n.password-section h2,\n.stats-section h2 {\n  color: #333;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 2px solid #f0f2ff;\n}\n\n.form-row {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 20px;\n  margin-bottom: 20px;\n}\n\n.form-group {\n  margin-bottom: 15px;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 5px;\n  color: #333;\n  font-weight: 500;\n}\n\n.form-group input {\n  width: 100%;\n  padding: 12px;\n  border: 1px solid #ddd;\n  border-radius: 5px;\n  font-size: 14px;\n  transition: border-color 0.3s;\n}\n\n.form-group input:focus {\n  outline: none;\n  border-color: #667eea;\n}\n\n.form-group input[readonly] {\n  background: #f5f5f5;\n  color: #666;\n}\n\n.form-actions {\n  margin-top: 20px;\n}\n\n.save-btn,\n.change-password-btn {\n  background: #667eea;\n  color: white;\n  border: none;\n  padding: 12px 30px;\n  border-radius: 5px;\n  cursor: pointer;\n  font-size: 14px;\n  transition: background 0.3s;\n}\n\n.save-btn:hover,\n.change-password-btn:hover {\n  background: #5a6fd8;\n}\n\n.save-btn:disabled,\n.change-password-btn:disabled {\n  background: #ccc;\n  cursor: not-allowed;\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n  gap: 20px;\n}\n\n.stat-item {\n  text-align: center;\n  padding: 20px;\n  background: #f8f9fa;\n  border-radius: 10px;\n}\n\n.stat-number {\n  font-size: 32px;\n  font-weight: bold;\n  color: #667eea;\n  margin-bottom: 5px;\n}\n\n.stat-label {\n  color: #666;\n  font-size: 14px;\n}\n\n@media (max-width: 768px) {\n  .form-row {\n    grid-template-columns: 1fr;\n  }\n  \n  .profile-header {\n    flex-direction: column;\n    text-align: center;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAQ;;EAIZA,KAAK,EAAC;AAAU;;EASlBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAQ;;;EAGdA,KAAK,EAAC;AAAY;;EAElBA,KAAK,EAAC;AAAS;;EAIjBA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAc;;EAGhBA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAY;;EAIlBA,KAAK,EAAC;AAAY;;EAMpBA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAY;;EAIlBA,KAAK,EAAC;AAAY;;EAMpBA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAY;;EAIlBA,KAAK,EAAC;AAAY;;EAMpBA,KAAK,EAAC;AAAc;;;EAQxBA,KAAK,EAAC;AAAkB;;EAGpBA,KAAK,EAAC;AAAY;;EAIlBA,KAAK,EAAC;AAAY;;EAIlBA,KAAK,EAAC;AAAY;;EAIlBA,KAAK,EAAC;AAAc;;;EAQxBA,KAAK,EAAC;AAAe;;EAEnBA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAa;;EAGrBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAa;;EAGrBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAa;;;uBA1GtCC,mBAAA,CAkHM,OAlHNC,UAkHM,GAjHJC,mBAAA,CAWM,OAXNC,UAWM,G,4BAVJD,mBAAA,CAEM;IAFDH,KAAK,EAAC;EAAW,IACpBG,mBAAA,CAAY,YAAR,KAAG,E,qBAETA,mBAAA,CAMM,OANNE,UAMM,GALJC,YAAA,CAA8DC,sBAAA;IAAjDC,EAAE,EAAC,YAAY;IAACR,KAAK,EAAC;;sBAAW,MAAES,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;MAChDH,YAAA,CAA8DC,sBAAA;IAAjDC,EAAE,EAAC,UAAU;IAACR,KAAK,EAAC;;sBAAW,MAAIS,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;MAChDH,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC,SAAS;IAACR,KAAK,EAAC;;sBAAW,MAAIS,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;MAC/CH,YAAA,CAAqEC,sBAAA;IAAxDC,EAAE,EAAC,UAAU;IAACR,KAAK,EAAC;;sBAAkB,MAAIS,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;MACvDN,mBAAA,CAAsD;IAA7CO,OAAK,EAAAD,MAAA,QAAAA,MAAA,UAAAE,IAAA,KAAEC,QAAA,CAAAC,MAAA,IAAAD,QAAA,CAAAC,MAAA,IAAAF,IAAA,CAAM;IAAEX,KAAK,EAAC;KAAa,IAAE,E,KAIjDG,mBAAA,CAmGM,OAnGNW,UAmGM,GAlGJX,mBAAA,CAiGM,OAjGNY,UAiGM,GAhGJZ,mBAAA,CAQM,OARNa,UAQM,GAPJb,mBAAA,CAEM,OAFNc,UAEM,GADJd,mBAAA,CAAgF;IAA1Ee,GAAG,EAAEC,KAAA,CAAAC,QAAQ,CAACC,MAAM;IAA4BC,GAAG,EAAEH,KAAA,CAAAC,QAAQ,CAACG;yCAEtEpB,mBAAA,CAGM,OAHNqB,UAGM,GAFJrB,mBAAA,CAAqD,YAAAsB,gBAAA,CAA9CN,KAAA,CAAAC,QAAQ,CAACG,QAAQ,IAAIJ,KAAA,CAAAC,QAAQ,CAACM,QAAQ,kBAC7CvB,mBAAA,CAA8C,KAA9CwB,WAA8C,EAA3B,QAAM,GAAAF,gBAAA,CAAGN,KAAA,CAAAC,QAAQ,CAACQ,EAAE,iB,KAI3CzB,mBAAA,CAqFM,OArFN0B,WAqFM,GApFJ1B,mBAAA,CA0CM,OA1CN2B,WA0CM,G,4BAzCJ3B,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CAuCO;IAvCA4B,QAAM,EAAAtB,MAAA,QAAAA,MAAA,MAAAuB,cAAA,KAAArB,IAAA,KAAUC,QAAA,CAAAqB,aAAA,IAAArB,QAAA,CAAAqB,aAAA,IAAAtB,IAAA,CAAa;IAAEX,KAAK,EAAC;MAC1CG,mBAAA,CASM,OATN+B,WASM,GARJ/B,mBAAA,CAGM,OAHNgC,WAGM,G,4BAFJhC,mBAAA,CAAkB,eAAX,KAAG,qB,gBACVA,mBAAA,CAA0D;IAAnDiC,IAAI,EAAC,MAAM;+DAAUjB,KAAA,CAAAC,QAAQ,CAACM,QAAQ,GAAAW,MAAA;IAAEC,QAAQ,EAAR;iDAAnBnB,KAAA,CAAAC,QAAQ,CAACM,QAAQ,E,KAE/CvB,mBAAA,CAGM,OAHNoC,WAGM,G,4BAFJpC,mBAAA,CAAiB,eAAV,IAAE,qB,gBACTA,mBAAA,CAAiD;IAA1CiC,IAAI,EAAC,MAAM;+DAAUjB,KAAA,CAAAC,QAAQ,CAACG,QAAQ,GAAAc,MAAA;iDAAjBlB,KAAA,CAAAC,QAAQ,CAACG,QAAQ,E,OAIjDpB,mBAAA,CASM,OATNqC,WASM,GARJrC,mBAAA,CAGM,OAHNsC,WAGM,G,4BAFJtC,mBAAA,CAAiB,eAAV,IAAE,qB,gBACTA,mBAAA,CAA+C;IAAxCiC,IAAI,EAAC,OAAO;+DAAUjB,KAAA,CAAAC,QAAQ,CAACsB,KAAK,GAAAL,MAAA;iDAAdlB,KAAA,CAAAC,QAAQ,CAACsB,KAAK,E,KAE7CvC,mBAAA,CAGM,OAHNwC,WAGM,G,4BAFJxC,mBAAA,CAAkB,eAAX,KAAG,qB,gBACVA,mBAAA,CAA6C;IAAtCiC,IAAI,EAAC,KAAK;+DAAUjB,KAAA,CAAAC,QAAQ,CAACwB,KAAK,GAAAP,MAAA;iDAAdlB,KAAA,CAAAC,QAAQ,CAACwB,KAAK,E,OAI7CzC,mBAAA,CASM,OATN0C,WASM,GARJ1C,mBAAA,CAGM,OAHN2C,WAGM,G,4BAFJ3C,mBAAA,CAAiB,eAAV,IAAE,qB,gBACTA,mBAAA,CAAiD;IAA1CiC,IAAI,EAAC,MAAM;+DAAUjB,KAAA,CAAAC,QAAQ,CAAC2B,QAAQ,GAAAV,MAAA;iDAAjBlB,KAAA,CAAAC,QAAQ,CAAC2B,QAAQ,E,KAE/C5C,mBAAA,CAGM,OAHN6C,WAGM,G,4BAFJ7C,mBAAA,CAAiB,eAAV,IAAE,qB,gBACTA,mBAAA,CAAgD;IAAzCiC,IAAI,EAAC,MAAM;+DAAUjB,KAAA,CAAAC,QAAQ,CAAC6B,OAAO,GAAAZ,MAAA;iDAAhBlB,KAAA,CAAAC,QAAQ,CAAC6B,OAAO,E,OAIhD9C,mBAAA,CAIM,OAJN+C,WAIM,GAHJ/C,mBAAA,CAES;IAFDiC,IAAI,EAAC,QAAQ;IAACpC,KAAK,EAAC,UAAU;IAAEmD,QAAQ,EAAEhC,KAAA,CAAAiC;sBAC7CjC,KAAA,CAAAiC,MAAM,4CAAAC,WAAA,E,gCAMjBlD,mBAAA,CAqBM,OArBNmD,WAqBM,G,4BApBJnD,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CAkBO;IAlBA4B,QAAM,EAAAtB,MAAA,SAAAA,MAAA,OAAAuB,cAAA,KAAArB,IAAA,KAAUC,QAAA,CAAA2C,cAAA,IAAA3C,QAAA,CAAA2C,cAAA,IAAA5C,IAAA,CAAc;IAAEX,KAAK,EAAC;MAC3CG,mBAAA,CAGM,OAHNqD,WAGM,G,4BAFJrD,mBAAA,CAAmB,eAAZ,MAAI,qB,gBACXA,mBAAA,CAAyE;IAAlEiC,IAAI,EAAC,UAAU;+DAAUjB,KAAA,CAAAsC,YAAY,CAACC,eAAe,GAAArB,MAAA;IAAEsB,QAAQ,EAAR;iDAA9BxC,KAAA,CAAAsC,YAAY,CAACC,eAAe,E,KAE9DvD,mBAAA,CAGM,OAHNyD,WAGM,G,4BAFJzD,mBAAA,CAAkB,eAAX,KAAG,qB,gBACVA,mBAAA,CAAqE;IAA9DiC,IAAI,EAAC,UAAU;+DAAUjB,KAAA,CAAAsC,YAAY,CAACI,WAAW,GAAAxB,MAAA;IAAEsB,QAAQ,EAAR;iDAA1BxC,KAAA,CAAAsC,YAAY,CAACI,WAAW,E,KAE1D1D,mBAAA,CAGM,OAHN2D,WAGM,G,4BAFJ3D,mBAAA,CAAoB,eAAb,OAAK,qB,gBACZA,mBAAA,CAAyE;IAAlEiC,IAAI,EAAC,UAAU;iEAAUjB,KAAA,CAAAsC,YAAY,CAACM,eAAe,GAAA1B,MAAA;IAAEsB,QAAQ,EAAR;iDAA9BxC,KAAA,CAAAsC,YAAY,CAACM,eAAe,E,KAE9D5D,mBAAA,CAIM,OAJN6D,WAIM,GAHJ7D,mBAAA,CAES;IAFDiC,IAAI,EAAC,QAAQ;IAACpC,KAAK,EAAC,qBAAqB;IAAEmD,QAAQ,EAAEhC,KAAA,CAAA8C;sBACxD9C,KAAA,CAAA8C,gBAAgB,4CAAAC,WAAA,E,gCAM3B/D,mBAAA,CAgBM,OAhBNgE,WAgBM,G,4BAfJhE,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CAaM,OAbNiE,WAaM,GAZJjE,mBAAA,CAGM,OAHNkE,WAGM,GAFJlE,mBAAA,CAAyD,OAAzDmE,WAAyD,EAAA7C,gBAAA,CAA7BN,KAAA,CAAAoD,SAAS,CAACC,UAAU,kB,4BAChDrE,mBAAA,CAAkC;IAA7BH,KAAK,EAAC;EAAY,GAAC,MAAI,oB,GAE9BG,mBAAA,CAGM,OAHNsE,WAGM,GAFJtE,mBAAA,CAA4D,OAA5DuE,WAA4D,EAAAjD,gBAAA,CAAhCN,KAAA,CAAAoD,SAAS,CAACI,aAAa,kB,4BACnDxE,mBAAA,CAAkC;IAA7BH,KAAK,EAAC;EAAY,GAAC,MAAI,oB,GAE9BG,mBAAA,CAGM,OAHNyE,WAGM,GAFJzE,mBAAA,CAAwD,OAAxD0E,WAAwD,EAAApD,gBAAA,CAA5BN,KAAA,CAAAoD,SAAS,CAACO,SAAS,kB,4BAC/C3E,mBAAA,CAAkC;IAA7BH,KAAK,EAAC;EAAY,GAAC,MAAI,oB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}