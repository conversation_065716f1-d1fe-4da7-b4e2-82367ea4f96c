package com.xbj.learn;

public class Node {
    private int data;

    public int getData() {
        return data;
    }

    public void setData(int data) {
        this.data = data;
    }

    private Node right;
    private Node left;
    //左孩子是否为线索，默认false
    private boolean leftIsThread;
    //有孩子是否为线索，默认false
    private boolean rightIsThread;

    public Node(int data){
        this.data = data;
        this.right = null;
        this.left = null;
        this.leftIsThread  = false;
        this.rightIsThread = false;
    }


    public Node getRight() {
        return right;
    }

    public void setRight(Node right) {
        this.right = right;
    }

    public Node getLeft() {
        return left;
    }

    public void setLeft(Node left) {
        this.left = left;
    }

    public boolean isLeftIsThread() {
        return leftIsThread;
    }

    public void setLeftIsThread(boolean leftIsThread) {
        this.leftIsThread = leftIsThread;
    }

    public boolean isRightIsThread() {
        return rightIsThread;
    }

    public void setRightIsThread(boolean rightIsThread) {
        this.rightIsThread = rightIsThread;
    }
}
