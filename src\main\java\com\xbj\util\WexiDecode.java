package com.xbj.util;


import org.apache.commons.codec.binary.Base64;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.*;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.*;
import java.util.Map;

public class WexiDecode {

    public static void main(String[] args) {
        String token = "eyJvcGVuaWQiOiJvX1AxMzVLdnR2SW5FN3ZFY084YW9pMW5yRk9FIiwic2Vzc2lvbl9rZXkiOiJhaGJBdnhzUXhJcEJPVms1QUNDT2VBPT0iLCJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.e30.8Aifw97Ji_A5ecnqmRIvP5HUNSWWV_OHkbM9XrdlQ6c";
        Map<String, Object> map = JWTUtils.verifyTokenGetResult(token, "lookforwatch");
        // String session_key = "FriUrXS84uTdkMNy2Rt+fg==";
        //     String iv = "LhyVWR/urIMAQpwsjEFjdQ==";
        // String encryptData = "8LPMKHpX5qIO9gPzUp6ySxNhmR5tlMj2NDV4wFDWPywADzk6+PsWpArnArROrql4MB8CLKoqBnodnWfWbVYjpQ82Uw51/pxiESA56BA369wXN1J6Wn3zDytuR6keoJLGaTcNkbXJz3qKSprfgEOld0rMiK+JllojV7CEWHj8RYPQlIh0ojzzO/5YJFk0eAGBP3MayMYe3scSbxGBW6Sv7TKVSv5jycoWKVVd+cIAnAadOM/Xt0rktLYOJRz6Ax/CM4nGlKAJssgfg7haWhXvFD8aWfkpiIJSgGBNTN1pWLylz4loZE81mH/cUw1pSw8WwqAwjfnHcd/liu8ssdQZB8NXOZEo7/n8gXllBzFFSWdkaAX5gfUg+WxSmLXnYNryMZrfhjNMsLgGn1IGGHjGBD47v9+whM+52S7zH2O8StZABO+hmHuPl/FbI1zCl/XDJNA+rOYcFUJ3c0+8GhbFOA==";
        String decrypt = decrypt("ahbAvxsQxIpBOVk5ACCOeA==", "c0WX7daSQk+MkRltKJ6o2g==", "Dn73R6rLnKxspPJHG3W16A5I9Pw5KbQ3zzT09CxB4avtr31pZJr4S/ibBUgmpkGsEeeM+9znaZXN5mCTkgvZDt/F9JO3DJRPAebaIYKDAmoCQ73wY5Hre4PHt/s/sv9qo1+E2KNTR8cAEg2ps3B5NGmazlCv9f051LGleL8jYLxxuyXIMtsVyYN15oTF3+5N7i71XfZfF/JsK/eln3YcCQ==");
        System.out.println(decrypt);
    }

    public static String decrypt(String session_key, String iv, String encryptData) {
        String decryptString = "";
        init();
        byte[] sessionKeyByte = Base64.decodeBase64(session_key);
        byte[] ivByte = Base64.decodeBase64(iv);
        byte[] encryptDataByte = Base64.decodeBase64(encryptData);

        try {
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding");
            Key key = new SecretKeySpec(sessionKeyByte, "AES");
            AlgorithmParameters algorithmParameters = AlgorithmParameters.getInstance("AES");
            algorithmParameters.init(new IvParameterSpec(ivByte));
            cipher.init(Cipher.DECRYPT_MODE, key, algorithmParameters);
            byte[] bytes = cipher.doFinal(encryptDataByte);
            decryptString = new String(bytes);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return decryptString;
    }

    private static boolean hasInited = false;

    public static void init() {
        if (hasInited) {
            return;
        }
        Security.addProvider(new BouncyCastleProvider());
        hasInited = true;
    }


}

