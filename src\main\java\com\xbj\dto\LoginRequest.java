package com.xbj.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotBlank;

/**
 * 登录请求DTO
 */
@Schema(description = "登录请求")
public class LoginRequest {

    @Schema(description = "微信授权码", example = "081234567890abcdef", required = true)
    @NotBlank(message = "微信授权码不能为空")
    private String wxCode;

    public LoginRequest() {
    }

    public LoginRequest(String wxCode) {
        this.wxCode = wxCode;
    }

    public String getWxCode() {
        return wxCode;
    }

    public void setWxCode(String wxCode) {
        this.wxCode = wxCode;
    }

    @Override
    public String toString() {
        return "LoginRequest{" +
                "wxCode='" + wxCode + '\'' +
                '}';
    }
}
