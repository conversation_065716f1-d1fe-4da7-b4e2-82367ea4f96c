package com.xbj.controller;

import com.alibaba.fastjson.JSON;
import com.xbj.common.ApiResponse;
import com.xbj.common.PageResponse;
import com.xbj.common.ResultCode;
import com.xbj.constant.Constants;
import com.xbj.entity.*;
import com.xbj.exception.BusinessException;
import com.xbj.interceptor.TraceInterceptor;
import com.xbj.service.UserService;
import com.xbj.service.WatchCodeService;
import com.xbj.service.WatchService;
import com.xbj.util.JWTUtils;
import com.xbj.util.RedisUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 手表信息控制器 - 微服务化重构版本
 */
@Tag(name = "手表管理", description = "手表信息查询相关接口")
@RestController
@RequestMapping("/api/v1/watches")
public class WatchControllerV2 {

    private static final Logger logger = LoggerFactory.getLogger(WatchControllerV2.class);
    private static final Integer DEFAULT_PAGE_SIZE = 10;

    @Autowired
    private WatchService watchService;

    @Autowired
    private WatchCodeService watchCodeService;

    @Autowired
    private UserService userService;

    @Autowired
    private RedisUtil redisUtil;

    /**
     * 根据条件搜索手表
     */
    @Operation(summary = "搜索手表", description = "根据条件分页搜索手表信息")
    @PostMapping("/search")
    public ApiResponse<PageResponse<Watch>> searchWatches(@Valid @RequestBody SearchInfo searchInfo) {
        try {
            String traceId = TraceInterceptor.getCurrentTraceId();
            logger.info("搜索手表请求 - TraceId: {}, SearchInfo: {}", traceId, searchInfo);

            // 设置分页参数
            Integer pageSize = searchInfo.getPageSize() != null ? searchInfo.getPageSize() : DEFAULT_PAGE_SIZE;
            Integer currentPage = searchInfo.getCurentPage() != null ? searchInfo.getCurentPage() : 1;
            Integer startRow = (currentPage - 1) * pageSize;
            
            searchInfo.setStartRow(startRow);
            searchInfo.setPageSize(pageSize);

            // 查询数据
            List<Watch> watches = watchService.selectByCondition(searchInfo);
            
            // 处理图片URL
            for (Watch watch : watches) {
                if (StringUtils.hasText(watch.getWatchUrl())) {
                    watch.setWatchUrl(String.format("images/watch/%s", watch.getWatchUrl()));
                }
            }

            // 这里应该有专门的计数方法，暂时使用查询结果数量
            Long totalCount = (long) watches.size();
            
            PageResponse<Watch> pageResponse = PageResponse.of(currentPage, pageSize, totalCount, watches);
            
            ApiResponse<PageResponse<Watch>> result = ApiResponse.success("搜索成功", pageResponse);
            result.setTraceId(traceId);
            
            logger.info("搜索手表成功 - TraceId: {}, 返回数量: {}", traceId, watches.size());
            return result;
            
        } catch (Exception e) {
            logger.error("搜索手表失败", e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "搜索失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有品牌列表
     */
    @Operation(summary = "获取品牌列表", description = "获取所有品牌信息")
    @GetMapping("/brands")
    public ApiResponse<List<Map<String, Object>>> getAllBrands() {
        try {
            String traceId = TraceInterceptor.getCurrentTraceId();
            logger.info("获取品牌列表请求 - TraceId: {}", traceId);

            String cacheKey = Constants.BRAND_KEY + "brandInfo";

            // 先从缓存获取
            if (redisUtil.hasKey(cacheKey)) {
                Map<String, Object> cachedData = redisUtil.getObjectString(cacheKey);
                List<Map<String, Object>> brandList = (List<Map<String, Object>>) cachedData.get("data");
                ApiResponse<List<Map<String, Object>>> result = ApiResponse.success("获取成功(缓存)", brandList);
                result.setTraceId(traceId);
                return result;
            }

            // 从数据库查询品牌数据
            WatchCodeExample watchCodeExample = new WatchCodeExample();
            watchCodeExample.setOrderByClause(" START_WITH ASC");
            WatchCodeExample.Criteria criteria = watchCodeExample.or();
            criteria.andTypeEqualTo("brand");
            List<WatchCode> watchCodes = watchCodeService.selectByExample(watchCodeExample);

            // 转换为前端需要的格式
            List<Map<String, Object>> brandList = new ArrayList<>();
            for (WatchCode watchCode : watchCodes) {
                Map<String, Object> brandInfo = new HashMap<>();
                brandInfo.put("id", watchCode.getId());
                brandInfo.put("name", watchCode.getName());
                brandInfo.put("country", "瑞士"); // 默认值，可以根据实际情况调整
                brandInfo.put("foundedYear", null); // 可以扩展数据库字段
                brandInfo.put("description", watchCode.getName() + "品牌");
                brandList.add(brandInfo);
            }

            // 设置缓存
            Map<String, Object> cacheData = new HashMap<>();
            cacheData.put("data", brandList);
            redisUtil.setExpireObject(cacheKey, cacheData, 2, TimeUnit.DAYS);

            ApiResponse<List<Map<String, Object>>> result = ApiResponse.success("获取成功", brandList);
            result.setTraceId(traceId);

            logger.info("获取品牌列表成功 - Count: {}, TraceId: {}", brandList.size(), traceId);
            return result;

        } catch (Exception e) {
            logger.error("获取品牌列表失败", e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取品牌列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取品牌下拉数据
     */
    @Operation(summary = "获取品牌数据", description = "获取指定品牌的系列信息")
    @PostMapping("/brands/{brand}/series")
    public ApiResponse<Map<String, Object>> getBrandSeries(
            @Parameter(description = "品牌名称") @PathVariable @NotBlank String brand) {
        try {
            String traceId = TraceInterceptor.getCurrentTraceId();
            logger.info("获取品牌数据请求 - Brand: {}, TraceId: {}", brand, traceId);

            String cacheKey = Constants.BRAND_KEY + brand;

            // 先从缓存获取
            if (redisUtil.hasKey(cacheKey)) {
                Map<String, Object> cachedData = redisUtil.getObjectString(cacheKey);
                ApiResponse<Map<String, Object>> result = ApiResponse.success("获取成功(缓存)", cachedData);
                result.setTraceId(traceId);
                return result;
            }

            // 从数据库查询
            List<Watch> watches = watchService.selectSeriesByBrandName(brand);
            Map<String, Object> data = new HashMap<>(16);
            data.put("series", watches);

            // 设置缓存
            if (!watches.isEmpty()) {
                redisUtil.setExpireObject(cacheKey, data, 2, TimeUnit.DAYS);
            } else {
                redisUtil.setExpireObject(cacheKey, data, 1, TimeUnit.HOURS);
            }

            ApiResponse<Map<String, Object>> result = ApiResponse.success("获取成功", data);
            result.setTraceId(traceId);

            logger.info("获取品牌数据成功 - Brand: {}, SeriesCount: {}, TraceId: {}",
                       brand, watches.size(), traceId);
            return result;

        } catch (Exception e) {
            logger.error("获取品牌数据失败 - Brand: {}", brand, e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取品牌数据失败: " + e.getMessage());
        }
    }

    /**
     * 添加品牌
     */
    @Operation(summary = "添加品牌", description = "添加新的品牌信息")
    @PostMapping("/brands")
    public ApiResponse<String> addBrand(@Valid @RequestBody Map<String, Object> brandData) {
        try {
            String traceId = TraceInterceptor.getCurrentTraceId();
            logger.info("添加品牌请求 - TraceId: {}, BrandData: {}", traceId, brandData);

            // 创建WatchCode对象
            WatchCode watchCode = new WatchCode();
            watchCode.setId(UUID.randomUUID().toString());
            watchCode.setName((String) brandData.get("name"));
            watchCode.setType("brand");
            watchCode.setTypeName("品牌");
            watchCode.setStartWith(((String) brandData.get("name")).substring(0, 1).toUpperCase());

            // 保存到数据库
            int result = watchCodeService.insertSelective(watchCode);

            if (result > 0) {
                // 清除相关缓存
                String cacheKey = Constants.BRAND_KEY + "brandInfo";
                redisUtil.delete(cacheKey);

                ApiResponse<String> response = ApiResponse.success("品牌添加成功", watchCode.getId());
                response.setTraceId(traceId);
                logger.info("添加品牌成功 - BrandId: {}, TraceId: {}", watchCode.getId(), traceId);
                return response;
            } else {
                throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "品牌添加失败");
            }

        } catch (Exception e) {
            logger.error("添加品牌失败", e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "添加品牌失败: " + e.getMessage());
        }
    }

    /**
     * 更新品牌
     */
    @Operation(summary = "更新品牌", description = "更新品牌信息")
    @PutMapping("/brands/{id}")
    public ApiResponse<String> updateBrand(
            @Parameter(description = "品牌ID") @PathVariable @NotBlank String id,
            @Valid @RequestBody Map<String, Object> brandData) {
        try {
            String traceId = TraceInterceptor.getCurrentTraceId();
            logger.info("更新品牌请求 - BrandId: {}, TraceId: {}, BrandData: {}", id, traceId, brandData);

            // 查询现有品牌
            WatchCode existingBrand = watchCodeService.selectByPrimaryKey(id);
            if (existingBrand == null) {
                throw new BusinessException(ResultCode.NOT_FOUND, "品牌不存在");
            }

            // 更新品牌信息
            existingBrand.setName((String) brandData.get("name"));
            existingBrand.setStartWith(((String) brandData.get("name")).substring(0, 1).toUpperCase());

            int result = watchCodeService.updateByPrimaryKeySelective(existingBrand);

            if (result > 0) {
                // 清除相关缓存
                String cacheKey = Constants.BRAND_KEY + "brandInfo";
                redisUtil.delete(cacheKey);

                ApiResponse<String> response = ApiResponse.success("品牌更新成功", id);
                response.setTraceId(traceId);
                logger.info("更新品牌成功 - BrandId: {}, TraceId: {}", id, traceId);
                return response;
            } else {
                throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "品牌更新失败");
            }

        } catch (Exception e) {
            logger.error("更新品牌失败 - BrandId: {}", id, e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "更新品牌失败: " + e.getMessage());
        }
    }

    /**
     * 删除品牌
     */
    @Operation(summary = "删除品牌", description = "删除品牌信息")
    @DeleteMapping("/brands/{id}")
    public ApiResponse<String> deleteBrand(
            @Parameter(description = "品牌ID") @PathVariable @NotBlank String id) {
        try {
            String traceId = TraceInterceptor.getCurrentTraceId();
            logger.info("删除品牌请求 - BrandId: {}, TraceId: {}", id, traceId);

            // 查询现有品牌
            WatchCode existingBrand = watchCodeService.selectByPrimaryKey(id);
            if (existingBrand == null) {
                throw new BusinessException(ResultCode.NOT_FOUND, "品牌不存在");
            }

            int result = watchCodeService.deleteByPrimaryKey(id);

            if (result > 0) {
                // 清除相关缓存
                String cacheKey = Constants.BRAND_KEY + "brandInfo";
                redisUtil.delete(cacheKey);

                ApiResponse<String> response = ApiResponse.success("品牌删除成功", id);
                response.setTraceId(traceId);
                logger.info("删除品牌成功 - BrandId: {}, TraceId: {}", id, traceId);
                return response;
            } else {
                throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "品牌删除失败");
            }

        } catch (Exception e) {
            logger.error("删除品牌失败 - BrandId: {}", id, e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "删除品牌失败: " + e.getMessage());
        }
    }

    /**
     * 检查用户是否有手机号
     */
    @Operation(summary = "检查用户手机号", description = "检查当前用户是否已绑定手机号")
    @SecurityRequirement(name = "Bearer Authentication")
    @GetMapping("/user/has-phone")
    public ApiResponse<Boolean> checkUserHasPhone(HttpServletRequest request) {
        try {
            String traceId = TraceInterceptor.getCurrentTraceId();
            logger.info("检查用户手机号请求 - TraceId: {}", traceId);

            // 验证JWT令牌
            String token = request.getHeader("authorization");
            if (!StringUtils.hasText(token)) {
                throw new BusinessException(ResultCode.UNAUTHORIZED, "缺少访问令牌");
            }

            Map<String, Object> tokenResult = JWTUtils.verifyTokenGetResult(token, "lookforwatch");
            boolean isValid = Boolean.parseBoolean(tokenResult.get("isSuccess").toString());
            
            if (!isValid) {
                throw new BusinessException(ResultCode.TOKEN_INVALID);
            }

            String openid = tokenResult.get("openid").toString();
            
            // 查询用户信息
            UserExample userExample = new UserExample();
            UserExample.Criteria criteria = userExample.or();
            criteria.andOpenidEqualTo(openid);
            
            List<User> users = userService.selectByExample(userExample);
            boolean hasPhone = false;
            
            if (!users.isEmpty()) {
                User user = users.get(0);
                hasPhone = StringUtils.hasText(user.getPhone());
            }

            ApiResponse<Boolean> result = ApiResponse.success("检查完成", hasPhone);
            result.setTraceId(traceId);
            
            logger.info("检查用户手机号完成 - OpenId: {}, HasPhone: {}, TraceId: {}", 
                       openid, hasPhone, traceId);
            return result;
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            logger.error("检查用户手机号失败", e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "检查失败: " + e.getMessage());
        }
    }

    /**
     * 根据手表编码搜索手表
     */
    @Operation(summary = "根据编码搜索手表", description = "根据手表编码信息搜索相关手表")
    @PostMapping("/search-by-code")
    public ApiResponse<List<Watch>> searchWatchesByCode(@Valid @RequestBody WatchCode watchCode) {
        try {
            String traceId = TraceInterceptor.getCurrentTraceId();
            logger.info("根据编码搜索手表请求 - TraceId: {}, WatchCode: {}", traceId, watchCode);

            WatchExample watchExample = new WatchExample();
            
            if (StringUtils.hasText(watchCode.getName())) {
                if (watchCode.getName().contains("/")) {
                    String[] brandParts = watchCode.getName().split("/");
                    WatchExample.Criteria criteria1 = watchExample.or();
                    WatchExample.Criteria criteria2 = watchExample.or();
                    criteria1.andBrandLike("%" + brandParts[0] + "%");
                    criteria2.andBrandLike("%" + brandParts[1] + "%");
                }
            }
            
            List<Watch> watches = watchService.selectByExample(watchExample);
            
            // 处理图片URL
            for (Watch watch : watches) {
                if (StringUtils.hasText(watch.getWatchUrl())) {
                    watch.setWatchUrl(String.format("images/watch/%s", watch.getWatchUrl()));
                }
            }

            ApiResponse<List<Watch>> result = ApiResponse.success("搜索成功", watches);
            result.setTraceId(traceId);
            
            logger.info("根据编码搜索手表成功 - TraceId: {}, 返回数量: {}", traceId, watches.size());
            return result;
            
        } catch (Exception e) {
            logger.error("根据编码搜索手表失败", e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "搜索失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有搜索条件
     */
    @Operation(summary = "获取搜索条件", description = "获取所有可用的搜索条件数据")
    @GetMapping("/search-conditions")
    public ApiResponse<Map<String, Object>> getAllSearchConditions() {
        try {
            String traceId = TraceInterceptor.getCurrentTraceId();
            logger.info("获取搜索条件请求 - TraceId: {}", traceId);

            String cacheKey = Constants.BRAND_KEY + "CODE";
            
            // 先从缓存获取
            if (redisUtil.hasKey(cacheKey)) {
                Map<String, Object> cachedData = JSON.parseObject(redisUtil.get(cacheKey));
                ApiResponse<Map<String, Object>> result = ApiResponse.success("获取成功(缓存)", cachedData);
                result.setTraceId(traceId);
                return result;
            }

            // 从数据库查询
            Map<String, Object> conditions = new HashMap<>(16);
            List<WatchCode> watchCodeList = watchCodeService.selectAllType();
            
            for (WatchCode watchCode : watchCodeList) {
                WatchCodeExample watchCodeExample = new WatchCodeExample();
                WatchCodeExample.Criteria criteria = watchCodeExample.or();
                String type = watchCode.getType();
                String typeName = watchCode.getTypeName();
                criteria.andTypeEqualTo(type);
                watchCodeExample.setOrderByClause(" code+0 ASC");
                
                if ("brand".equals(type)) {
                    watchCodeExample.setPageSize(6);
                    watchCodeExample.setStartRow(0);
                }
                
                List<WatchCode> watchCodes = watchCodeService.selectByExample(watchCodeExample);
                List<List<WatchCode>> lists = splitList(watchCodes, 4);
                
                Map<String, Object> subMap = new HashMap<>();
                subMap.put("title", typeName);
                subMap.put("data", lists);
                conditions.put(type, subMap);
            }

            // 设置缓存
            redisUtil.setExpireObject(cacheKey, conditions, 2, TimeUnit.DAYS);

            ApiResponse<Map<String, Object>> result = ApiResponse.success("获取成功", conditions);
            result.setTraceId(traceId);
            
            logger.info("获取搜索条件成功 - TraceId: {}, 条件数量: {}", traceId, conditions.size());
            return result;
            
        } catch (Exception e) {
            logger.error("获取搜索条件失败", e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取搜索条件失败: " + e.getMessage());
        }
    }

    /**
     * 分割列表的辅助方法
     */
    private <T> List<List<T>> splitList(List<T> list, int groupSize) {
        List<List<T>> result = new ArrayList<>();
        for (int i = 0; i < list.size(); i += groupSize) {
            result.add(list.subList(i, Math.min(i + groupSize, list.size())));
        }
        return result;
    }
}
