create table user
(
    id        varchar(40)             not null comment '主键或微信unid'
        primary key,
    openid    varchar(40)             not null,
    unionid   varchar(40)             null,
    nick_name varchar(100) default '' null comment '昵称',
    province  varchar(40)  default '' null comment '省份',
    country   varchar(40)  default '' null comment '国家',
    phone     varchar(20)  default '' null comment '手机号码',
    email     varchar(40)  default '' null comment '邮箱',
    adress    varchar(200) default '' null comment '住址',
    gender    varchar(4)   default '' null comment '性别',
    wx_number varchar(50)  default '' null comment '微信号',
    data1     varchar(255) default '' null comment '备用1',
    data2     varchar(255) default '' null comment '备用2'
)
    comment '用户表';

INSERT INTO watch.user (id, openid, unionid, nick_name, province, country, phone, email, adress, gender, wx_number, data1, data2) VALUES ('90223bd1-e74c-4cfe-8ca0-0256a4f8e787', 'o_P135KvtvInE7vEcO8aoi1nrFOE', null, '杨阳', 'Jiangsu', 'China', '13276646608', '', '', '男', 'hello', '', '86');
INSERT INTO watch.user (id, openid, unionid, nick_name, province, country, phone, email, adress, gender, wx_number, data1, data2) VALUES ('e2f4bf75-af66-46b6-9a46-0fee3d3e4e67', 'o_P135GtqAg7ofCHqptAaXnEsz90', null, '胡叔叔', 'Torino', 'Italy', '18852308464', '', '', '男', 'hushushu_', '', '86');