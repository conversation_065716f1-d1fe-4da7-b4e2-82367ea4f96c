{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nexport default {\n  name: 'Login',\n  data() {\n    return {\n      loginForm: {\n        username: '',\n        password: ''\n      },\n      loading: false\n    };\n  },\n  methods: {\n    async handleLogin() {\n      this.loading = true;\n      try {\n        // 模拟登录请求\n        if (this.loginForm.username === 'admin' && this.loginForm.password === '123456') {\n          // 保存登录状态\n          localStorage.setItem('userToken', 'demo-token');\n          localStorage.setItem('userInfo', JSON.stringify({\n            id: 1,\n            username: 'admin',\n            nickname: '管理员'\n          }));\n          this.$router.push('/dashboard');\n        } else {\n          alert('用户名或密码错误');\n        }\n      } catch (error) {\n        console.error('登录失败:', error);\n        alert('登录失败，请重试');\n      } finally {\n        this.loading = false;\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "loginForm", "username", "password", "loading", "methods", "handleLogin", "localStorage", "setItem", "JSON", "stringify", "id", "nickname", "$router", "push", "alert", "error", "console"], "sources": ["D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\Login.vue"], "sourcesContent": ["<template>\n  <div class=\"login-container\">\n    <div class=\"login-box\">\n      <div class=\"login-header\">\n        <h1>寻表记</h1>\n        <p>手表管理系统</p>\n      </div>\n      \n      <form @submit.prevent=\"handleLogin\" class=\"login-form\">\n        <div class=\"form-group\">\n          <label for=\"username\">用户名</label>\n          <input\n            type=\"text\"\n            id=\"username\"\n            v-model=\"loginForm.username\"\n            placeholder=\"请输入用户名\"\n            required\n          />\n        </div>\n        \n        <div class=\"form-group\">\n          <label for=\"password\">密码</label>\n          <input\n            type=\"password\"\n            id=\"password\"\n            v-model=\"loginForm.password\"\n            placeholder=\"请输入密码\"\n            required\n          />\n        </div>\n        \n        <button type=\"submit\" class=\"login-btn\" :disabled=\"loading\">\n          {{ loading ? '登录中...' : '登录' }}\n        </button>\n      </form>\n      \n      <div class=\"login-footer\">\n        <p>演示账号: admin / 123456</p>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Login',\n  data() {\n    return {\n      loginForm: {\n        username: '',\n        password: ''\n      },\n      loading: false\n    }\n  },\n  methods: {\n    async handleLogin() {\n      this.loading = true\n      \n      try {\n        // 模拟登录请求\n        if (this.loginForm.username === 'admin' && this.loginForm.password === '123456') {\n          // 保存登录状态\n          localStorage.setItem('userToken', 'demo-token')\n          localStorage.setItem('userInfo', JSON.stringify({\n            id: 1,\n            username: 'admin',\n            nickname: '管理员'\n          }))\n          \n          this.$router.push('/dashboard')\n        } else {\n          alert('用户名或密码错误')\n        }\n      } catch (error) {\n        console.error('登录失败:', error)\n        alert('登录失败，请重试')\n      } finally {\n        this.loading = false\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.login-container {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.login-box {\n  background: white;\n  padding: 40px;\n  border-radius: 10px;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\n  width: 100%;\n  max-width: 400px;\n}\n\n.login-header {\n  text-align: center;\n  margin-bottom: 30px;\n}\n\n.login-header h1 {\n  color: #333;\n  margin-bottom: 10px;\n  font-size: 28px;\n}\n\n.login-header p {\n  color: #666;\n  font-size: 14px;\n}\n\n.form-group {\n  margin-bottom: 20px;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 5px;\n  color: #333;\n  font-weight: 500;\n}\n\n.form-group input {\n  width: 100%;\n  padding: 12px;\n  border: 1px solid #ddd;\n  border-radius: 5px;\n  font-size: 14px;\n  transition: border-color 0.3s;\n}\n\n.form-group input:focus {\n  outline: none;\n  border-color: #667eea;\n}\n\n.login-btn {\n  width: 100%;\n  padding: 12px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  border-radius: 5px;\n  font-size: 16px;\n  cursor: pointer;\n  transition: opacity 0.3s;\n}\n\n.login-btn:hover {\n  opacity: 0.9;\n}\n\n.login-btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.login-footer {\n  text-align: center;\n  margin-top: 20px;\n  padding-top: 20px;\n  border-top: 1px solid #eee;\n}\n\n.login-footer p {\n  color: #999;\n  font-size: 12px;\n}\n</style>\n"], "mappings": ";AA4CA,eAAe;EACbA,IAAI,EAAE,OAAO;EACbC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,SAAS,EAAE;QACTC,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE;MACZ,CAAC;MACDC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,OAAO,EAAE;IACP,MAAMC,WAAWA,CAAA,EAAG;MAClB,IAAI,CAACF,OAAM,GAAI,IAAG;MAElB,IAAI;QACF;QACA,IAAI,IAAI,CAACH,SAAS,CAACC,QAAO,KAAM,OAAM,IAAK,IAAI,CAACD,SAAS,CAACE,QAAO,KAAM,QAAQ,EAAE;UAC/E;UACAI,YAAY,CAACC,OAAO,CAAC,WAAW,EAAE,YAAY;UAC9CD,YAAY,CAACC,OAAO,CAAC,UAAU,EAAEC,IAAI,CAACC,SAAS,CAAC;YAC9CC,EAAE,EAAE,CAAC;YACLT,QAAQ,EAAE,OAAO;YACjBU,QAAQ,EAAE;UACZ,CAAC,CAAC;UAEF,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,YAAY;QAChC,OAAO;UACLC,KAAK,CAAC,UAAU;QAClB;MACF,EAAE,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK;QAC5BD,KAAK,CAAC,UAAU;MAClB,UAAU;QACR,IAAI,CAACX,OAAM,GAAI,KAAI;MACrB;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}