package com.xbj.test;

import java.io.*;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2023/10/20 11:27
 **/
public class SqlToCamelCase {

        public static void main(String[] args) {
            String inputFile = "E:\\db_model.sql";
            String outputFile = "E:\\output.sql";

            try (BufferedReader reader = new BufferedReader(new InputStreamReader(new FileInputStream(inputFile), StandardCharsets.UTF_8));
                 BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(outputFile), StandardCharsets.UTF_8))) {

                String line;
                while ((line = reader.readLine()) != null) {
                    // 将表名和字段名从下划线式命名转换为驼峰命名
                    line = convertToCamelCase(line);
                    writer.write(line + "\n");
                }

                System.out.println("SQL文件已处理完成！");
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        private static String convertToCamelCase(String line) {
            String[] parts = line.split("\\s+");
            for (int i = 0; i < parts.length; i++) {
                parts[i] = convertIdentifierToCamelCase(parts[i]);
            }
            return String.join(" ", parts);
        }

        private static String convertIdentifierToCamelCase(String identifier) {
            StringBuilder result = new StringBuilder();
            boolean capitalizeNext = false;

            for (char c : identifier.toCharArray()) {
                if (c == '_') {
                    capitalizeNext = true;
                } else {
                    if (capitalizeNext) {
                        result.append(Character.toUpperCase(c));
                    } else {
                        result.append(Character.toLowerCase(c));
                    }
                    capitalizeNext = false;
                }
            }

            return result.toString();
        }

}
