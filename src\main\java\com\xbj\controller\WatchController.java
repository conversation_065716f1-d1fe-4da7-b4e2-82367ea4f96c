package com.xbj.controller;


import com.alibaba.fastjson.JSON;
import com.xbj.constant.Constants;
import com.xbj.entity.*;
import com.xbj.service.UserService;
import com.xbj.service.WatchCodeService;
import com.xbj.service.WatchService;
import com.xbj.util.JWTUtils;
import com.xbj.util.RedisUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@RequestMapping(value = "/WatchController",method = RequestMethod.POST)
@RestController //RestController = Controller + ResponseBody
public class WatchController {

    private static final Logger logger = LoggerFactory.getLogger(WatchController.class);

    private final WatchService watchService;

    private final WatchCodeService watchCodeService;

    private final UserService userService;

    private static final Integer PAGE_SIZE = 10;

    private final RedisUtil redisUtil;

    @Autowired
    public WatchController(RedisUtil redisUtil, WatchService watchService, WatchCodeService watchCodeService, UserService userService) {
        this.redisUtil = redisUtil;
        this.watchService = watchService;
        this.watchCodeService = watchCodeService;
        this.userService = userService;
    }

    /**
     *
     * @return List<Watch>
     */
    @RequestMapping(value = "/getAllWatches",method = RequestMethod.POST)
    public List<Watch> getWatch(@RequestBody SearchInfo searchInfo){

//        SearchInfo searchInfo = JSONObject.parseObject(data, SearchInfo.class);
        Integer pageSize;
        if (!StringUtils.isEmpty(searchInfo.getPageSize())) {
            pageSize = searchInfo.getPageSize();
        } else {
            pageSize = WatchController.PAGE_SIZE;
        }

        Integer curRow = (searchInfo.getCurentPage()-1) * pageSize;

        searchInfo.setStartRow(curRow);

        List<Watch> watches = watchService.selectByCondition(searchInfo);
        // String tmpName = "";
        for (Watch watch : watches) {
            // tmpName = watch.getBrand().split("/")[1].replace(" ","");
            // watch.setWatchUrl(String.format("images/watch/%s/%s",watch.getBrand(),watch.getWatchUrl()));
            watch.setWatchUrl(String.format("images/watch/%s",watch.getWatchUrl()));
        }
        return watches;
    }

    @RequestMapping("/getDropDownData")
    public Map<String,Object> getDropDownData(@RequestBody Map map){
        Map<String,Object> res = new HashMap<>(16);
        String brand = map.get("brand").toString();
        if (redisUtil.hasKey(Constants.BRAND_KEY +brand)){
            return redisUtil.getObjectString(Constants.BRAND_KEY +brand);
        }else {
            List<Watch> watches = watchService.selectSeriesByBrandName(brand);
            res.put("series",watches);
            if (watches.size()>0){
                redisUtil.setExpireObject(Constants.BRAND_KEY +brand,res,2,TimeUnit.DAYS);
            }else {
                redisUtil.setExpireObject(Constants.BRAND_KEY +brand,res,1,TimeUnit.HOURS);
            }
        }
        return res;
    }

    @RequestMapping(value = "/userHasPhone",method = RequestMethod.POST)
    public Boolean getUserInfo(HttpServletRequest request){
        boolean result = false;
        String token = request.getHeader("authorization");
        Map res = JWTUtils.verifyTokenGetResult(token, "lookforwatch");
        boolean isSuccess = Boolean.parseBoolean(res.get("isSuccess").toString());
        if (isSuccess){
            String openid = res.get("openid").toString();
            UserExample userExample = new UserExample();
            UserExample.Criteria or = userExample.or();
            or.andOpenidEqualTo(openid);
            List<User> users = userService.selectByExample(userExample);
            if (users.size()>0){
                User user = users.get(0);
                if (!StringUtils.isEmpty(user.getPhone())){
                    result = true;
                }
            }
        }
        return result;
    }

    /**
     * 获取所有的品牌列表
     */
    @RequestMapping("/getAllBrands")
    public Map<String,Object> getAllBrands(){
        Map<String,Object> finRes = new HashMap<>();
        if (redisUtil.hasKey(Constants.BRAND_KEY+"brandInfo")){
            finRes = redisUtil.getObjectString(Constants.BRAND_KEY+"brandInfo");
        }else {
            WatchCodeExample watchCodeExample = new WatchCodeExample();
            watchCodeExample.setOrderByClause(" START_WITH ASC");
            WatchCodeExample.Criteria or = watchCodeExample.or();
            or.andTypeEqualTo("brand");
            List<WatchCode> watchCodes = watchCodeService.selectByExample(watchCodeExample);
            Map<String,List<WatchCode>> resMap = new HashMap<>();

            Set<String> typesSet = new HashSet<>();
            for (WatchCode watchCode : watchCodes) {
                watchCode.setPicUrl("images/brand/"+watchCode.getPicUrl());
                watchCode.setName_0(watchCode.getName().split("/")[0]);
                watchCode.setName_1(watchCode.getName().split("/")[1]);
                String with = watchCode.getStartWith();
                if (!StringUtils.isEmpty(with)){
                    if (resMap.containsKey(with))
                    {
                        List<WatchCode> watches = resMap.get(with);
                        watches.add(watchCode);
                    }else {
                        resMap.put(with,new ArrayList<>());
                        List<WatchCode> watchCodes1 = resMap.get(with);
                        watchCodes1.add(watchCode);
                    }
                    typesSet.add(with);
                }
            }
            List<Map<String,Object>> resList = new ArrayList<>();
            for (String s : typesSet) {
                Map<String,Object> temp = new HashMap<>();
                temp.put("cityInfo",resMap.get(s));
                temp.put("initial",s.toUpperCase());
                resList.add(temp);
            }
            WatchCodeExample watchCodeExample1 = new WatchCodeExample();
            WatchCodeExample.Criteria or1 = watchCodeExample1.or();
            or1.andTypeEqualTo("service_url");
            List<WatchCode> serviceUrl = watchCodeService.selectByExample(watchCodeExample1);


            finRes.put("searchLetter",typesSet);
            finRes.put("data",resList);
            finRes.put("serviceUrl",serviceUrl.get(0).getName());
//            UserInfoCache.put("brandInfo",finRes,86400000);
            redisUtil.setExpireObject(Constants.BRAND_KEY+"brandInfo",finRes,2,TimeUnit.DAYS);
        }
        return finRes;
    }

    @RequestMapping("/getWatchByWatchCode")
    public List<Watch> getWatchByWatchCode(@RequestBody WatchCode watchCode){
        WatchExample watchExample = new WatchExample();

        WatchExample.Criteria or = watchExample.or();
        WatchExample.Criteria or1 = watchExample.or();
        if (!StringUtils.isEmpty(watchCode.getName())){
            if (watchCode.getName().contains("/")){
                String [] arr = watchCode.getName().split("/");
                or.andBrandLike("%"+arr[0]+"%");
                or1.andBrandLike("%"+arr[1]+"%");
            }
        }
        watchExample.or(or1);
        List<Watch> watches = watchService.selectByExample(watchExample);
        // String tmpName = "";
        for (Watch watch : watches) {
            // tmpName = watch.getBrand().split("/")[1].replace(" ","");
            // watch.setWatchUrl(String.format("images/watch/%s/%s",watch.getBrand(),watch.getWatchUrl()));
            watch.setWatchUrl(String.format("images/watch/%s",watch.getWatchUrl()));
        }
        return watches;
    }

    @RequestMapping(value = "/getAllSearchCondition",method = RequestMethod.POST)
    public Object getAllSearchCondition(String loginStatus){
        Map<String,Object> res = new HashMap<>(16);
        if (redisUtil.hasKey(Constants.BRAND_KEY+"CODE")){
            res = JSON.parseObject(redisUtil.get(Constants.BRAND_KEY+"CODE"));
        }else {
            List<WatchCode> watchCodeList = watchCodeService.selectAllType();
            for (WatchCode watchCode : watchCodeList) {
                WatchCodeExample watchCodeExample = new WatchCodeExample();
                WatchCodeExample.Criteria or = watchCodeExample.or();
                String type = watchCode.getType();
                String typeName = watchCode.getTypeName();
                or.andTypeEqualTo(type);
                watchCodeExample.setOrderByClause(" code+0 ASC");
                if ("brand".equals(type)){
                    watchCodeExample.setPageSize(6);
                    watchCodeExample.setStartRow(0);
                }
                List<WatchCode> watchCodes = watchCodeService.selectByExample(watchCodeExample);
                List<List<WatchCode>> lists = splitList(watchCodes, 4);
                Map<String,Object> subMap = new HashMap<>();
                subMap.put("title",typeName);
                subMap.put("data",lists);
                res.put(type,subMap);
            }
//            UserInfoCache.put("CODE",res,86400000);
            redisUtil.setExpireObject(Constants.BRAND_KEY+"CODE",res,2, TimeUnit.DAYS);
        }
        return res;
    }

    private <T> List<List<T>> splitList(List<T> list, int len) {
        if (list == null || list.size() == 0 || len < 1) {
            return null;
        }
        //返回结果
        List<List<T>> result = new ArrayList<>();
        //传入集合长度
        int size = list.size();
        //分隔后的集合个数
        int count = (size + len - 1) / len;
        for (int i = 0; i < count; i++) {
            List<T> subList = list.subList(i * len, (Math.min((i + 1) * len, size)));
            result.add(subList);
        }
        return result;
    }


}
