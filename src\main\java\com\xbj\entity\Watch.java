package com.xbj.entity;

public class Watch {
    private String id;

    private String brand;

    private String series;

    private String model;

    private String size;

    private String priceInland;

    private String coreType;

    private String coreModel;

    private String bkType;

    private String bdType;

    private String bkHorSize;

    private String bpColor;

    private String ks;

    private String fbDate;

    private String fzFun;

    private String watchUrl;

    private String bkouType;

    private String useHours;

    private String waterDepth;

    private String priceInHk;

    private String priceInItaly;

    private String priceInFrance;

    private String priceInSpain;

    private String priceInSwitzerland;

    private String priceInGermany;

    private String priceInHolland;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand == null ? null : brand.trim();
    }

    public String getSeries() {
        return series;
    }

    public void setSeries(String series) {
        this.series = series == null ? null : series.trim();
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model == null ? null : model.trim();
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size == null ? null : size.trim();
    }

    public String getPriceInland() {
        return priceInland;
    }

    public void setPriceInland(String priceInland) {
        this.priceInland = priceInland == null ? null : priceInland.trim();
    }

    public String getCoreType() {
        return coreType;
    }

    public void setCoreType(String coreType) {
        this.coreType = coreType == null ? null : coreType.trim();
    }

    public String getCoreModel() {
        return coreModel;
    }

    public void setCoreModel(String coreModel) {
        this.coreModel = coreModel == null ? null : coreModel.trim();
    }

    public String getBkType() {
        return bkType;
    }

    public void setBkType(String bkType) {
        this.bkType = bkType == null ? null : bkType.trim();
    }

    public String getBdType() {
        return bdType;
    }

    public void setBdType(String bdType) {
        this.bdType = bdType == null ? null : bdType.trim();
    }

    public String getBkHorSize() {
        return bkHorSize;
    }

    public void setBkHorSize(String bkHorSize) {
        this.bkHorSize = bkHorSize == null ? null : bkHorSize.trim();
    }

    public String getBpColor() {
        return bpColor;
    }

    public void setBpColor(String bpColor) {
        this.bpColor = bpColor == null ? null : bpColor.trim();
    }

    public String getKs() {
        return ks;
    }

    public void setKs(String ks) {
        this.ks = ks == null ? null : ks.trim();
    }

    public String getFbDate() {
        return fbDate;
    }

    public void setFbDate(String fbDate) {
        this.fbDate = fbDate == null ? null : fbDate.trim();
    }

    public String getFzFun() {
        return fzFun;
    }

    public void setFzFun(String fzFun) {
        this.fzFun = fzFun == null ? null : fzFun.trim();
    }

    public String getWatchUrl() {
        return watchUrl;
    }

    public void setWatchUrl(String watchUrl) {
        this.watchUrl = watchUrl == null ? null : watchUrl.trim();
    }

    public String getBkouType() {
        return bkouType;
    }

    public void setBkouType(String bkouType) {
        this.bkouType = bkouType == null ? null : bkouType.trim();
    }

    public String getUseHours() {
        return useHours;
    }

    public void setUseHours(String useHours) {
        this.useHours = useHours == null ? null : useHours.trim();
    }

    public String getWaterDepth() {
        return waterDepth;
    }

    public void setWaterDepth(String waterDepth) {
        this.waterDepth = waterDepth == null ? null : waterDepth.trim();
    }

    public String getPriceInHk() {
        return priceInHk;
    }

    public void setPriceInHk(String priceInHk) {
        this.priceInHk = priceInHk == null ? null : priceInHk.trim();
    }

    public String getPriceInItaly() {
        return priceInItaly;
    }

    public void setPriceInItaly(String priceInItaly) {
        this.priceInItaly = priceInItaly == null ? null : priceInItaly.trim();
    }

    public String getPriceInFrance() {
        return priceInFrance;
    }

    public void setPriceInFrance(String priceInFrance) {
        this.priceInFrance = priceInFrance == null ? null : priceInFrance.trim();
    }

    public String getPriceInSpain() {
        return priceInSpain;
    }

    public void setPriceInSpain(String priceInSpain) {
        this.priceInSpain = priceInSpain == null ? null : priceInSpain.trim();
    }

    public String getPriceInSwitzerland() {
        return priceInSwitzerland;
    }

    public void setPriceInSwitzerland(String priceInSwitzerland) {
        this.priceInSwitzerland = priceInSwitzerland == null ? null : priceInSwitzerland.trim();
    }

    public String getPriceInGermany() {
        return priceInGermany;
    }

    public void setPriceInGermany(String priceInGermany) {
        this.priceInGermany = priceInGermany == null ? null : priceInGermany.trim();
    }

    public String getPriceInHolland() {
        return priceInHolland;
    }

    public void setPriceInHolland(String priceInHolland) {
        this.priceInHolland = priceInHolland == null ? null : priceInHolland.trim();
    }
}