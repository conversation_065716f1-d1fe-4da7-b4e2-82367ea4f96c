package com.xbj.test;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xbj.entity.User;
import com.xbj.entity.Watch;
import com.xbj.util.RedisUtil;
import com.xbj.util.UserInfoCache;
import com.xbj.util.security.SM4Utils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @create 2020-01-20 16:19
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration({"classpath:spring/spring-*.xml"})
@WebAppConfiguration
public class TestSomething {
    @Autowired
    WebApplicationContext context;

    MockMvc mockMvc;


    @Autowired
    private StringRedisTemplate redisTemplate;


    @Before
    public void initMokcMvc() {
        mockMvc = MockMvcBuilders.webAppContextSetup(context).build();
    }

    @Test
    public void test01() throws Exception {
//        MvcResult result = mockMvc.perform(MockMvcRequestBuilders.get("/messages").param("pn", "5"))
//                .andReturn();
//
//        MockHttpServletRequest request = result.getRequest();
//        PageInfo pi = (PageInfo) request.getAttribute("pageInfo");
//        System.out.println(pi);
        UserInfoCache.put("13276646608","1234",1000);
        UserInfoCache.put("11111111111","1234");
        System.out.println("map--size"+UserInfoCache.size());
    }

    @Test
    public void test02() throws Exception{
//        ApplicationContext context=new AnnotationConfigApplicationContext(RedisConfiguration.class);
        Watch watch = new Watch();
        watch.setWatchUrl("surl");
        watch.setBrand("劳力士");
        Watch watch1 = new Watch();
        watch1.setWatchUrl("surl");
        watch1.setBrand("劳力士01");
        List<Watch> list = new ArrayList<>();
        list.add(watch);
        list.add(watch1);
        List<List<Watch>> lists = new ArrayList<>();
        lists.add(list);
        lists.add(list);

        Map<String,Object> map = new HashMap<>();
        map.put("title","rolex");
        map.put("data",lists);
        RedisUtil redisUtil = context.getBean(RedisUtil.class);
//        String string = JSON.toJSONString(map);
        redisUtil.setExpireObject("test",lists,20, TimeUnit.SECONDS);
        JSONArray code = redisUtil.getListString("test");
        System.out.println(redisUtil.get("test"));
    }

    @Test
    public void test03(){
        RedisUtil redisUtil = context.getBean(RedisUtil.class);
        Object code = redisUtil.getObjectString("CODE");
        System.out.println(code);
    }


    @Test
    public void  testSM4() throws Exception{

        List<User> users = new ArrayList<>();
        for (int i = 0; i < 1000; i++) {
            User user = new User();
            user.setId("1111"+i);
            user.setCountry("江苏"+i);
            user.setGender("1");
            user.setAdress("国家电网");
            user.setEmail("<EMAIL>");
            users.add(user);
        }

        String string = JSONObject.toJSONString(users);
        SM4Utils sm4 = new SM4Utils();
        sm4.setSecretKey("11HDESaAhiHHugDz");
//        String  string= "需要加密的字符串!";
//        System.out.println("加密前"+string);
        String encryData = sm4.encryptData_ECB(string);
        System.out.println("加密后："+encryData);
        String decryData = sm4.decryptData_ECB(encryData);
        System.out.println("解密后："+decryData);

    }

    @Test
    public void testLocalData() throws Exception{
        LocalDateTime time = LocalDateTime.now();
        System.out.println(time.toLocalTime().toString());
    }


}
