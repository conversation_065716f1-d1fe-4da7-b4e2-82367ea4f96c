package com.xbj.test;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/7 17:56
 **/
public class SqlGenerate {
    public static void main(String[] args) {
        String data = "公司发展计划、批复意见、发布单位（部门）、发布时间";

        List<String> items = splitData(data);

        // 遍历拆分后的项
        for (String item : items) {
            System.out.println(item);
        }
    }

    private static List<String> splitData(String data) {
        List<String> items = new ArrayList<>();
        StringBuilder item = new StringBuilder();
        int parenthesesLevel = 0;

        for (char c : data.toCharArray()) {
            if (c == '、' && parenthesesLevel == 0) {
                items.add(item.toString().trim());
                item.setLength(0);
            } else {
                item.append(c);
                if (c == '（') {
                    parenthesesLevel++;
                } else if (c == '）') {
                    parenthesesLevel--;
                }
            }
        }

        if (item.length() > 0) {
            items.add(item.toString().trim());
        }

        return items;
    }
}
