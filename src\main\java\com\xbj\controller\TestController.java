package com.xbj.controller;

import com.xbj.common.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器 - 用于验证前后端连接
 */
@Tag(name = "测试接口", description = "用于测试前后端连接的接口")
@RestController
@RequestMapping("/api/test")
public class TestController {

    /**
     * 简单的测试接口
     */
    @Operation(summary = "测试接口", description = "用于测试前后端连接")
    @GetMapping("/hello")
    public ApiResponse<String> hello() {
        return ApiResponse.success("Hello from Spring Boot!", "后端连接成功！");
    }

    /**
     * 简单登录接口 - 用于前端测试
     */
    @Operation(summary = "简单登录", description = "用于前端测试的简单登录接口")
    @PostMapping("/login")
    public ApiResponse<Map<String, Object>> simpleLogin(@RequestBody Map<String, String> loginRequest) {
        try {
            String username = loginRequest.get("username");
            String password = loginRequest.get("password");
            
            // 简单的演示登录验证
            if ("admin".equals(username) && "123456".equals(password)) {
                Map<String, Object> result = new HashMap<>();
                result.put("token", "demo-token-" + System.currentTimeMillis());
                result.put("username", username);
                result.put("nickname", "管理员");
                result.put("id", 1);
                
                return ApiResponse.success("登录成功", result);
            } else {
                return ApiResponse.error(401, "用户名或密码错误");
            }
        } catch (Exception e) {
            return ApiResponse.error(500, "登录失败: " + e.getMessage());
        }
    }
}
