package com.xbj.interceptor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.UUID;

/**
 * 请求追踪拦截器
 */
@Component
public class TraceInterceptor implements HandlerInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(TraceInterceptor.class);
    private static final String TRACE_ID_HEADER = "X-Trace-Id";
    private static final String TRACE_ID_KEY = "traceId";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 生成或获取追踪ID
        String traceId = request.getHeader(TRACE_ID_HEADER);
        if (traceId == null || traceId.trim().isEmpty()) {
            traceId = UUID.randomUUID().toString().replace("-", "");
        }

        // 设置到MDC中，用于日志输出
        MDC.put(TRACE_ID_KEY, traceId);
        
        // 设置到响应头中
        response.setHeader(TRACE_ID_HEADER, traceId);
        
        // 记录请求开始时间
        request.setAttribute("startTime", System.currentTimeMillis());
        
        logger.info("请求开始 - Method: {}, URI: {}, TraceId: {}", 
                request.getMethod(), request.getRequestURI(), traceId);
        
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, 
                               Object handler, Exception ex) {
        try {
            Long startTime = (Long) request.getAttribute("startTime");
            long duration = System.currentTimeMillis() - (startTime != null ? startTime : 0);
            
            String traceId = MDC.get(TRACE_ID_KEY);
            
            if (ex != null) {
                logger.error("请求异常 - Method: {}, URI: {}, TraceId: {}, Duration: {}ms, Exception: {}", 
                        request.getMethod(), request.getRequestURI(), traceId, duration, ex.getMessage());
            } else {
                logger.info("请求完成 - Method: {}, URI: {}, TraceId: {}, Duration: {}ms, Status: {}", 
                        request.getMethod(), request.getRequestURI(), traceId, duration, response.getStatus());
            }
        } finally {
            // 清理MDC
            MDC.clear();
        }
    }

    /**
     * 获取当前请求的追踪ID
     */
    public static String getCurrentTraceId() {
        return MDC.get(TRACE_ID_KEY);
    }
}
