package com.xbj.service;

import com.xbj.entity.WatchCode;
import com.xbj.entity.WatchCodeExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WatchCodeService {

    long countByExample(WatchCodeExample example);

    int deleteByExample(WatchCodeExample example);

    int deleteByPrimaryKey(String id);

    int insert(WatchCode record);

    int insertSelective(WatchCode record);

    List<WatchCode> selectByExample(WatchCodeExample example);

    List<WatchCode> selectAllType();

    WatchCode selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") WatchCode record, @Param("example") WatchCodeExample example);

    int updateByExample(@Param("record") WatchCode record, @Param("example") WatchCodeExample example);

    int updateByPrimaryKeySelective(WatchCode record);

    int updateByPrimaryKey(WatchCode record);
}
