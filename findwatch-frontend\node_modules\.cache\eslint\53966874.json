[{"D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\main.js": "1", "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\App.vue": "2", "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\router\\index.js": "3", "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\Login.vue": "4", "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\BrandList.vue": "5", "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\UserProfile.vue": "6", "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\WatchList.vue": "7", "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\Dashboard.vue": "8"}, {"size": 706, "mtime": 1752046719477, "results": "9", "hashOfConfig": "10"}, {"size": 488, "mtime": 1752043866090, "results": "11", "hashOfConfig": "10"}, {"size": 1282, "mtime": 1752043854600, "results": "12", "hashOfConfig": "10"}, {"size": 3562, "mtime": 1752047407474, "results": "13", "hashOfConfig": "10"}, {"size": 9601, "mtime": 1752044002396, "results": "14", "hashOfConfig": "10"}, {"size": 10787, "mtime": 1752044047566, "results": "15", "hashOfConfig": "10"}, {"size": 9212, "mtime": 1752043960582, "results": "16", "hashOfConfig": "10"}, {"size": 5644, "mtime": 1752043918775, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "20"}, "1esy7n0", {"filePath": "21", "messages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "23"}, {"filePath": "24", "messages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "20"}, {"filePath": "26", "messages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "23"}, {"filePath": "30", "messages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "23"}, {"filePath": "32", "messages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "23"}, {"filePath": "34", "messages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "23"}, "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\main.js", [], [], "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\App.vue", [], [], "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\router\\index.js", [], "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\Login.vue", [], "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\BrandList.vue", [], "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\UserProfile.vue", [], "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\WatchList.vue", [], "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\Dashboard.vue", []]