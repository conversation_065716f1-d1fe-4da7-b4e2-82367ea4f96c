[{"D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\main.js": "1", "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\App.vue": "2", "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\router\\index.js": "3", "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\Login.vue": "4", "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\BrandList.vue": "5", "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\UserProfile.vue": "6", "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\WatchList.vue": "7", "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\Dashboard.vue": "8", "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\Test.vue": "9"}, {"size": 706, "mtime": 1752047547129, "results": "10", "hashOfConfig": "11"}, {"size": 488, "mtime": 1752043866090, "results": "12", "hashOfConfig": "11"}, {"size": 1385, "mtime": 1752047677147, "results": "13", "hashOfConfig": "11"}, {"size": 3562, "mtime": 1752047407474, "results": "14", "hashOfConfig": "11"}, {"size": 9601, "mtime": 1752044002396, "results": "15", "hashOfConfig": "11"}, {"size": 10787, "mtime": 1752044047566, "results": "16", "hashOfConfig": "11"}, {"size": 9212, "mtime": 1752043960582, "results": "17", "hashOfConfig": "11"}, {"size": 5644, "mtime": 1752043918775, "results": "18", "hashOfConfig": "11"}, {"size": 4299, "mtime": 1752047638821, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1esy7n0", {"filePath": "22", "messages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "24"}, {"filePath": "25", "messages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "24"}, {"filePath": "31", "messages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "24"}, {"filePath": "33", "messages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "24"}, {"filePath": "35", "messages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "24"}, {"filePath": "37", "messages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\main.js", [], "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\App.vue", [], [], "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\router\\index.js", [], "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\Login.vue", [], "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\BrandList.vue", [], "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\UserProfile.vue", [], "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\WatchList.vue", [], "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\Dashboard.vue", [], "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\Test.vue", []]