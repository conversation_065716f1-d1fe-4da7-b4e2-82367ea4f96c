# 寻表记 (FindWatch) - 微信小程序后端

## 项目简介
寻表记是一个基于Spring Boot的微信小程序后端服务，提供手表品牌管理、用户管理等功能。

## 技术栈
- **后端框架**: Spring Boot 2.7.18
- **数据库**: MySQL 8.0 (Docker容器)
- **缓存**: Redis (Docker容器)
- **ORM**: MyBatis
- **认证**: JWT + 微信小程序认证
- **API文档**: Swagger/OpenAPI 3
- **前端**: Vue.js 3 + 自定义UI组件
- **容器化**: Docker + Docker Compose

## Docker容器配置

### MySQL 8.0 容器
- **容器名称**: mysql
- **端口映射**: 3306:3306
- **Root密码**: FLZX3000C_ysyhl9t
- **数据库名**: findwatch
- **用户名**: findwatch
- **用户密码**: FLZX3000C_ysyhl9t
- **数据持久化**: /docker/mysql/data
- **配置文件**: /docker/mysql/conf/my.cnf

### Redis 容器
- **容器名称**: redis
- **端口映射**: 6379:6379
- **认证密码**: FLZX3000C_ysyhl9t
- **数据持久化**: /docker/redis/data
- **配置文件**: /docker/redis/conf/redis.conf

### 容器管理命令
```bash
# 启动所有容器
docker start mysql redis

# 停止所有容器
docker stop mysql redis

# 重启所有容器
docker restart mysql redis

# 查看容器状态
docker ps

# 查看容器日志
docker logs mysql
docker logs redis

# 进入MySQL容器
docker exec -it mysql mysql -uroot -pFLZX3000C_ysyhl9t

# 进入Redis容器
docker exec -it redis redis-cli -a FLZX3000C_ysyhl9t
```

## 服务器信息
- **服务器地址**: *************
- **用户名**: root
- **密码**: FLZX3000C_ysyhl9t
- **操作系统**: CentOS 7

## 数据库连接信息
```yaml
# application.yml 配置
spring:
  datasource:
    url: *************************************************************************************************************
    username: findwatch
    password: FLZX3000C_ysyhl9t
  redis:
    host: *************
    port: 6379
    password: FLZX3000C_ysyhl9t
```

## 项目结构
```
findwatch/
├── src/main/java/com/xbj/
│   ├── controller/          # 控制器层
│   │   ├── AuthController.java      # 认证控制器
│   │   ├── BrandController.java     # 品牌管理控制器
│   │   ├── UserController.java      # 用户管理控制器
│   │   └── WatchController.java     # 手表管理控制器
│   ├── service/            # 服务层
│   │   ├── AuthService.java        # 认证服务
│   │   ├── BrandService.java       # 品牌服务
│   │   ├── UserService.java        # 用户服务
│   │   └── WatchService.java       # 手表服务
│   ├── entity/             # 实体类
│   │   ├── User.java               # 用户实体
│   │   ├── Brand.java              # 品牌实体
│   │   └── Watch.java              # 手表实体
│   ├── mapper/             # MyBatis映射器
│   ├── config/             # 配置类
│   │   ├── CorsConfig.java         # 跨域配置
│   │   ├── JwtConfig.java          # JWT配置
│   │   └── SwaggerConfig.java      # Swagger配置
│   └── util/               # 工具类
├── src/main/resources/
│   ├── mapper/             # MyBatis XML映射文件
│   ├── application.yml     # 应用配置
│   └── sql/               # 数据库脚本
├── findwatch-frontend/     # Vue.js前端项目
│   ├── src/
│   │   ├── views/         # 页面组件
│   │   ├── components/    # 通用组件
│   │   ├── router/        # 路由配置
│   │   └── main.js        # 入口文件
│   ├── package.json
│   └── vite.config.js
└── README.md
```

## 快速开始

### 1. 环境要求
- Java 8+
- Node.js 16+
- Docker & Docker Compose

### 2. 启动后端服务
```bash
# 克隆项目
git clone <repository-url>
cd findwatch

# 启动Docker容器（MySQL和Redis）
docker start mysql redis

# 编译并运行Spring Boot应用
./mvnw spring-boot:run
# 或者
mvn spring-boot:run
```

### 3. 启动前端服务
```bash
cd findwatch-frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 4. 访问应用
- **后端API**: http://localhost:8083
- **API文档**: http://localhost:8083/swagger-ui/index.html
- **前端应用**: http://localhost:5173

## API接口

### 认证接口
- `POST /api/v1/auth/simple-login` - 简单登录（测试用）
- `POST /api/v1/auth/wechat-login` - 微信小程序登录

### 用户管理
- `GET /api/v1/users` - 获取用户列表
- `GET /api/v1/users/{id}` - 获取用户详情
- `PUT /api/v1/users/{id}` - 更新用户信息

### 品牌管理
- `GET /api/v1/brands` - 获取品牌列表
- `POST /api/v1/brands` - 创建品牌
- `PUT /api/v1/brands/{id}` - 更新品牌
- `DELETE /api/v1/brands/{id}` - 删除品牌

### 手表管理
- `GET /api/v1/watches` - 获取手表列表
- `POST /api/v1/watches` - 创建手表记录
- `PUT /api/v1/watches/{id}` - 更新手表信息
- `DELETE /api/v1/watches/{id}` - 删除手表记录

## 开发说明

### 当前已知问题
1. **登录认证**: 目前使用硬编码的测试账号（admin/123456），需要集成真实的用户认证系统
2. **前端UI**: 除首页外其他页面UI需要使用ElementUI进行美化
3. **数据库**: 需要导入完整的数据库表结构和初始数据

### 待完成任务
1. 修改User实体，添加username和password字段支持传统认证
2. 集成ElementUI到Vue.js前端项目
3. 导入数据库表结构和初始数据
4. 完善API接口的错误处理和数据验证
5. 添加单元测试和集成测试

## 部署说明

### 生产环境部署
1. 确保服务器已安装Docker和Docker Compose
2. 配置生产环境的数据库和Redis连接
3. 构建Spring Boot应用JAR包
4. 使用nginx进行反向代理和静态文件服务

### 监控和日志
- 应用日志位置: `logs/findwatch.log`
- Docker容器日志: `docker logs <container_name>`
- 数据库日志: MySQL容器内的错误日志

## 联系信息
- 项目维护者: [您的姓名]
- 邮箱: [您的邮箱]
- 项目地址: [Git仓库地址]
