# 寻表记微服务化重构文档

## 概述

本文档描述了寻表记项目从传统Spring MVC架构向微服务架构的重构过程和实现细节。

## 重构目标

1. **统一API响应格式** - 实现标准化的API响应结构
2. **全局异常处理** - 集中化的错误处理机制
3. **请求追踪** - 分布式系统的请求链路追踪
4. **API文档化** - 自动生成的API文档
5. **监控和指标** - 应用性能监控和健康检查
6. **服务治理** - 微服务治理能力

## 核心组件

### 1. 统一响应格式

#### ApiResponse<T>
- **位置**: `src/main/java/com/xbj/common/ApiResponse.java`
- **功能**: 统一的API响应格式，支持泛型
- **特性**:
  - 成功/失败状态标识
  - 错误码和消息
  - 请求追踪ID
  - 时间戳

#### PageResponse<T>
- **位置**: `src/main/java/com/xbj/common/PageResponse.java`
- **功能**: 分页响应格式
- **特性**:
  - 分页元数据
  - 数据列表
  - 导航信息

#### ResultCode
- **位置**: `src/main/java/com/xbj/common/ResultCode.java`
- **功能**: 标准化错误码定义
- **分类**:
  - 系统级错误 (1xxx)
  - 用户相关错误 (2xxx)
  - 微信相关错误 (3xxx)
  - 手表相关错误 (4xxx)
  - 短信相关错误 (5xxx)
  - 缓存相关错误 (6xxx)
  - 数据库相关错误 (7xxx)

### 2. 异常处理

#### BusinessException
- **位置**: `src/main/java/com/xbj/exception/BusinessException.java`
- **功能**: 业务异常类
- **特性**:
  - 错误码集成
  - 多种构造方式
  - 链式异常支持

#### GlobalExceptionHandler
- **位置**: `src/main/java/com/xbj/exception/GlobalExceptionHandler.java`
- **功能**: 全局异常处理器
- **处理类型**:
  - 业务异常
  - 参数验证异常
  - 系统异常
  - 未知异常

### 3. 请求追踪

#### TraceInterceptor
- **位置**: `src/main/java/com/xbj/interceptor/TraceInterceptor.java`
- **功能**: 请求追踪拦截器
- **特性**:
  - 生成唯一追踪ID
  - MDC日志上下文
  - 请求响应日志

### 4. API文档

#### OpenApiConfig
- **位置**: `src/main/java/com/xbj/config/OpenApiConfig.java`
- **功能**: OpenAPI/Swagger配置
- **特性**:
  - JWT认证配置
  - 多环境支持
  - 自动文档生成

### 5. 监控和指标

#### MicroserviceConfig
- **位置**: `src/main/java/com/xbj/config/MicroserviceConfig.java`
- **功能**: 微服务配置
- **特性**:
  - 健康检查
  - 性能指标
  - 请求计时

## 重构后的控制器

### 1. AuthController
- **位置**: `src/main/java/com/xbj/controller/AuthController.java`
- **功能**: 认证管理
- **接口**:
  - `POST /api/v1/auth/login` - 微信登录
  - `POST /api/v1/auth/decrypt-phone` - 手机号解密
  - `POST /api/v1/auth/register` - 用户注册
  - `GET /api/v1/auth/verify` - 令牌验证

### 2. WatchControllerV2
- **位置**: `src/main/java/com/xbj/controller/WatchControllerV2.java`
- **功能**: 手表信息管理
- **接口**:
  - `POST /api/v1/watches/search` - 搜索手表
  - `POST /api/v1/watches/brands/{brand}/series` - 获取品牌系列
  - `GET /api/v1/watches/user/has-phone` - 检查用户手机号
  - `POST /api/v1/watches/search-by-code` - 根据编码搜索
  - `GET /api/v1/watches/search-conditions` - 获取搜索条件

### 3. UserControllerV2
- **位置**: `src/main/java/com/xbj/controller/UserControllerV2.java`
- **功能**: 用户管理
- **接口**:
  - `GET /api/v1/users/profile` - 获取用户信息
  - `PUT /api/v1/users/profile` - 更新用户信息
  - `DELETE /api/v1/users/profile` - 删除用户账户
  - `GET /api/v1/users/exists` - 检查用户存在性

### 4. HealthController
- **位置**: `src/main/java/com/xbj/controller/HealthController.java`
- **功能**: 健康检查
- **接口**:
  - `GET /health` - 简单健康检查
  - `GET /api/v1/health/check` - 详细健康检查

## 配置更新

### application.yml
主要更新内容：
- 日志格式增加追踪ID
- Actuator监控端点配置
- Prometheus指标导出
- OpenAPI文档配置

### Maven依赖
新增依赖：
- `spring-boot-starter-actuator` - 监控端点
- `springdoc-openapi-ui` - API文档
- `micrometer-registry-prometheus` - 指标收集
- `spring-boot-starter-validation` - 参数验证

## 微服务特性

### 1. 服务发现和注册
- 支持多环境配置
- 健康检查端点
- 服务元数据

### 2. 配置管理
- 外部化配置
- 环境特定配置
- 动态配置更新

### 3. 监控和观测
- 应用指标收集
- 健康状态监控
- 请求链路追踪
- 性能监控

### 4. 安全性
- JWT令牌认证
- API访问控制
- 请求验证

### 5. 容错和恢复
- 全局异常处理
- 优雅错误响应
- 服务降级准备

## API文档访问

启动应用后，可通过以下地址访问API文档：
- Swagger UI: `http://localhost:8080/swagger-ui.html`
- OpenAPI JSON: `http://localhost:8080/v3/api-docs`

## 监控端点

Actuator提供的监控端点：
- 健康检查: `http://localhost:8080/actuator/health`
- 应用信息: `http://localhost:8080/actuator/info`
- 指标数据: `http://localhost:8080/actuator/metrics`
- Prometheus: `http://localhost:8080/actuator/prometheus`

## 使用示例

### 1. 用户登录
```bash
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"wxCode": "081234567890abcdef"}'
```

### 2. 搜索手表
```bash
curl -X POST http://localhost:8080/api/v1/watches/search \
  -H "Content-Type: application/json" \
  -d '{"curentPage": 1, "pageSize": 10, "brand": "劳力士"}'
```

### 3. 获取用户信息
```bash
curl -X GET http://localhost:8080/api/v1/users/profile \
  -H "Authorization: Bearer <token>"
```

## 后续优化建议

1. **服务拆分** - 将单体应用拆分为多个微服务
2. **缓存优化** - 实现分布式缓存策略
3. **消息队列** - 引入异步消息处理
4. **服务网格** - 使用Istio等服务网格
5. **容器化** - Docker容器化部署
6. **CI/CD** - 持续集成和部署流水线

## 总结

通过本次重构，寻表记项目已经具备了微服务架构的基础特性，包括统一的API格式、全局异常处理、请求追踪、API文档化和监控能力。这为后续的服务拆分和微服务治理奠定了坚实的基础。
