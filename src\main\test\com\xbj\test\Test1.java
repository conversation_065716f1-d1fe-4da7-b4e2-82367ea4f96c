package com.xbj.test;

import com.xbj.controller.LoginController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.SecureRandom;

public class Test1 {
    private final static Logger logger = LoggerFactory.getLogger(LoginController.class);
    public static void main(String[] args) {
        /*String dateStr1 = "2017-12-12 22:14:26";
        Date date1 = DateUtil.parse(dateStr1);

        String dateStr2 = "2022-04-24 09:54:23";
        Date date2 = DateUtil.parse(dateStr2);

        //相差一个月，31天
        long betweenDay = DateUtil.between(date1, date2, DateUnit.DAY);
        System.out.println("相差天数："+betweenDay);*/
        //System.out.println(UUID.randomUUID().toString());
        String randomText = generateRandomText(10);
        System.out.println(randomText);
    }



    private static final int CHAR_COUNT = 26;
    private static final SecureRandom random = new SecureRandom();

    public static String generateRandomText(int sizeInMB) {
        StringBuilder sb = new StringBuilder(sizeInMB * 1024 * 1024);

        for (int i = 0; i < sizeInMB * 1024 * 1024 / CHAR_COUNT; i++) {
            char c = (char) ('a' + random.nextInt(CHAR_COUNT));
            sb.append(c);
        }

        return sb.toString();
    }

}
