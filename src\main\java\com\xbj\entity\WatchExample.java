package com.xbj.entity;

import java.util.ArrayList;
import java.util.List;

public class WatchExample {

    //*************************增加分页查询********************************
    private Integer startRow;

    private Integer pageSize;

    public Integer getStartRow() {
        return startRow;
    }

    public void setStartRow(Integer startRow) {
        this.startRow = startRow;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
//************************************************************

    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public WatchExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBrandIsNull() {
            addCriterion("brand is null");
            return (Criteria) this;
        }

        public Criteria andBrandIsNotNull() {
            addCriterion("brand is not null");
            return (Criteria) this;
        }

        public Criteria andBrandEqualTo(String value) {
            addCriterion("brand =", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotEqualTo(String value) {
            addCriterion("brand <>", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThan(String value) {
            addCriterion("brand >", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThanOrEqualTo(String value) {
            addCriterion("brand >=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThan(String value) {
            addCriterion("brand <", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThanOrEqualTo(String value) {
            addCriterion("brand <=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLike(String value) {
            addCriterion("brand like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotLike(String value) {
            addCriterion("brand not like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandIn(List<String> values) {
            addCriterion("brand in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotIn(List<String> values) {
            addCriterion("brand not in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandBetween(String value1, String value2) {
            addCriterion("brand between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotBetween(String value1, String value2) {
            addCriterion("brand not between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andSeriesIsNull() {
            addCriterion("series is null");
            return (Criteria) this;
        }

        public Criteria andSeriesIsNotNull() {
            addCriterion("series is not null");
            return (Criteria) this;
        }

        public Criteria andSeriesEqualTo(String value) {
            addCriterion("series =", value, "series");
            return (Criteria) this;
        }

        public Criteria andSeriesNotEqualTo(String value) {
            addCriterion("series <>", value, "series");
            return (Criteria) this;
        }

        public Criteria andSeriesGreaterThan(String value) {
            addCriterion("series >", value, "series");
            return (Criteria) this;
        }

        public Criteria andSeriesGreaterThanOrEqualTo(String value) {
            addCriterion("series >=", value, "series");
            return (Criteria) this;
        }

        public Criteria andSeriesLessThan(String value) {
            addCriterion("series <", value, "series");
            return (Criteria) this;
        }

        public Criteria andSeriesLessThanOrEqualTo(String value) {
            addCriterion("series <=", value, "series");
            return (Criteria) this;
        }

        public Criteria andSeriesLike(String value) {
            addCriterion("series like", value, "series");
            return (Criteria) this;
        }

        public Criteria andSeriesNotLike(String value) {
            addCriterion("series not like", value, "series");
            return (Criteria) this;
        }

        public Criteria andSeriesIn(List<String> values) {
            addCriterion("series in", values, "series");
            return (Criteria) this;
        }

        public Criteria andSeriesNotIn(List<String> values) {
            addCriterion("series not in", values, "series");
            return (Criteria) this;
        }

        public Criteria andSeriesBetween(String value1, String value2) {
            addCriterion("series between", value1, value2, "series");
            return (Criteria) this;
        }

        public Criteria andSeriesNotBetween(String value1, String value2) {
            addCriterion("series not between", value1, value2, "series");
            return (Criteria) this;
        }

        public Criteria andModelIsNull() {
            addCriterion("model is null");
            return (Criteria) this;
        }

        public Criteria andModelIsNotNull() {
            addCriterion("model is not null");
            return (Criteria) this;
        }

        public Criteria andModelEqualTo(String value) {
            addCriterion("model =", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotEqualTo(String value) {
            addCriterion("model <>", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThan(String value) {
            addCriterion("model >", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanOrEqualTo(String value) {
            addCriterion("model >=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThan(String value) {
            addCriterion("model <", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThanOrEqualTo(String value) {
            addCriterion("model <=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLike(String value) {
            addCriterion("model like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotLike(String value) {
            addCriterion("model not like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelIn(List<String> values) {
            addCriterion("model in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotIn(List<String> values) {
            addCriterion("model not in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelBetween(String value1, String value2) {
            addCriterion("model between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotBetween(String value1, String value2) {
            addCriterion("model not between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andSizeIsNull() {
            addCriterion("size is null");
            return (Criteria) this;
        }

        public Criteria andSizeIsNotNull() {
            addCriterion("size is not null");
            return (Criteria) this;
        }

        public Criteria andSizeEqualTo(String value) {
            addCriterion("size =", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeNotEqualTo(String value) {
            addCriterion("size <>", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeGreaterThan(String value) {
            addCriterion("size >", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeGreaterThanOrEqualTo(String value) {
            addCriterion("size >=", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeLessThan(String value) {
            addCriterion("size <", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeLessThanOrEqualTo(String value) {
            addCriterion("size <=", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeLike(String value) {
            addCriterion("size like", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeNotLike(String value) {
            addCriterion("size not like", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeIn(List<String> values) {
            addCriterion("size in", values, "size");
            return (Criteria) this;
        }

        public Criteria andSizeNotIn(List<String> values) {
            addCriterion("size not in", values, "size");
            return (Criteria) this;
        }

        public Criteria andSizeBetween(String value1, String value2) {
            addCriterion("size between", value1, value2, "size");
            return (Criteria) this;
        }

        public Criteria andSizeNotBetween(String value1, String value2) {
            addCriterion("size not between", value1, value2, "size");
            return (Criteria) this;
        }

        public Criteria andPriceInlandIsNull() {
            addCriterion("price_inland is null");
            return (Criteria) this;
        }

        public Criteria andPriceInlandIsNotNull() {
            addCriterion("price_inland is not null");
            return (Criteria) this;
        }

        public Criteria andPriceInlandEqualTo(String value) {
            addCriterion("price_inland =", value, "priceInland");
            return (Criteria) this;
        }

        public Criteria andPriceInlandNotEqualTo(String value) {
            addCriterion("price_inland <>", value, "priceInland");
            return (Criteria) this;
        }

        public Criteria andPriceInlandGreaterThan(String value) {
            addCriterion("price_inland >", value, "priceInland");
            return (Criteria) this;
        }

        public Criteria andPriceInlandGreaterThanOrEqualTo(String value) {
            addCriterion("price_inland >=", value, "priceInland");
            return (Criteria) this;
        }

        public Criteria andPriceInlandLessThan(String value) {
            addCriterion("price_inland <", value, "priceInland");
            return (Criteria) this;
        }

        public Criteria andPriceInlandLessThanOrEqualTo(String value) {
            addCriterion("price_inland <=", value, "priceInland");
            return (Criteria) this;
        }

        public Criteria andPriceInlandLike(String value) {
            addCriterion("price_inland like", value, "priceInland");
            return (Criteria) this;
        }

        public Criteria andPriceInlandNotLike(String value) {
            addCriterion("price_inland not like", value, "priceInland");
            return (Criteria) this;
        }

        public Criteria andPriceInlandIn(List<String> values) {
            addCriterion("price_inland in", values, "priceInland");
            return (Criteria) this;
        }

        public Criteria andPriceInlandNotIn(List<String> values) {
            addCriterion("price_inland not in", values, "priceInland");
            return (Criteria) this;
        }

        public Criteria andPriceInlandBetween(String value1, String value2) {
            addCriterion("price_inland between", value1, value2, "priceInland");
            return (Criteria) this;
        }

        public Criteria andPriceInlandNotBetween(String value1, String value2) {
            addCriterion("price_inland not between", value1, value2, "priceInland");
            return (Criteria) this;
        }

        public Criteria andCoreTypeIsNull() {
            addCriterion("core_type is null");
            return (Criteria) this;
        }

        public Criteria andCoreTypeIsNotNull() {
            addCriterion("core_type is not null");
            return (Criteria) this;
        }

        public Criteria andCoreTypeEqualTo(String value) {
            addCriterion("core_type =", value, "coreType");
            return (Criteria) this;
        }

        public Criteria andCoreTypeNotEqualTo(String value) {
            addCriterion("core_type <>", value, "coreType");
            return (Criteria) this;
        }

        public Criteria andCoreTypeGreaterThan(String value) {
            addCriterion("core_type >", value, "coreType");
            return (Criteria) this;
        }

        public Criteria andCoreTypeGreaterThanOrEqualTo(String value) {
            addCriterion("core_type >=", value, "coreType");
            return (Criteria) this;
        }

        public Criteria andCoreTypeLessThan(String value) {
            addCriterion("core_type <", value, "coreType");
            return (Criteria) this;
        }

        public Criteria andCoreTypeLessThanOrEqualTo(String value) {
            addCriterion("core_type <=", value, "coreType");
            return (Criteria) this;
        }

        public Criteria andCoreTypeLike(String value) {
            addCriterion("core_type like", value, "coreType");
            return (Criteria) this;
        }

        public Criteria andCoreTypeNotLike(String value) {
            addCriterion("core_type not like", value, "coreType");
            return (Criteria) this;
        }

        public Criteria andCoreTypeIn(List<String> values) {
            addCriterion("core_type in", values, "coreType");
            return (Criteria) this;
        }

        public Criteria andCoreTypeNotIn(List<String> values) {
            addCriterion("core_type not in", values, "coreType");
            return (Criteria) this;
        }

        public Criteria andCoreTypeBetween(String value1, String value2) {
            addCriterion("core_type between", value1, value2, "coreType");
            return (Criteria) this;
        }

        public Criteria andCoreTypeNotBetween(String value1, String value2) {
            addCriterion("core_type not between", value1, value2, "coreType");
            return (Criteria) this;
        }

        public Criteria andCoreModelIsNull() {
            addCriterion("core_model is null");
            return (Criteria) this;
        }

        public Criteria andCoreModelIsNotNull() {
            addCriterion("core_model is not null");
            return (Criteria) this;
        }

        public Criteria andCoreModelEqualTo(String value) {
            addCriterion("core_model =", value, "coreModel");
            return (Criteria) this;
        }

        public Criteria andCoreModelNotEqualTo(String value) {
            addCriterion("core_model <>", value, "coreModel");
            return (Criteria) this;
        }

        public Criteria andCoreModelGreaterThan(String value) {
            addCriterion("core_model >", value, "coreModel");
            return (Criteria) this;
        }

        public Criteria andCoreModelGreaterThanOrEqualTo(String value) {
            addCriterion("core_model >=", value, "coreModel");
            return (Criteria) this;
        }

        public Criteria andCoreModelLessThan(String value) {
            addCriterion("core_model <", value, "coreModel");
            return (Criteria) this;
        }

        public Criteria andCoreModelLessThanOrEqualTo(String value) {
            addCriterion("core_model <=", value, "coreModel");
            return (Criteria) this;
        }

        public Criteria andCoreModelLike(String value) {
            addCriterion("core_model like", value, "coreModel");
            return (Criteria) this;
        }

        public Criteria andCoreModelNotLike(String value) {
            addCriterion("core_model not like", value, "coreModel");
            return (Criteria) this;
        }

        public Criteria andCoreModelIn(List<String> values) {
            addCriterion("core_model in", values, "coreModel");
            return (Criteria) this;
        }

        public Criteria andCoreModelNotIn(List<String> values) {
            addCriterion("core_model not in", values, "coreModel");
            return (Criteria) this;
        }

        public Criteria andCoreModelBetween(String value1, String value2) {
            addCriterion("core_model between", value1, value2, "coreModel");
            return (Criteria) this;
        }

        public Criteria andCoreModelNotBetween(String value1, String value2) {
            addCriterion("core_model not between", value1, value2, "coreModel");
            return (Criteria) this;
        }

        public Criteria andBkTypeIsNull() {
            addCriterion("bk_type is null");
            return (Criteria) this;
        }

        public Criteria andBkTypeIsNotNull() {
            addCriterion("bk_type is not null");
            return (Criteria) this;
        }

        public Criteria andBkTypeEqualTo(String value) {
            addCriterion("bk_type =", value, "bkType");
            return (Criteria) this;
        }

        public Criteria andBkTypeNotEqualTo(String value) {
            addCriterion("bk_type <>", value, "bkType");
            return (Criteria) this;
        }

        public Criteria andBkTypeGreaterThan(String value) {
            addCriterion("bk_type >", value, "bkType");
            return (Criteria) this;
        }

        public Criteria andBkTypeGreaterThanOrEqualTo(String value) {
            addCriterion("bk_type >=", value, "bkType");
            return (Criteria) this;
        }

        public Criteria andBkTypeLessThan(String value) {
            addCriterion("bk_type <", value, "bkType");
            return (Criteria) this;
        }

        public Criteria andBkTypeLessThanOrEqualTo(String value) {
            addCriterion("bk_type <=", value, "bkType");
            return (Criteria) this;
        }

        public Criteria andBkTypeLike(String value) {
            addCriterion("bk_type like", value, "bkType");
            return (Criteria) this;
        }

        public Criteria andBkTypeNotLike(String value) {
            addCriterion("bk_type not like", value, "bkType");
            return (Criteria) this;
        }

        public Criteria andBkTypeIn(List<String> values) {
            addCriterion("bk_type in", values, "bkType");
            return (Criteria) this;
        }

        public Criteria andBkTypeNotIn(List<String> values) {
            addCriterion("bk_type not in", values, "bkType");
            return (Criteria) this;
        }

        public Criteria andBkTypeBetween(String value1, String value2) {
            addCriterion("bk_type between", value1, value2, "bkType");
            return (Criteria) this;
        }

        public Criteria andBkTypeNotBetween(String value1, String value2) {
            addCriterion("bk_type not between", value1, value2, "bkType");
            return (Criteria) this;
        }

        public Criteria andBdTypeIsNull() {
            addCriterion("bd_type is null");
            return (Criteria) this;
        }

        public Criteria andBdTypeIsNotNull() {
            addCriterion("bd_type is not null");
            return (Criteria) this;
        }

        public Criteria andBdTypeEqualTo(String value) {
            addCriterion("bd_type =", value, "bdType");
            return (Criteria) this;
        }

        public Criteria andBdTypeNotEqualTo(String value) {
            addCriterion("bd_type <>", value, "bdType");
            return (Criteria) this;
        }

        public Criteria andBdTypeGreaterThan(String value) {
            addCriterion("bd_type >", value, "bdType");
            return (Criteria) this;
        }

        public Criteria andBdTypeGreaterThanOrEqualTo(String value) {
            addCriterion("bd_type >=", value, "bdType");
            return (Criteria) this;
        }

        public Criteria andBdTypeLessThan(String value) {
            addCriterion("bd_type <", value, "bdType");
            return (Criteria) this;
        }

        public Criteria andBdTypeLessThanOrEqualTo(String value) {
            addCriterion("bd_type <=", value, "bdType");
            return (Criteria) this;
        }

        public Criteria andBdTypeLike(String value) {
            addCriterion("bd_type like", value, "bdType");
            return (Criteria) this;
        }

        public Criteria andBdTypeNotLike(String value) {
            addCriterion("bd_type not like", value, "bdType");
            return (Criteria) this;
        }

        public Criteria andBdTypeIn(List<String> values) {
            addCriterion("bd_type in", values, "bdType");
            return (Criteria) this;
        }

        public Criteria andBdTypeNotIn(List<String> values) {
            addCriterion("bd_type not in", values, "bdType");
            return (Criteria) this;
        }

        public Criteria andBdTypeBetween(String value1, String value2) {
            addCriterion("bd_type between", value1, value2, "bdType");
            return (Criteria) this;
        }

        public Criteria andBdTypeNotBetween(String value1, String value2) {
            addCriterion("bd_type not between", value1, value2, "bdType");
            return (Criteria) this;
        }

        public Criteria andBkHorSizeIsNull() {
            addCriterion("bk_hor_size is null");
            return (Criteria) this;
        }

        public Criteria andBkHorSizeIsNotNull() {
            addCriterion("bk_hor_size is not null");
            return (Criteria) this;
        }

        public Criteria andBkHorSizeEqualTo(String value) {
            addCriterion("bk_hor_size =", value, "bkHorSize");
            return (Criteria) this;
        }

        public Criteria andBkHorSizeNotEqualTo(String value) {
            addCriterion("bk_hor_size <>", value, "bkHorSize");
            return (Criteria) this;
        }

        public Criteria andBkHorSizeGreaterThan(String value) {
            addCriterion("bk_hor_size >", value, "bkHorSize");
            return (Criteria) this;
        }

        public Criteria andBkHorSizeGreaterThanOrEqualTo(String value) {
            addCriterion("bk_hor_size >=", value, "bkHorSize");
            return (Criteria) this;
        }

        public Criteria andBkHorSizeLessThan(String value) {
            addCriterion("bk_hor_size <", value, "bkHorSize");
            return (Criteria) this;
        }

        public Criteria andBkHorSizeLessThanOrEqualTo(String value) {
            addCriterion("bk_hor_size <=", value, "bkHorSize");
            return (Criteria) this;
        }

        public Criteria andBkHorSizeLike(String value) {
            addCriterion("bk_hor_size like", value, "bkHorSize");
            return (Criteria) this;
        }

        public Criteria andBkHorSizeNotLike(String value) {
            addCriterion("bk_hor_size not like", value, "bkHorSize");
            return (Criteria) this;
        }

        public Criteria andBkHorSizeIn(List<String> values) {
            addCriterion("bk_hor_size in", values, "bkHorSize");
            return (Criteria) this;
        }

        public Criteria andBkHorSizeNotIn(List<String> values) {
            addCriterion("bk_hor_size not in", values, "bkHorSize");
            return (Criteria) this;
        }

        public Criteria andBkHorSizeBetween(String value1, String value2) {
            addCriterion("bk_hor_size between", value1, value2, "bkHorSize");
            return (Criteria) this;
        }

        public Criteria andBkHorSizeNotBetween(String value1, String value2) {
            addCriterion("bk_hor_size not between", value1, value2, "bkHorSize");
            return (Criteria) this;
        }

        public Criteria andBpColorIsNull() {
            addCriterion("bp_color is null");
            return (Criteria) this;
        }

        public Criteria andBpColorIsNotNull() {
            addCriterion("bp_color is not null");
            return (Criteria) this;
        }

        public Criteria andBpColorEqualTo(String value) {
            addCriterion("bp_color =", value, "bpColor");
            return (Criteria) this;
        }

        public Criteria andBpColorNotEqualTo(String value) {
            addCriterion("bp_color <>", value, "bpColor");
            return (Criteria) this;
        }

        public Criteria andBpColorGreaterThan(String value) {
            addCriterion("bp_color >", value, "bpColor");
            return (Criteria) this;
        }

        public Criteria andBpColorGreaterThanOrEqualTo(String value) {
            addCriterion("bp_color >=", value, "bpColor");
            return (Criteria) this;
        }

        public Criteria andBpColorLessThan(String value) {
            addCriterion("bp_color <", value, "bpColor");
            return (Criteria) this;
        }

        public Criteria andBpColorLessThanOrEqualTo(String value) {
            addCriterion("bp_color <=", value, "bpColor");
            return (Criteria) this;
        }

        public Criteria andBpColorLike(String value) {
            addCriterion("bp_color like", value, "bpColor");
            return (Criteria) this;
        }

        public Criteria andBpColorNotLike(String value) {
            addCriterion("bp_color not like", value, "bpColor");
            return (Criteria) this;
        }

        public Criteria andBpColorIn(List<String> values) {
            addCriterion("bp_color in", values, "bpColor");
            return (Criteria) this;
        }

        public Criteria andBpColorNotIn(List<String> values) {
            addCriterion("bp_color not in", values, "bpColor");
            return (Criteria) this;
        }

        public Criteria andBpColorBetween(String value1, String value2) {
            addCriterion("bp_color between", value1, value2, "bpColor");
            return (Criteria) this;
        }

        public Criteria andBpColorNotBetween(String value1, String value2) {
            addCriterion("bp_color not between", value1, value2, "bpColor");
            return (Criteria) this;
        }

        public Criteria andKsIsNull() {
            addCriterion("ks is null");
            return (Criteria) this;
        }

        public Criteria andKsIsNotNull() {
            addCriterion("ks is not null");
            return (Criteria) this;
        }

        public Criteria andKsEqualTo(String value) {
            addCriterion("ks =", value, "ks");
            return (Criteria) this;
        }

        public Criteria andKsNotEqualTo(String value) {
            addCriterion("ks <>", value, "ks");
            return (Criteria) this;
        }

        public Criteria andKsGreaterThan(String value) {
            addCriterion("ks >", value, "ks");
            return (Criteria) this;
        }

        public Criteria andKsGreaterThanOrEqualTo(String value) {
            addCriterion("ks >=", value, "ks");
            return (Criteria) this;
        }

        public Criteria andKsLessThan(String value) {
            addCriterion("ks <", value, "ks");
            return (Criteria) this;
        }

        public Criteria andKsLessThanOrEqualTo(String value) {
            addCriterion("ks <=", value, "ks");
            return (Criteria) this;
        }

        public Criteria andKsLike(String value) {
            addCriterion("ks like", value, "ks");
            return (Criteria) this;
        }

        public Criteria andKsNotLike(String value) {
            addCriterion("ks not like", value, "ks");
            return (Criteria) this;
        }

        public Criteria andKsIn(List<String> values) {
            addCriterion("ks in", values, "ks");
            return (Criteria) this;
        }

        public Criteria andKsNotIn(List<String> values) {
            addCriterion("ks not in", values, "ks");
            return (Criteria) this;
        }

        public Criteria andKsBetween(String value1, String value2) {
            addCriterion("ks between", value1, value2, "ks");
            return (Criteria) this;
        }

        public Criteria andKsNotBetween(String value1, String value2) {
            addCriterion("ks not between", value1, value2, "ks");
            return (Criteria) this;
        }

        public Criteria andFbDateIsNull() {
            addCriterion("fb_date is null");
            return (Criteria) this;
        }

        public Criteria andFbDateIsNotNull() {
            addCriterion("fb_date is not null");
            return (Criteria) this;
        }

        public Criteria andFbDateEqualTo(String value) {
            addCriterion("fb_date =", value, "fbDate");
            return (Criteria) this;
        }

        public Criteria andFbDateNotEqualTo(String value) {
            addCriterion("fb_date <>", value, "fbDate");
            return (Criteria) this;
        }

        public Criteria andFbDateGreaterThan(String value) {
            addCriterion("fb_date >", value, "fbDate");
            return (Criteria) this;
        }

        public Criteria andFbDateGreaterThanOrEqualTo(String value) {
            addCriterion("fb_date >=", value, "fbDate");
            return (Criteria) this;
        }

        public Criteria andFbDateLessThan(String value) {
            addCriterion("fb_date <", value, "fbDate");
            return (Criteria) this;
        }

        public Criteria andFbDateLessThanOrEqualTo(String value) {
            addCriterion("fb_date <=", value, "fbDate");
            return (Criteria) this;
        }

        public Criteria andFbDateLike(String value) {
            addCriterion("fb_date like", value, "fbDate");
            return (Criteria) this;
        }

        public Criteria andFbDateNotLike(String value) {
            addCriterion("fb_date not like", value, "fbDate");
            return (Criteria) this;
        }

        public Criteria andFbDateIn(List<String> values) {
            addCriterion("fb_date in", values, "fbDate");
            return (Criteria) this;
        }

        public Criteria andFbDateNotIn(List<String> values) {
            addCriterion("fb_date not in", values, "fbDate");
            return (Criteria) this;
        }

        public Criteria andFbDateBetween(String value1, String value2) {
            addCriterion("fb_date between", value1, value2, "fbDate");
            return (Criteria) this;
        }

        public Criteria andFbDateNotBetween(String value1, String value2) {
            addCriterion("fb_date not between", value1, value2, "fbDate");
            return (Criteria) this;
        }

        public Criteria andFzFunIsNull() {
            addCriterion("fz_fun is null");
            return (Criteria) this;
        }

        public Criteria andFzFunIsNotNull() {
            addCriterion("fz_fun is not null");
            return (Criteria) this;
        }

        public Criteria andFzFunEqualTo(String value) {
            addCriterion("fz_fun =", value, "fzFun");
            return (Criteria) this;
        }

        public Criteria andFzFunNotEqualTo(String value) {
            addCriterion("fz_fun <>", value, "fzFun");
            return (Criteria) this;
        }

        public Criteria andFzFunGreaterThan(String value) {
            addCriterion("fz_fun >", value, "fzFun");
            return (Criteria) this;
        }

        public Criteria andFzFunGreaterThanOrEqualTo(String value) {
            addCriterion("fz_fun >=", value, "fzFun");
            return (Criteria) this;
        }

        public Criteria andFzFunLessThan(String value) {
            addCriterion("fz_fun <", value, "fzFun");
            return (Criteria) this;
        }

        public Criteria andFzFunLessThanOrEqualTo(String value) {
            addCriterion("fz_fun <=", value, "fzFun");
            return (Criteria) this;
        }

        public Criteria andFzFunLike(String value) {
            addCriterion("fz_fun like", value, "fzFun");
            return (Criteria) this;
        }

        public Criteria andFzFunNotLike(String value) {
            addCriterion("fz_fun not like", value, "fzFun");
            return (Criteria) this;
        }

        public Criteria andFzFunIn(List<String> values) {
            addCriterion("fz_fun in", values, "fzFun");
            return (Criteria) this;
        }

        public Criteria andFzFunNotIn(List<String> values) {
            addCriterion("fz_fun not in", values, "fzFun");
            return (Criteria) this;
        }

        public Criteria andFzFunBetween(String value1, String value2) {
            addCriterion("fz_fun between", value1, value2, "fzFun");
            return (Criteria) this;
        }

        public Criteria andFzFunNotBetween(String value1, String value2) {
            addCriterion("fz_fun not between", value1, value2, "fzFun");
            return (Criteria) this;
        }

        public Criteria andWatchUrlIsNull() {
            addCriterion("watch_url is null");
            return (Criteria) this;
        }

        public Criteria andWatchUrlIsNotNull() {
            addCriterion("watch_url is not null");
            return (Criteria) this;
        }

        public Criteria andWatchUrlEqualTo(String value) {
            addCriterion("watch_url =", value, "watchUrl");
            return (Criteria) this;
        }

        public Criteria andWatchUrlNotEqualTo(String value) {
            addCriterion("watch_url <>", value, "watchUrl");
            return (Criteria) this;
        }

        public Criteria andWatchUrlGreaterThan(String value) {
            addCriterion("watch_url >", value, "watchUrl");
            return (Criteria) this;
        }

        public Criteria andWatchUrlGreaterThanOrEqualTo(String value) {
            addCriterion("watch_url >=", value, "watchUrl");
            return (Criteria) this;
        }

        public Criteria andWatchUrlLessThan(String value) {
            addCriterion("watch_url <", value, "watchUrl");
            return (Criteria) this;
        }

        public Criteria andWatchUrlLessThanOrEqualTo(String value) {
            addCriterion("watch_url <=", value, "watchUrl");
            return (Criteria) this;
        }

        public Criteria andWatchUrlLike(String value) {
            addCriterion("watch_url like", value, "watchUrl");
            return (Criteria) this;
        }

        public Criteria andWatchUrlNotLike(String value) {
            addCriterion("watch_url not like", value, "watchUrl");
            return (Criteria) this;
        }

        public Criteria andWatchUrlIn(List<String> values) {
            addCriterion("watch_url in", values, "watchUrl");
            return (Criteria) this;
        }

        public Criteria andWatchUrlNotIn(List<String> values) {
            addCriterion("watch_url not in", values, "watchUrl");
            return (Criteria) this;
        }

        public Criteria andWatchUrlBetween(String value1, String value2) {
            addCriterion("watch_url between", value1, value2, "watchUrl");
            return (Criteria) this;
        }

        public Criteria andWatchUrlNotBetween(String value1, String value2) {
            addCriterion("watch_url not between", value1, value2, "watchUrl");
            return (Criteria) this;
        }

        public Criteria andBkouTypeIsNull() {
            addCriterion("bkou_type is null");
            return (Criteria) this;
        }

        public Criteria andBkouTypeIsNotNull() {
            addCriterion("bkou_type is not null");
            return (Criteria) this;
        }

        public Criteria andBkouTypeEqualTo(String value) {
            addCriterion("bkou_type =", value, "bkouType");
            return (Criteria) this;
        }

        public Criteria andBkouTypeNotEqualTo(String value) {
            addCriterion("bkou_type <>", value, "bkouType");
            return (Criteria) this;
        }

        public Criteria andBkouTypeGreaterThan(String value) {
            addCriterion("bkou_type >", value, "bkouType");
            return (Criteria) this;
        }

        public Criteria andBkouTypeGreaterThanOrEqualTo(String value) {
            addCriterion("bkou_type >=", value, "bkouType");
            return (Criteria) this;
        }

        public Criteria andBkouTypeLessThan(String value) {
            addCriterion("bkou_type <", value, "bkouType");
            return (Criteria) this;
        }

        public Criteria andBkouTypeLessThanOrEqualTo(String value) {
            addCriterion("bkou_type <=", value, "bkouType");
            return (Criteria) this;
        }

        public Criteria andBkouTypeLike(String value) {
            addCriterion("bkou_type like", value, "bkouType");
            return (Criteria) this;
        }

        public Criteria andBkouTypeNotLike(String value) {
            addCriterion("bkou_type not like", value, "bkouType");
            return (Criteria) this;
        }

        public Criteria andBkouTypeIn(List<String> values) {
            addCriterion("bkou_type in", values, "bkouType");
            return (Criteria) this;
        }

        public Criteria andBkouTypeNotIn(List<String> values) {
            addCriterion("bkou_type not in", values, "bkouType");
            return (Criteria) this;
        }

        public Criteria andBkouTypeBetween(String value1, String value2) {
            addCriterion("bkou_type between", value1, value2, "bkouType");
            return (Criteria) this;
        }

        public Criteria andBkouTypeNotBetween(String value1, String value2) {
            addCriterion("bkou_type not between", value1, value2, "bkouType");
            return (Criteria) this;
        }

        public Criteria andUseHoursIsNull() {
            addCriterion("use_hours is null");
            return (Criteria) this;
        }

        public Criteria andUseHoursIsNotNull() {
            addCriterion("use_hours is not null");
            return (Criteria) this;
        }

        public Criteria andUseHoursEqualTo(String value) {
            addCriterion("use_hours =", value, "useHours");
            return (Criteria) this;
        }

        public Criteria andUseHoursNotEqualTo(String value) {
            addCriterion("use_hours <>", value, "useHours");
            return (Criteria) this;
        }

        public Criteria andUseHoursGreaterThan(String value) {
            addCriterion("use_hours >", value, "useHours");
            return (Criteria) this;
        }

        public Criteria andUseHoursGreaterThanOrEqualTo(String value) {
            addCriterion("use_hours >=", value, "useHours");
            return (Criteria) this;
        }

        public Criteria andUseHoursLessThan(String value) {
            addCriterion("use_hours <", value, "useHours");
            return (Criteria) this;
        }

        public Criteria andUseHoursLessThanOrEqualTo(String value) {
            addCriterion("use_hours <=", value, "useHours");
            return (Criteria) this;
        }

        public Criteria andUseHoursLike(String value) {
            addCriterion("use_hours like", value, "useHours");
            return (Criteria) this;
        }

        public Criteria andUseHoursNotLike(String value) {
            addCriterion("use_hours not like", value, "useHours");
            return (Criteria) this;
        }

        public Criteria andUseHoursIn(List<String> values) {
            addCriterion("use_hours in", values, "useHours");
            return (Criteria) this;
        }

        public Criteria andUseHoursNotIn(List<String> values) {
            addCriterion("use_hours not in", values, "useHours");
            return (Criteria) this;
        }

        public Criteria andUseHoursBetween(String value1, String value2) {
            addCriterion("use_hours between", value1, value2, "useHours");
            return (Criteria) this;
        }

        public Criteria andUseHoursNotBetween(String value1, String value2) {
            addCriterion("use_hours not between", value1, value2, "useHours");
            return (Criteria) this;
        }

        public Criteria andWaterDepthIsNull() {
            addCriterion("water_depth is null");
            return (Criteria) this;
        }

        public Criteria andWaterDepthIsNotNull() {
            addCriterion("water_depth is not null");
            return (Criteria) this;
        }

        public Criteria andWaterDepthEqualTo(String value) {
            addCriterion("water_depth =", value, "waterDepth");
            return (Criteria) this;
        }

        public Criteria andWaterDepthNotEqualTo(String value) {
            addCriterion("water_depth <>", value, "waterDepth");
            return (Criteria) this;
        }

        public Criteria andWaterDepthGreaterThan(String value) {
            addCriterion("water_depth >", value, "waterDepth");
            return (Criteria) this;
        }

        public Criteria andWaterDepthGreaterThanOrEqualTo(String value) {
            addCriterion("water_depth >=", value, "waterDepth");
            return (Criteria) this;
        }

        public Criteria andWaterDepthLessThan(String value) {
            addCriterion("water_depth <", value, "waterDepth");
            return (Criteria) this;
        }

        public Criteria andWaterDepthLessThanOrEqualTo(String value) {
            addCriterion("water_depth <=", value, "waterDepth");
            return (Criteria) this;
        }

        public Criteria andWaterDepthLike(String value) {
            addCriterion("water_depth like", value, "waterDepth");
            return (Criteria) this;
        }

        public Criteria andWaterDepthNotLike(String value) {
            addCriterion("water_depth not like", value, "waterDepth");
            return (Criteria) this;
        }

        public Criteria andWaterDepthIn(List<String> values) {
            addCriterion("water_depth in", values, "waterDepth");
            return (Criteria) this;
        }

        public Criteria andWaterDepthNotIn(List<String> values) {
            addCriterion("water_depth not in", values, "waterDepth");
            return (Criteria) this;
        }

        public Criteria andWaterDepthBetween(String value1, String value2) {
            addCriterion("water_depth between", value1, value2, "waterDepth");
            return (Criteria) this;
        }

        public Criteria andWaterDepthNotBetween(String value1, String value2) {
            addCriterion("water_depth not between", value1, value2, "waterDepth");
            return (Criteria) this;
        }

        public Criteria andPriceInHkIsNull() {
            addCriterion("price_in_hk is null");
            return (Criteria) this;
        }

        public Criteria andPriceInHkIsNotNull() {
            addCriterion("price_in_hk is not null");
            return (Criteria) this;
        }

        public Criteria andPriceInHkEqualTo(String value) {
            addCriterion("price_in_hk =", value, "priceInHk");
            return (Criteria) this;
        }

        public Criteria andPriceInHkNotEqualTo(String value) {
            addCriterion("price_in_hk <>", value, "priceInHk");
            return (Criteria) this;
        }

        public Criteria andPriceInHkGreaterThan(String value) {
            addCriterion("price_in_hk >", value, "priceInHk");
            return (Criteria) this;
        }

        public Criteria andPriceInHkGreaterThanOrEqualTo(String value) {
            addCriterion("price_in_hk >=", value, "priceInHk");
            return (Criteria) this;
        }

        public Criteria andPriceInHkLessThan(String value) {
            addCriterion("price_in_hk <", value, "priceInHk");
            return (Criteria) this;
        }

        public Criteria andPriceInHkLessThanOrEqualTo(String value) {
            addCriterion("price_in_hk <=", value, "priceInHk");
            return (Criteria) this;
        }

        public Criteria andPriceInHkLike(String value) {
            addCriterion("price_in_hk like", value, "priceInHk");
            return (Criteria) this;
        }

        public Criteria andPriceInHkNotLike(String value) {
            addCriterion("price_in_hk not like", value, "priceInHk");
            return (Criteria) this;
        }

        public Criteria andPriceInHkIn(List<String> values) {
            addCriterion("price_in_hk in", values, "priceInHk");
            return (Criteria) this;
        }

        public Criteria andPriceInHkNotIn(List<String> values) {
            addCriterion("price_in_hk not in", values, "priceInHk");
            return (Criteria) this;
        }

        public Criteria andPriceInHkBetween(String value1, String value2) {
            addCriterion("price_in_hk between", value1, value2, "priceInHk");
            return (Criteria) this;
        }

        public Criteria andPriceInHkNotBetween(String value1, String value2) {
            addCriterion("price_in_hk not between", value1, value2, "priceInHk");
            return (Criteria) this;
        }

        public Criteria andPriceInItalyIsNull() {
            addCriterion("price_in_italy is null");
            return (Criteria) this;
        }

        public Criteria andPriceInItalyIsNotNull() {
            addCriterion("price_in_italy is not null");
            return (Criteria) this;
        }

        public Criteria andPriceInItalyEqualTo(String value) {
            addCriterion("price_in_italy =", value, "priceInItaly");
            return (Criteria) this;
        }

        public Criteria andPriceInItalyNotEqualTo(String value) {
            addCriterion("price_in_italy <>", value, "priceInItaly");
            return (Criteria) this;
        }

        public Criteria andPriceInItalyGreaterThan(String value) {
            addCriterion("price_in_italy >", value, "priceInItaly");
            return (Criteria) this;
        }

        public Criteria andPriceInItalyGreaterThanOrEqualTo(String value) {
            addCriterion("price_in_italy >=", value, "priceInItaly");
            return (Criteria) this;
        }

        public Criteria andPriceInItalyLessThan(String value) {
            addCriterion("price_in_italy <", value, "priceInItaly");
            return (Criteria) this;
        }

        public Criteria andPriceInItalyLessThanOrEqualTo(String value) {
            addCriterion("price_in_italy <=", value, "priceInItaly");
            return (Criteria) this;
        }

        public Criteria andPriceInItalyLike(String value) {
            addCriterion("price_in_italy like", value, "priceInItaly");
            return (Criteria) this;
        }

        public Criteria andPriceInItalyNotLike(String value) {
            addCriterion("price_in_italy not like", value, "priceInItaly");
            return (Criteria) this;
        }

        public Criteria andPriceInItalyIn(List<String> values) {
            addCriterion("price_in_italy in", values, "priceInItaly");
            return (Criteria) this;
        }

        public Criteria andPriceInItalyNotIn(List<String> values) {
            addCriterion("price_in_italy not in", values, "priceInItaly");
            return (Criteria) this;
        }

        public Criteria andPriceInItalyBetween(String value1, String value2) {
            addCriterion("price_in_italy between", value1, value2, "priceInItaly");
            return (Criteria) this;
        }

        public Criteria andPriceInItalyNotBetween(String value1, String value2) {
            addCriterion("price_in_italy not between", value1, value2, "priceInItaly");
            return (Criteria) this;
        }

        public Criteria andPriceInFranceIsNull() {
            addCriterion("price_in_france is null");
            return (Criteria) this;
        }

        public Criteria andPriceInFranceIsNotNull() {
            addCriterion("price_in_france is not null");
            return (Criteria) this;
        }

        public Criteria andPriceInFranceEqualTo(String value) {
            addCriterion("price_in_france =", value, "priceInFrance");
            return (Criteria) this;
        }

        public Criteria andPriceInFranceNotEqualTo(String value) {
            addCriterion("price_in_france <>", value, "priceInFrance");
            return (Criteria) this;
        }

        public Criteria andPriceInFranceGreaterThan(String value) {
            addCriterion("price_in_france >", value, "priceInFrance");
            return (Criteria) this;
        }

        public Criteria andPriceInFranceGreaterThanOrEqualTo(String value) {
            addCriterion("price_in_france >=", value, "priceInFrance");
            return (Criteria) this;
        }

        public Criteria andPriceInFranceLessThan(String value) {
            addCriterion("price_in_france <", value, "priceInFrance");
            return (Criteria) this;
        }

        public Criteria andPriceInFranceLessThanOrEqualTo(String value) {
            addCriterion("price_in_france <=", value, "priceInFrance");
            return (Criteria) this;
        }

        public Criteria andPriceInFranceLike(String value) {
            addCriterion("price_in_france like", value, "priceInFrance");
            return (Criteria) this;
        }

        public Criteria andPriceInFranceNotLike(String value) {
            addCriterion("price_in_france not like", value, "priceInFrance");
            return (Criteria) this;
        }

        public Criteria andPriceInFranceIn(List<String> values) {
            addCriterion("price_in_france in", values, "priceInFrance");
            return (Criteria) this;
        }

        public Criteria andPriceInFranceNotIn(List<String> values) {
            addCriterion("price_in_france not in", values, "priceInFrance");
            return (Criteria) this;
        }

        public Criteria andPriceInFranceBetween(String value1, String value2) {
            addCriterion("price_in_france between", value1, value2, "priceInFrance");
            return (Criteria) this;
        }

        public Criteria andPriceInFranceNotBetween(String value1, String value2) {
            addCriterion("price_in_france not between", value1, value2, "priceInFrance");
            return (Criteria) this;
        }

        public Criteria andPriceInSpainIsNull() {
            addCriterion("price_in_spain is null");
            return (Criteria) this;
        }

        public Criteria andPriceInSpainIsNotNull() {
            addCriterion("price_in_spain is not null");
            return (Criteria) this;
        }

        public Criteria andPriceInSpainEqualTo(String value) {
            addCriterion("price_in_spain =", value, "priceInSpain");
            return (Criteria) this;
        }

        public Criteria andPriceInSpainNotEqualTo(String value) {
            addCriterion("price_in_spain <>", value, "priceInSpain");
            return (Criteria) this;
        }

        public Criteria andPriceInSpainGreaterThan(String value) {
            addCriterion("price_in_spain >", value, "priceInSpain");
            return (Criteria) this;
        }

        public Criteria andPriceInSpainGreaterThanOrEqualTo(String value) {
            addCriterion("price_in_spain >=", value, "priceInSpain");
            return (Criteria) this;
        }

        public Criteria andPriceInSpainLessThan(String value) {
            addCriterion("price_in_spain <", value, "priceInSpain");
            return (Criteria) this;
        }

        public Criteria andPriceInSpainLessThanOrEqualTo(String value) {
            addCriterion("price_in_spain <=", value, "priceInSpain");
            return (Criteria) this;
        }

        public Criteria andPriceInSpainLike(String value) {
            addCriterion("price_in_spain like", value, "priceInSpain");
            return (Criteria) this;
        }

        public Criteria andPriceInSpainNotLike(String value) {
            addCriterion("price_in_spain not like", value, "priceInSpain");
            return (Criteria) this;
        }

        public Criteria andPriceInSpainIn(List<String> values) {
            addCriterion("price_in_spain in", values, "priceInSpain");
            return (Criteria) this;
        }

        public Criteria andPriceInSpainNotIn(List<String> values) {
            addCriterion("price_in_spain not in", values, "priceInSpain");
            return (Criteria) this;
        }

        public Criteria andPriceInSpainBetween(String value1, String value2) {
            addCriterion("price_in_spain between", value1, value2, "priceInSpain");
            return (Criteria) this;
        }

        public Criteria andPriceInSpainNotBetween(String value1, String value2) {
            addCriterion("price_in_spain not between", value1, value2, "priceInSpain");
            return (Criteria) this;
        }

        public Criteria andPriceInSwitzerlandIsNull() {
            addCriterion("price_in_switzerland is null");
            return (Criteria) this;
        }

        public Criteria andPriceInSwitzerlandIsNotNull() {
            addCriterion("price_in_switzerland is not null");
            return (Criteria) this;
        }

        public Criteria andPriceInSwitzerlandEqualTo(String value) {
            addCriterion("price_in_switzerland =", value, "priceInSwitzerland");
            return (Criteria) this;
        }

        public Criteria andPriceInSwitzerlandNotEqualTo(String value) {
            addCriterion("price_in_switzerland <>", value, "priceInSwitzerland");
            return (Criteria) this;
        }

        public Criteria andPriceInSwitzerlandGreaterThan(String value) {
            addCriterion("price_in_switzerland >", value, "priceInSwitzerland");
            return (Criteria) this;
        }

        public Criteria andPriceInSwitzerlandGreaterThanOrEqualTo(String value) {
            addCriterion("price_in_switzerland >=", value, "priceInSwitzerland");
            return (Criteria) this;
        }

        public Criteria andPriceInSwitzerlandLessThan(String value) {
            addCriterion("price_in_switzerland <", value, "priceInSwitzerland");
            return (Criteria) this;
        }

        public Criteria andPriceInSwitzerlandLessThanOrEqualTo(String value) {
            addCriterion("price_in_switzerland <=", value, "priceInSwitzerland");
            return (Criteria) this;
        }

        public Criteria andPriceInSwitzerlandLike(String value) {
            addCriterion("price_in_switzerland like", value, "priceInSwitzerland");
            return (Criteria) this;
        }

        public Criteria andPriceInSwitzerlandNotLike(String value) {
            addCriterion("price_in_switzerland not like", value, "priceInSwitzerland");
            return (Criteria) this;
        }

        public Criteria andPriceInSwitzerlandIn(List<String> values) {
            addCriterion("price_in_switzerland in", values, "priceInSwitzerland");
            return (Criteria) this;
        }

        public Criteria andPriceInSwitzerlandNotIn(List<String> values) {
            addCriterion("price_in_switzerland not in", values, "priceInSwitzerland");
            return (Criteria) this;
        }

        public Criteria andPriceInSwitzerlandBetween(String value1, String value2) {
            addCriterion("price_in_switzerland between", value1, value2, "priceInSwitzerland");
            return (Criteria) this;
        }

        public Criteria andPriceInSwitzerlandNotBetween(String value1, String value2) {
            addCriterion("price_in_switzerland not between", value1, value2, "priceInSwitzerland");
            return (Criteria) this;
        }

        public Criteria andPriceInGermanyIsNull() {
            addCriterion("price_in_germany is null");
            return (Criteria) this;
        }

        public Criteria andPriceInGermanyIsNotNull() {
            addCriterion("price_in_germany is not null");
            return (Criteria) this;
        }

        public Criteria andPriceInGermanyEqualTo(String value) {
            addCriterion("price_in_germany =", value, "priceInGermany");
            return (Criteria) this;
        }

        public Criteria andPriceInGermanyNotEqualTo(String value) {
            addCriterion("price_in_germany <>", value, "priceInGermany");
            return (Criteria) this;
        }

        public Criteria andPriceInGermanyGreaterThan(String value) {
            addCriterion("price_in_germany >", value, "priceInGermany");
            return (Criteria) this;
        }

        public Criteria andPriceInGermanyGreaterThanOrEqualTo(String value) {
            addCriterion("price_in_germany >=", value, "priceInGermany");
            return (Criteria) this;
        }

        public Criteria andPriceInGermanyLessThan(String value) {
            addCriterion("price_in_germany <", value, "priceInGermany");
            return (Criteria) this;
        }

        public Criteria andPriceInGermanyLessThanOrEqualTo(String value) {
            addCriterion("price_in_germany <=", value, "priceInGermany");
            return (Criteria) this;
        }

        public Criteria andPriceInGermanyLike(String value) {
            addCriterion("price_in_germany like", value, "priceInGermany");
            return (Criteria) this;
        }

        public Criteria andPriceInGermanyNotLike(String value) {
            addCriterion("price_in_germany not like", value, "priceInGermany");
            return (Criteria) this;
        }

        public Criteria andPriceInGermanyIn(List<String> values) {
            addCriterion("price_in_germany in", values, "priceInGermany");
            return (Criteria) this;
        }

        public Criteria andPriceInGermanyNotIn(List<String> values) {
            addCriterion("price_in_germany not in", values, "priceInGermany");
            return (Criteria) this;
        }

        public Criteria andPriceInGermanyBetween(String value1, String value2) {
            addCriterion("price_in_germany between", value1, value2, "priceInGermany");
            return (Criteria) this;
        }

        public Criteria andPriceInGermanyNotBetween(String value1, String value2) {
            addCriterion("price_in_germany not between", value1, value2, "priceInGermany");
            return (Criteria) this;
        }

        public Criteria andPriceInHollandIsNull() {
            addCriterion("price_in_holland is null");
            return (Criteria) this;
        }

        public Criteria andPriceInHollandIsNotNull() {
            addCriterion("price_in_holland is not null");
            return (Criteria) this;
        }

        public Criteria andPriceInHollandEqualTo(String value) {
            addCriterion("price_in_holland =", value, "priceInHolland");
            return (Criteria) this;
        }

        public Criteria andPriceInHollandNotEqualTo(String value) {
            addCriterion("price_in_holland <>", value, "priceInHolland");
            return (Criteria) this;
        }

        public Criteria andPriceInHollandGreaterThan(String value) {
            addCriterion("price_in_holland >", value, "priceInHolland");
            return (Criteria) this;
        }

        public Criteria andPriceInHollandGreaterThanOrEqualTo(String value) {
            addCriterion("price_in_holland >=", value, "priceInHolland");
            return (Criteria) this;
        }

        public Criteria andPriceInHollandLessThan(String value) {
            addCriterion("price_in_holland <", value, "priceInHolland");
            return (Criteria) this;
        }

        public Criteria andPriceInHollandLessThanOrEqualTo(String value) {
            addCriterion("price_in_holland <=", value, "priceInHolland");
            return (Criteria) this;
        }

        public Criteria andPriceInHollandLike(String value) {
            addCriterion("price_in_holland like", value, "priceInHolland");
            return (Criteria) this;
        }

        public Criteria andPriceInHollandNotLike(String value) {
            addCriterion("price_in_holland not like", value, "priceInHolland");
            return (Criteria) this;
        }

        public Criteria andPriceInHollandIn(List<String> values) {
            addCriterion("price_in_holland in", values, "priceInHolland");
            return (Criteria) this;
        }

        public Criteria andPriceInHollandNotIn(List<String> values) {
            addCriterion("price_in_holland not in", values, "priceInHolland");
            return (Criteria) this;
        }

        public Criteria andPriceInHollandBetween(String value1, String value2) {
            addCriterion("price_in_holland between", value1, value2, "priceInHolland");
            return (Criteria) this;
        }

        public Criteria andPriceInHollandNotBetween(String value1, String value2) {
            addCriterion("price_in_holland not between", value1, value2, "priceInHolland");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}