package com.xbj.test;

public class StackTest<T> {
    private Element<T> base;
    private Element<T> top;

    static class Element<T> {
        public T data;
        Element<T> next;
    }

    public void init() {
        Element<T> element = new Element<>();
        base = element;
        top = element;
    }

    public void push(T obj) {
        Element<T> element = new Element<>();
        element.data = obj;
        element.next = top;
        top = element;
    }

    public T pop() {
        if (isEmpty()) {
            return null;
        }
        T t = top.data;
        top = top.next;
        return t;
    }

    public boolean isEmpty() {
        return base == top;
    }

    public void clear() {
        while (!isEmpty()) {
            pop();
        }
    }

    /*获取栈顶元素*/
    public Element<T> getTop() {
        return top;
    }

    public void print() {
        if (this.isEmpty()) {
            System.out.println("输出失败，栈为空！");
        } else {
            Element<T> flag = top;
            System.out.print("栈中元素为：");
            while (flag != base) {
                System.out.print(flag.data + " ");
                flag = flag.next;
            }
        }
    }

    public static void main(String[] args) {
        StackTest<String> stackTest = new StackTest<>();
        stackTest.init();
        System.out.println(compute(stackTest,"15 7 1 1 + - / 3 * 2 1 1 + + -"));


    }
    /**
     *
     * 后缀表达式计算（暂时只支持整形）
     *
     * */
    public static int compute(StackTest<String> stackTest,String string){
        String[] s = string.split(" ");
        for (int j = 0; j < s.length; j++) {
            String num = String.valueOf(s[j]);
            if ("/".equals(num)||"*".equals(num)||"+".equals(num)||"-".equals(num)){
                Integer a = Integer.parseInt(stackTest.pop());
                Integer b = Integer.parseInt(stackTest.pop());
                if ("/".equals(num)){
                    stackTest.push(String.valueOf(b/a));
                }
                if ("*".equals(num)){
                    stackTest.push(String.valueOf(b*a));
                }
                if ("+".equals(num)){
                    stackTest.push(String.valueOf(b+a));
                }
                if ("-".equals(num)){
                    stackTest.push(String.valueOf(b-a));
                }
            }else {
                stackTest.push(num);
            }
        }
        return Integer.parseInt(stackTest.getTop().data);
    }

}
