package com.xbj.common;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Collections;
import java.util.List;

/**
 * 分页响应格式
 * @param <T> 数据类型
 */
@Schema(description = "分页响应格式")
public class PageResponse<T> {

    @Schema(description = "当前页码", example = "1")
    private Integer currentPage;

    @Schema(description = "每页大小", example = "10")
    private Integer pageSize;

    @Schema(description = "总记录数", example = "100")
    private Long totalCount;

    @Schema(description = "总页数", example = "10")
    private Integer totalPages;

    @Schema(description = "数据列表")
    private List<T> records;

    @Schema(description = "是否有下一页")
    private Boolean hasNext;

    @Schema(description = "是否有上一页")
    private Boolean hasPrevious;

    public PageResponse() {
    }

    public PageResponse(Integer currentPage, Integer pageSize, Long totalCount, List<T> records) {
        this.currentPage = currentPage;
        this.pageSize = pageSize;
        this.totalCount = totalCount;
        this.records = records;
        this.totalPages = (int) Math.ceil((double) totalCount / pageSize);
        this.hasNext = currentPage < totalPages;
        this.hasPrevious = currentPage > 1;
    }

    /**
     * 创建分页响应
     */
    public static <T> PageResponse<T> of(Integer currentPage, Integer pageSize, Long totalCount, List<T> records) {
        return new PageResponse<>(currentPage, pageSize, totalCount, records);
    }

    /**
     * 创建空分页响应
     */
    public static <T> PageResponse<T> empty(Integer currentPage, Integer pageSize) {
        return new PageResponse<>(currentPage, pageSize, 0L, Collections.emptyList());
    }

    // Getters and Setters
    public Integer getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Long totalCount) {
        this.totalCount = totalCount;
    }

    public Integer getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(Integer totalPages) {
        this.totalPages = totalPages;
    }

    public List<T> getRecords() {
        return records;
    }

    public void setRecords(List<T> records) {
        this.records = records;
    }

    public Boolean getHasNext() {
        return hasNext;
    }

    public void setHasNext(Boolean hasNext) {
        this.hasNext = hasNext;
    }

    public Boolean getHasPrevious() {
        return hasPrevious;
    }

    public void setHasPrevious(Boolean hasPrevious) {
        this.hasPrevious = hasPrevious;
    }

    @Override
    public String toString() {
        return "PageResponse{" +
                "currentPage=" + currentPage +
                ", pageSize=" + pageSize +
                ", totalCount=" + totalCount +
                ", totalPages=" + totalPages +
                ", records=" + records +
                ", hasNext=" + hasNext +
                ", hasPrevious=" + hasPrevious +
                '}';
    }
}
