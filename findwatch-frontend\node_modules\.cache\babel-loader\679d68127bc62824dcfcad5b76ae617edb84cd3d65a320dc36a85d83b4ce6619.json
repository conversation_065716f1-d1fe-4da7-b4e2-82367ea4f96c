{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nexport default {\n  name: 'Login',\n  data() {\n    return {\n      loginForm: {\n        username: '',\n        password: ''\n      },\n      loading: false\n    };\n  },\n  methods: {\n    async handleLogin() {\n      this.loading = true;\n      try {\n        // 调用后端登录API\n        const response = await this.$http.post('/test/login', {\n          username: this.loginForm.username,\n          password: this.loginForm.password\n        });\n        if (response.success) {\n          // 保存登录状态\n          localStorage.setItem('userToken', response.data.token);\n          localStorage.setItem('userInfo', JSON.stringify(response.data));\n          alert('登录成功！');\n          this.$router.push('/dashboard');\n        } else {\n          alert(response.message || '登录失败');\n        }\n      } catch (error) {\n        console.error('登录失败:', error);\n        alert('登录失败，请检查网络连接或联系管理员');\n      } finally {\n        this.loading = false;\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "loginForm", "username", "password", "loading", "methods", "handleLogin", "response", "$http", "post", "success", "localStorage", "setItem", "token", "JSON", "stringify", "alert", "$router", "push", "message", "error", "console"], "sources": ["D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\Login.vue"], "sourcesContent": ["<template>\n  <div class=\"login-container\">\n    <div class=\"login-box\">\n      <div class=\"login-header\">\n        <h1>寻表记</h1>\n        <p>手表管理系统</p>\n      </div>\n      \n      <form @submit.prevent=\"handleLogin\" class=\"login-form\">\n        <div class=\"form-group\">\n          <label for=\"username\">用户名</label>\n          <input\n            type=\"text\"\n            id=\"username\"\n            v-model=\"loginForm.username\"\n            placeholder=\"请输入用户名\"\n            required\n          />\n        </div>\n        \n        <div class=\"form-group\">\n          <label for=\"password\">密码</label>\n          <input\n            type=\"password\"\n            id=\"password\"\n            v-model=\"loginForm.password\"\n            placeholder=\"请输入密码\"\n            required\n          />\n        </div>\n        \n        <button type=\"submit\" class=\"login-btn\" :disabled=\"loading\">\n          {{ loading ? '登录中...' : '登录' }}\n        </button>\n      </form>\n      \n      <div class=\"login-footer\">\n        <p>演示账号: admin / 123456</p>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Login',\n  data() {\n    return {\n      loginForm: {\n        username: '',\n        password: ''\n      },\n      loading: false\n    }\n  },\n  methods: {\n    async handleLogin() {\n      this.loading = true\n\n      try {\n        // 调用后端登录API\n        const response = await this.$http.post('/test/login', {\n          username: this.loginForm.username,\n          password: this.loginForm.password\n        })\n\n        if (response.success) {\n          // 保存登录状态\n          localStorage.setItem('userToken', response.data.token)\n          localStorage.setItem('userInfo', JSON.stringify(response.data))\n\n          alert('登录成功！')\n          this.$router.push('/dashboard')\n        } else {\n          alert(response.message || '登录失败')\n        }\n      } catch (error) {\n        console.error('登录失败:', error)\n        alert('登录失败，请检查网络连接或联系管理员')\n      } finally {\n        this.loading = false\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.login-container {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.login-box {\n  background: white;\n  padding: 40px;\n  border-radius: 10px;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\n  width: 100%;\n  max-width: 400px;\n}\n\n.login-header {\n  text-align: center;\n  margin-bottom: 30px;\n}\n\n.login-header h1 {\n  color: #333;\n  margin-bottom: 10px;\n  font-size: 28px;\n}\n\n.login-header p {\n  color: #666;\n  font-size: 14px;\n}\n\n.form-group {\n  margin-bottom: 20px;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 5px;\n  color: #333;\n  font-weight: 500;\n}\n\n.form-group input {\n  width: 100%;\n  padding: 12px;\n  border: 1px solid #ddd;\n  border-radius: 5px;\n  font-size: 14px;\n  transition: border-color 0.3s;\n}\n\n.form-group input:focus {\n  outline: none;\n  border-color: #667eea;\n}\n\n.login-btn {\n  width: 100%;\n  padding: 12px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  border-radius: 5px;\n  font-size: 16px;\n  cursor: pointer;\n  transition: opacity 0.3s;\n}\n\n.login-btn:hover {\n  opacity: 0.9;\n}\n\n.login-btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.login-footer {\n  text-align: center;\n  margin-top: 20px;\n  padding-top: 20px;\n  border-top: 1px solid #eee;\n}\n\n.login-footer p {\n  color: #999;\n  font-size: 12px;\n}\n</style>\n"], "mappings": ";AA4CA,eAAe;EACbA,IAAI,EAAE,OAAO;EACbC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,SAAS,EAAE;QACTC,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE;MACZ,CAAC;MACDC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,OAAO,EAAE;IACP,MAAMC,WAAWA,CAAA,EAAG;MAClB,IAAI,CAACF,OAAM,GAAI,IAAG;MAElB,IAAI;QACF;QACA,MAAMG,QAAO,GAAI,MAAM,IAAI,CAACC,KAAK,CAACC,IAAI,CAAC,aAAa,EAAE;UACpDP,QAAQ,EAAE,IAAI,CAACD,SAAS,CAACC,QAAQ;UACjCC,QAAQ,EAAE,IAAI,CAACF,SAAS,CAACE;QAC3B,CAAC;QAED,IAAII,QAAQ,CAACG,OAAO,EAAE;UACpB;UACAC,YAAY,CAACC,OAAO,CAAC,WAAW,EAAEL,QAAQ,CAACP,IAAI,CAACa,KAAK;UACrDF,YAAY,CAACC,OAAO,CAAC,UAAU,EAAEE,IAAI,CAACC,SAAS,CAACR,QAAQ,CAACP,IAAI,CAAC;UAE9DgB,KAAK,CAAC,OAAO;UACb,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,YAAY;QAChC,OAAO;UACLF,KAAK,CAACT,QAAQ,CAACY,OAAM,IAAK,MAAM;QAClC;MACF,EAAE,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK;QAC5BJ,KAAK,CAAC,oBAAoB;MAC5B,UAAU;QACR,IAAI,CAACZ,OAAM,GAAI,KAAI;MACrB;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}