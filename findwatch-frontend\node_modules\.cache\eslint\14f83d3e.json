[{"D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\main.js": "1", "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\App.vue": "2", "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\router\\index.js": "3", "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\Dashboard.vue": "4", "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\WatchList.vue": "5", "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\UserProfile.vue": "6", "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\Login.vue": "7", "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\BrandList.vue": "8"}, {"size": 710, "mtime": 1752043839328, "results": "9", "hashOfConfig": "10"}, {"size": 488, "mtime": 1752043866090, "results": "11", "hashOfConfig": "10"}, {"size": 1282, "mtime": 1752043854600, "results": "12", "hashOfConfig": "10"}, {"size": 5644, "mtime": 1752043918775, "results": "13", "hashOfConfig": "10"}, {"size": 9212, "mtime": 1752043960582, "results": "14", "hashOfConfig": "10"}, {"size": 10787, "mtime": 1752044047566, "results": "15", "hashOfConfig": "10"}, {"size": 3473, "mtime": 1752043890001, "results": "16", "hashOfConfig": "10"}, {"size": 9601, "mtime": 1752044002396, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ja2m71", {"filePath": "20", "messages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "22", "messages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "26", "messages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "32", "messages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\main.js", [], "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\App.vue", [], "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\router\\index.js", [], "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\Dashboard.vue", ["34"], "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\WatchList.vue", [], "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\UserProfile.vue", [], "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\Login.vue", ["35"], "D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\BrandList.vue", [], {"ruleId": "36", "severity": 2, "message": "37", "line": 84, "column": 9, "nodeType": "38", "messageId": "39", "endLine": 84, "endColumn": 20}, {"ruleId": "36", "severity": 2, "message": "40", "line": 46, "column": 9, "nodeType": "38", "messageId": "39", "endLine": 46, "endColumn": 16}, "vue/multi-word-component-names", "Component name \"Dashboard\" should always be multi-word.", "Literal", "unexpected", "Component name \"Login\" should always be multi-word."]