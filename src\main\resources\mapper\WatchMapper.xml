<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xbj.dao.WatchMapper">
    <resultMap id="BaseResultMap" type="com.xbj.entity.Watch">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="brand" jdbcType="VARCHAR" property="brand"/>
        <result column="series" jdbcType="VARCHAR" property="series"/>
        <result column="model" jdbcType="VARCHAR" property="model"/>
        <result column="size" jdbcType="VARCHAR" property="size"/>
        <result column="price_inland" jdbcType="VARCHAR" property="priceInland"/>
        <result column="core_type" jdbcType="VARCHAR" property="coreType"/>
        <result column="core_model" jdbcType="VARCHAR" property="coreModel"/>
        <result column="bk_type" jdbcType="VARCHAR" property="bkType"/>
        <result column="bd_type" jdbcType="VARCHAR" property="bdType"/>
        <result column="bk_hor_size" jdbcType="VARCHAR" property="bkHorSize"/>
        <result column="bp_color" jdbcType="VARCHAR" property="bpColor"/>
        <result column="ks" jdbcType="VARCHAR" property="ks"/>
        <result column="fb_date" jdbcType="VARCHAR" property="fbDate"/>
        <result column="fz_fun" jdbcType="VARCHAR" property="fzFun"/>
        <result column="watch_url" jdbcType="VARCHAR" property="watchUrl"/>
        <result column="bkou_type" jdbcType="VARCHAR" property="bkouType"/>
        <result column="use_hours" jdbcType="VARCHAR" property="useHours"/>
        <result column="water_depth" jdbcType="VARCHAR" property="waterDepth"/>
        <result column="price_in_hk" jdbcType="VARCHAR" property="priceInHk"/>
        <result column="price_in_italy" jdbcType="VARCHAR" property="priceInItaly"/>
        <result column="price_in_france" jdbcType="VARCHAR" property="priceInFrance"/>
        <result column="price_in_spain" jdbcType="VARCHAR" property="priceInSpain"/>
        <result column="price_in_switzerland" jdbcType="VARCHAR" property="priceInSwitzerland"/>
        <result column="price_in_germany" jdbcType="VARCHAR" property="priceInGermany"/>
        <result column="price_in_holland" jdbcType="VARCHAR" property="priceInHolland"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
    id, brand, series, model, size, price_inland, core_type, core_model, bk_type, bd_type,
    bk_hor_size, bp_color, ks, fb_date, fz_fun, watch_url, bkou_type, use_hours, water_depth,
    price_in_hk, price_in_italy, price_in_france, price_in_spain, price_in_switzerland,
    price_in_germany, price_in_holland
  </sql>
    <select id="selectByExample" parameterType="com.xbj.entity.WatchExample" resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from watch
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="startRow != null and pageSize != null and pageSize != 0">
            limit #{startRow,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from watch
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from watch
    where id = #{id,jdbcType=VARCHAR}
  </delete>
    <delete id="deleteByExample" parameterType="com.xbj.entity.WatchExample">
        delete from watch
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.xbj.entity.Watch">
    insert into watch (id, brand, series,
      model, size, price_inland,
      core_type, core_model, bk_type,
      bd_type, bk_hor_size, bp_color,
      ks, fb_date, fz_fun,
      watch_url, bkou_type, use_hours,
      water_depth, price_in_hk, price_in_italy,
      price_in_france, price_in_spain, price_in_switzerland,
      price_in_germany, price_in_holland)
    values (#{id,jdbcType=VARCHAR}, #{brand,jdbcType=VARCHAR}, #{series,jdbcType=VARCHAR},
      #{model,jdbcType=VARCHAR}, #{size,jdbcType=VARCHAR}, #{priceInland,jdbcType=VARCHAR},
      #{coreType,jdbcType=VARCHAR}, #{coreModel,jdbcType=VARCHAR}, #{bkType,jdbcType=VARCHAR},
      #{bdType,jdbcType=VARCHAR}, #{bkHorSize,jdbcType=VARCHAR}, #{bpColor,jdbcType=VARCHAR},
      #{ks,jdbcType=VARCHAR}, #{fbDate,jdbcType=VARCHAR}, #{fzFun,jdbcType=VARCHAR},
      #{watchUrl,jdbcType=VARCHAR}, #{bkouType,jdbcType=VARCHAR}, #{useHours,jdbcType=VARCHAR},
      #{waterDepth,jdbcType=VARCHAR}, #{priceInHk,jdbcType=VARCHAR}, #{priceInItaly,jdbcType=VARCHAR},
      #{priceInFrance,jdbcType=VARCHAR}, #{priceInSpain,jdbcType=VARCHAR}, #{priceInSwitzerland,jdbcType=VARCHAR},
      #{priceInGermany,jdbcType=VARCHAR}, #{priceInHolland,jdbcType=VARCHAR})
  </insert>
    <insert id="insertSelective" parameterType="com.xbj.entity.Watch">
        insert into watch
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="brand != null">
                brand,
            </if>
            <if test="series != null">
                series,
            </if>
            <if test="model != null">
                model,
            </if>
            <if test="size != null">
                size,
            </if>
            <if test="priceInland != null">
                price_inland,
            </if>
            <if test="coreType != null">
                core_type,
            </if>
            <if test="coreModel != null">
                core_model,
            </if>
            <if test="bkType != null">
                bk_type,
            </if>
            <if test="bdType != null">
                bd_type,
            </if>
            <if test="bkHorSize != null">
                bk_hor_size,
            </if>
            <if test="bpColor != null">
                bp_color,
            </if>
            <if test="ks != null">
                ks,
            </if>
            <if test="fbDate != null">
                fb_date,
            </if>
            <if test="fzFun != null">
                fz_fun,
            </if>
            <if test="watchUrl != null">
                watch_url,
            </if>
            <if test="bkouType != null">
                bkou_type,
            </if>
            <if test="useHours != null">
                use_hours,
            </if>
            <if test="waterDepth != null">
                water_depth,
            </if>
            <if test="priceInHk != null">
                price_in_hk,
            </if>
            <if test="priceInItaly != null">
                price_in_italy,
            </if>
            <if test="priceInFrance != null">
                price_in_france,
            </if>
            <if test="priceInSpain != null">
                price_in_spain,
            </if>
            <if test="priceInSwitzerland != null">
                price_in_switzerland,
            </if>
            <if test="priceInGermany != null">
                price_in_germany,
            </if>
            <if test="priceInHolland != null">
                price_in_holland,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="brand != null">
                #{brand,jdbcType=VARCHAR},
            </if>
            <if test="series != null">
                #{series,jdbcType=VARCHAR},
            </if>
            <if test="model != null">
                #{model,jdbcType=VARCHAR},
            </if>
            <if test="size != null">
                #{size,jdbcType=VARCHAR},
            </if>
            <if test="priceInland != null">
                #{priceInland,jdbcType=VARCHAR},
            </if>
            <if test="coreType != null">
                #{coreType,jdbcType=VARCHAR},
            </if>
            <if test="coreModel != null">
                #{coreModel,jdbcType=VARCHAR},
            </if>
            <if test="bkType != null">
                #{bkType,jdbcType=VARCHAR},
            </if>
            <if test="bdType != null">
                #{bdType,jdbcType=VARCHAR},
            </if>
            <if test="bkHorSize != null">
                #{bkHorSize,jdbcType=VARCHAR},
            </if>
            <if test="bpColor != null">
                #{bpColor,jdbcType=VARCHAR},
            </if>
            <if test="ks != null">
                #{ks,jdbcType=VARCHAR},
            </if>
            <if test="fbDate != null">
                #{fbDate,jdbcType=VARCHAR},
            </if>
            <if test="fzFun != null">
                #{fzFun,jdbcType=VARCHAR},
            </if>
            <if test="watchUrl != null">
                #{watchUrl,jdbcType=VARCHAR},
            </if>
            <if test="bkouType != null">
                #{bkouType,jdbcType=VARCHAR},
            </if>
            <if test="useHours != null">
                #{useHours,jdbcType=VARCHAR},
            </if>
            <if test="waterDepth != null">
                #{waterDepth,jdbcType=VARCHAR},
            </if>
            <if test="priceInHk != null">
                #{priceInHk,jdbcType=VARCHAR},
            </if>
            <if test="priceInItaly != null">
                #{priceInItaly,jdbcType=VARCHAR},
            </if>
            <if test="priceInFrance != null">
                #{priceInFrance,jdbcType=VARCHAR},
            </if>
            <if test="priceInSpain != null">
                #{priceInSpain,jdbcType=VARCHAR},
            </if>
            <if test="priceInSwitzerland != null">
                #{priceInSwitzerland,jdbcType=VARCHAR},
            </if>
            <if test="priceInGermany != null">
                #{priceInGermany,jdbcType=VARCHAR},
            </if>
            <if test="priceInHolland != null">
                #{priceInHolland,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.xbj.entity.WatchExample" resultType="java.lang.Long">
        select count(*) from watch
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <select id="selectByCondition" resultMap="BaseResultMap" parameterType="com.xbj.entity.SearchInfo">
        select * from (
        select
        cast(replace(price_inland,',','') AS SIGNED) PRICE,
        <include refid="Base_Column_List"></include>
        from watch
        <where>
            <if test="brand !=null and brand != ''">
                <foreach collection="brand.split('/')" item="item" open="(" separator="or" close=")">
                    brand like "%"#{item,jdbcType=VARCHAR}"%"
                </foreach>
            </if>
            <if test="minPrice != null or maxPrice != null">
                and price_inland != '暂无'
            </if>
            <if test="name != null and name != ''">
                and model like "%"#{name,jdbcType=VARCHAR}"%"
            </if>
            <if test="seriesName !=null and seriesName!=''">
                and
                <foreach collection="seriesName.split(';')" item="item" open="(" separator="or" close=")">
                    series = #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="null != more">
                <if test="null != more.core_type and more.core_type.size>0 ">
                    and
                    <foreach collection="more.core_type" item="item" open="(" separator="or" close=")">
                        core_type = #{item.name,jdbcType=VARCHAR}
                    </foreach>
                </if>
                <if test="null != more.bk_type and more.bk_type.size>0">
                    and
                    <foreach collection="more.bk_type" item="item" open="(" separator="or" close=")">
                        bk_type like "%"#{item.name,jdbcType=VARCHAR}"%"
                    </foreach>
                </if>
                <if test="null != more.ks and more.ks.size>0">
                    and
                    <foreach collection="more.ks" item="item" open="(" separator="or" close=")">
                        ks like "%"#{item.name,jdbcType=VARCHAR}"%"
                    </foreach>
                </if>

            </if>
        </where>
        ) B
        <where>
            <if test="minPrice != null">
                B.PRICE &gt; #{minPrice,jdbcType=INTEGER}
            </if>
            <if test="maxPrice!=null">
                and B.PRICE &lt; #{maxPrice,jdbcType=INTEGER}
            </if>
        </where>
        order by B.PRICE,B.series
        <if test="startRow != null and pageSize != null and pageSize != 0">
            limit #{startRow,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
        </if>
    </select>
    <select id="selectSeriesByBrandName" resultMap="BaseResultMap">
      select distinct w.series from watch w where w.brand=#{brandName,jdbcType=VARCHAR}
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update watch
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=VARCHAR},
            </if>
            <if test="record.brand != null">
                brand = #{record.brand,jdbcType=VARCHAR},
            </if>
            <if test="record.series != null">
                series = #{record.series,jdbcType=VARCHAR},
            </if>
            <if test="record.model != null">
                model = #{record.model,jdbcType=VARCHAR},
            </if>
            <if test="record.size != null">
                size = #{record.size,jdbcType=VARCHAR},
            </if>
            <if test="record.priceInland != null">
                price_inland = #{record.priceInland,jdbcType=VARCHAR},
            </if>
            <if test="record.coreType != null">
                core_type = #{record.coreType,jdbcType=VARCHAR},
            </if>
            <if test="record.coreModel != null">
                core_model = #{record.coreModel,jdbcType=VARCHAR},
            </if>
            <if test="record.bkType != null">
                bk_type = #{record.bkType,jdbcType=VARCHAR},
            </if>
            <if test="record.bdType != null">
                bd_type = #{record.bdType,jdbcType=VARCHAR},
            </if>
            <if test="record.bkHorSize != null">
                bk_hor_size = #{record.bkHorSize,jdbcType=VARCHAR},
            </if>
            <if test="record.bpColor != null">
                bp_color = #{record.bpColor,jdbcType=VARCHAR},
            </if>
            <if test="record.ks != null">
                ks = #{record.ks,jdbcType=VARCHAR},
            </if>
            <if test="record.fbDate != null">
                fb_date = #{record.fbDate,jdbcType=VARCHAR},
            </if>
            <if test="record.fzFun != null">
                fz_fun = #{record.fzFun,jdbcType=VARCHAR},
            </if>
            <if test="record.watchUrl != null">
                watch_url = #{record.watchUrl,jdbcType=VARCHAR},
            </if>
            <if test="record.bkouType != null">
                bkou_type = #{record.bkouType,jdbcType=VARCHAR},
            </if>
            <if test="record.useHours != null">
                use_hours = #{record.useHours,jdbcType=VARCHAR},
            </if>
            <if test="record.waterDepth != null">
                water_depth = #{record.waterDepth,jdbcType=VARCHAR},
            </if>
            <if test="record.priceInHk != null">
                price_in_hk = #{record.priceInHk,jdbcType=VARCHAR},
            </if>
            <if test="record.priceInItaly != null">
                price_in_italy = #{record.priceInItaly,jdbcType=VARCHAR},
            </if>
            <if test="record.priceInFrance != null">
                price_in_france = #{record.priceInFrance,jdbcType=VARCHAR},
            </if>
            <if test="record.priceInSpain != null">
                price_in_spain = #{record.priceInSpain,jdbcType=VARCHAR},
            </if>
            <if test="record.priceInSwitzerland != null">
                price_in_switzerland = #{record.priceInSwitzerland,jdbcType=VARCHAR},
            </if>
            <if test="record.priceInGermany != null">
                price_in_germany = #{record.priceInGermany,jdbcType=VARCHAR},
            </if>
            <if test="record.priceInHolland != null">
                price_in_holland = #{record.priceInHolland,jdbcType=VARCHAR},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update watch
        set id = #{record.id,jdbcType=VARCHAR},
        brand = #{record.brand,jdbcType=VARCHAR},
        series = #{record.series,jdbcType=VARCHAR},
        model = #{record.model,jdbcType=VARCHAR},
        size = #{record.size,jdbcType=VARCHAR},
        price_inland = #{record.priceInland,jdbcType=VARCHAR},
        core_type = #{record.coreType,jdbcType=VARCHAR},
        core_model = #{record.coreModel,jdbcType=VARCHAR},
        bk_type = #{record.bkType,jdbcType=VARCHAR},
        bd_type = #{record.bdType,jdbcType=VARCHAR},
        bk_hor_size = #{record.bkHorSize,jdbcType=VARCHAR},
        bp_color = #{record.bpColor,jdbcType=VARCHAR},
        ks = #{record.ks,jdbcType=VARCHAR},
        fb_date = #{record.fbDate,jdbcType=VARCHAR},
        fz_fun = #{record.fzFun,jdbcType=VARCHAR},
        watch_url = #{record.watchUrl,jdbcType=VARCHAR},
        bkou_type = #{record.bkouType,jdbcType=VARCHAR},
        use_hours = #{record.useHours,jdbcType=VARCHAR},
        water_depth = #{record.waterDepth,jdbcType=VARCHAR},
        price_in_hk = #{record.priceInHk,jdbcType=VARCHAR},
        price_in_italy = #{record.priceInItaly,jdbcType=VARCHAR},
        price_in_france = #{record.priceInFrance,jdbcType=VARCHAR},
        price_in_spain = #{record.priceInSpain,jdbcType=VARCHAR},
        price_in_switzerland = #{record.priceInSwitzerland,jdbcType=VARCHAR},
        price_in_germany = #{record.priceInGermany,jdbcType=VARCHAR},
        price_in_holland = #{record.priceInHolland,jdbcType=VARCHAR}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.xbj.entity.Watch">
        update watch
        <set>
            <if test="brand != null">
                brand = #{brand,jdbcType=VARCHAR},
            </if>
            <if test="series != null">
                series = #{series,jdbcType=VARCHAR},
            </if>
            <if test="model != null">
                model = #{model,jdbcType=VARCHAR},
            </if>
            <if test="size != null">
                size = #{size,jdbcType=VARCHAR},
            </if>
            <if test="priceInland != null">
                price_inland = #{priceInland,jdbcType=VARCHAR},
            </if>
            <if test="coreType != null">
                core_type = #{coreType,jdbcType=VARCHAR},
            </if>
            <if test="coreModel != null">
                core_model = #{coreModel,jdbcType=VARCHAR},
            </if>
            <if test="bkType != null">
                bk_type = #{bkType,jdbcType=VARCHAR},
            </if>
            <if test="bdType != null">
                bd_type = #{bdType,jdbcType=VARCHAR},
            </if>
            <if test="bkHorSize != null">
                bk_hor_size = #{bkHorSize,jdbcType=VARCHAR},
            </if>
            <if test="bpColor != null">
                bp_color = #{bpColor,jdbcType=VARCHAR},
            </if>
            <if test="ks != null">
                ks = #{ks,jdbcType=VARCHAR},
            </if>
            <if test="fbDate != null">
                fb_date = #{fbDate,jdbcType=VARCHAR},
            </if>
            <if test="fzFun != null">
                fz_fun = #{fzFun,jdbcType=VARCHAR},
            </if>
            <if test="watchUrl != null">
                watch_url = #{watchUrl,jdbcType=VARCHAR},
            </if>
            <if test="bkouType != null">
                bkou_type = #{bkouType,jdbcType=VARCHAR},
            </if>
            <if test="useHours != null">
                use_hours = #{useHours,jdbcType=VARCHAR},
            </if>
            <if test="waterDepth != null">
                water_depth = #{waterDepth,jdbcType=VARCHAR},
            </if>
            <if test="priceInHk != null">
                price_in_hk = #{priceInHk,jdbcType=VARCHAR},
            </if>
            <if test="priceInItaly != null">
                price_in_italy = #{priceInItaly,jdbcType=VARCHAR},
            </if>
            <if test="priceInFrance != null">
                price_in_france = #{priceInFrance,jdbcType=VARCHAR},
            </if>
            <if test="priceInSpain != null">
                price_in_spain = #{priceInSpain,jdbcType=VARCHAR},
            </if>
            <if test="priceInSwitzerland != null">
                price_in_switzerland = #{priceInSwitzerland,jdbcType=VARCHAR},
            </if>
            <if test="priceInGermany != null">
                price_in_germany = #{priceInGermany,jdbcType=VARCHAR},
            </if>
            <if test="priceInHolland != null">
                price_in_holland = #{priceInHolland,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xbj.entity.Watch">
    update watch
    set brand = #{brand,jdbcType=VARCHAR},
      series = #{series,jdbcType=VARCHAR},
      model = #{model,jdbcType=VARCHAR},
      size = #{size,jdbcType=VARCHAR},
      price_inland = #{priceInland,jdbcType=VARCHAR},
      core_type = #{coreType,jdbcType=VARCHAR},
      core_model = #{coreModel,jdbcType=VARCHAR},
      bk_type = #{bkType,jdbcType=VARCHAR},
      bd_type = #{bdType,jdbcType=VARCHAR},
      bk_hor_size = #{bkHorSize,jdbcType=VARCHAR},
      bp_color = #{bpColor,jdbcType=VARCHAR},
      ks = #{ks,jdbcType=VARCHAR},
      fb_date = #{fbDate,jdbcType=VARCHAR},
      fz_fun = #{fzFun,jdbcType=VARCHAR},
      watch_url = #{watchUrl,jdbcType=VARCHAR},
      bkou_type = #{bkouType,jdbcType=VARCHAR},
      use_hours = #{useHours,jdbcType=VARCHAR},
      water_depth = #{waterDepth,jdbcType=VARCHAR},
      price_in_hk = #{priceInHk,jdbcType=VARCHAR},
      price_in_italy = #{priceInItaly,jdbcType=VARCHAR},
      price_in_france = #{priceInFrance,jdbcType=VARCHAR},
      price_in_spain = #{priceInSpain,jdbcType=VARCHAR},
      price_in_switzerland = #{priceInSwitzerland,jdbcType=VARCHAR},
      price_in_germany = #{priceInGermany,jdbcType=VARCHAR},
      price_in_holland = #{priceInHolland,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>
