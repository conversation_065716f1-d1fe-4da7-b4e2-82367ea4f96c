<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"

	xsi:schemaLocation="http://www.springframework.org/schema/beans
	http://www.springframework.org/schema/beans/spring-beans.xsd
	http://www.springframework.org/schema/context
	http://www.springframework.org/schema/context/spring-context.xsd">
	<!--xmlns:mongo="http://www.springframework.org/schema/data/mongo"-->
	<!-- 配置整合mybatis过程 -->
	<!-- 1.配置数据库相关参数properties的属性：${url} -->
	<context:property-placeholder location="classpath:jdbc.properties,classpath:redis.properties" />

	<!-- 2.数据库连接池 -->
	<bean id="dataSource" class="com.mchange.v2.c3p0.ComboPooledDataSource" destroy-method="close">
		<!-- 配置连接池属性 -->
		<property name="driverClass" value="${jdbc.driver}" />
		<property name="jdbcUrl" value="${jdbc.url}" />
		<property name="user" value="${jdbc.username}" />
		<property name="password" value="${jdbc.password}" />

		<!-- c3p0连接池的私有属性 -->
		<property name="maxPoolSize" value="${jdbc.c3p0.maxPoolSize}" />
		<property name="minPoolSize" value="${jdbc.c3p0.minPoolSize}" />
		<!-- 关闭连接后不自动commit -->
		<property name="autoCommitOnClose" value="false" />
		<!-- 获取连接超时时间 -->
		<property name="checkoutTimeout" value="10000" />
		<!-- 当获取连接失败重试次数 -->
		<property name="acquireRetryAttempts" value="10" />

		<property name="initialPoolSize" value="${jdbc.c3p0.initialPoolSize}" />

		<!--最大空闲时间,60秒内未使用则连接被丢弃。若为0则永不丢弃。Default: 0 -->
		<property name="maxIdleTime" value="${jdbc.c3p0.maxIdleTime}"/>
		<!--因性能消耗大请只在需要的时候使用它。如果设为true那么在每个connection提交的

        　　时候都将校验其有效性。建议使用idleConnectionTestPeriod或automaticTestTable

        　　等方法来提升连接测试的性能。Default: false -->
		<property name="testConnectionOnCheckout" value="${jdbc.c3p0.testConnectionOnCheckout}" />

		<!--如果设为true那么在取得连接的同时将校验连接的有效性。Default: false -->
		<property name="testConnectionOnCheckin" value="${jdbc.c3p0.testConnectionOnCheckin}" />
		<!--每3600秒检查所有连接池中的空闲连接。Default: 0 -->
		<property name="idleConnectionTestPeriod" value="${jdbc.c3p0.idleConnectionTestPeriod}" />

	</bean>

	<!-- 3.配置SqlSessionFactory对象 -->
	<bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
		<!-- 注入数据库连接池 -->
		<property name="dataSource" ref="dataSource" />
		<!-- 配置MyBaties全局配置文件:mapper-config.xml -->
		<property name="configLocation" value="classpath:mybatis-config.xml" />
		<!-- 扫描entity包 使用别名 -->
		<property name="typeAliasesPackage" value="com.xbj.entity" />
		<!-- 扫描sql配置文件:mapper需要的xml文件 -->
		<property name="mapperLocations" value="classpath:mapper/*.xml" />
	</bean>

	<!-- 4.配置扫描Dao接口包，动态实现Dao接口，注入到spring容器中 -->
	<bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
		<!-- 注入sqlSessionFactory -->
		<property name="sqlSessionFactoryBeanName" value="sqlSessionFactory" />
		<!-- 给出需要扫描Dao接口包 -->
		<property name="basePackage" value="com.xbj.dao" />
	</bean>

	<!--<mongo:mongo id="mongo" replica-set="${mongo.hostport}">-->
		<!--&lt;!&ndash; 一些连接属性的设置 &ndash;&gt;-->
		<!--<mongo:options-->
				<!--connections-per-host="${mongo.connectionsPerHost}"-->
				<!--threads-allowed-to-block-for-connection-multiplier="${mongo.threadsAllowedToBlockForConnectionMultiplier}"-->
				<!--connect-timeout="${mongo.connectTimeout}"-->
				<!--max-wait-time="${mongo.maxWaitTime}"-->
				<!--auto-connect-retry="${mongo.autoConnectRetry}"-->
				<!--socket-keep-alive="${mongo.socketKeepAlive}"-->
				<!--socket-timeout="${mongo.socketTimeout}"-->
				<!--slave-ok="${mongo.slaveOk}"-->
				<!--write-number="1"-->
				<!--write-timeout="0"-->
				<!--write-fsync="true" />-->
	<!--</mongo:mongo>-->

	<!--<mongo:db-factory dbname="database" mongo-ref="mongo" />-->

	<!--<bean id="mongoTemplate"-->
		  <!--class="org.springframework.data.mongodb.core.MongoTemplate">-->
		<!--<constructor-arg name="mongo" ref="mongo" />-->
		<!--<constructor-arg name="databaseName" value="test" />-->
	<!--</bean>-->
	<!--配置redis  此处的配置和 com.xbj.config.RedisConfiguration 任选其一-->
	<!--<bean id="poolConfig" class="redis.clients.jedis.JedisPoolConfig">-->
		<!--<property name="maxIdle" value="30" />-->
		<!--<property name="maxTotal" value="60" />-->
		<!--<property name="maxWaitMillis" value="2000" />-->
		<!--<property name="testOnBorrow" value="true" />-->
	<!--</bean>-->

	<!--<bean id="jedisConnectionFactory" class="org.springframework.data.redis.connection.jedis.JedisConnectionFactory">-->
		<!--<property name="hostName" value="${redis.ip}" />-->
		<!--<property name="password" value="${redis.password}" />-->
		<!--<property name="port" value="${redis.port}" />-->
		<!--<property name="poolConfig" ref="poolConfig" />-->
	<!--</bean>-->

	<!--<bean id="redisTemplate" class="org.springframework.data.redis.core.StringRedisTemplate">-->
		<!--<property name="connectionFactory" ref="jedisConnectionFactory" />-->
	<!--</bean>-->
	<!--&lt;!&ndash; RedisUtil注入RedisTemplate &ndash;&gt;-->
	<!--<bean id="redisUtil" class="com.xbj.util.RedisUtil">-->
		<!--<property name="redisTemplate" ref="redisTemplate" />-->
	<!--</bean>-->
</beans>