package com.xbj.test;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManagerFactory;
import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.URL;
import java.security.KeyStore;

public class HttpsCallWithCertificateVerification {

    public static void main(String[] args) throws Exception {
        // 配置证书校验
        String trustStorePath = "classpath:server.keystore"; // 证书库文件路径
        String trustStorePassword = "fnst1234"; // 证书库密码

        // 加载证书库
        FileInputStream trustStoreInputStream = new FileInputStream(trustStorePath);
        KeyStore trustStore = KeyStore.getInstance(KeyStore.getDefaultType());
        trustStore.load(trustStoreInputStream, trustStorePassword.toCharArray());

        // 创建 TrustManagerFactory
        TrustManagerFactory trustManagerFactory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
        trustManagerFactory.init(trustStore);

        // 创建 SSLContext
        SSLContext sslContext = SSLContext.getInstance("TLS");
        sslContext.init(null, trustManagerFactory.getTrustManagers(), null);

        // 创建 URL 对象
        URL url = new URL("https://192.168.3.51:3366/servlet/DlpServletInterface?console=DlpServer&function=SystemManager&act=setApproveServerConf"); // 替换为目标 HTTPS 接口的 URL

        // 打开连接
        HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
        connection.setSSLSocketFactory(sslContext.getSocketFactory());
        connection.setRequestMethod("POST");

        // 设置请求头
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("Authorization", "Bearer token"); // 替换为实际的认证信息

        // 启用输入和输出流
        connection.setDoInput(true);
        connection.setDoOutput(true);

        // 设置请求体
        String requestBody = "{\n" +
                "\"AutoLogin\": true,\n" +
                "\"ServerIP\": \"************\",\n" +
                "\"Port\": 19091,\n" +
                "\"PlatformType\": \"Appr\",\n" +
                "\"ServerUrl\": \"https://************:19091\"\n" +
                "}"; // 替换为实际的请求体
        OutputStream outputStream = connection.getOutputStream();
        outputStream.write(requestBody.getBytes());
        outputStream.flush();
        outputStream.close();

        // 发起请求
        int responseCode = connection.getResponseCode();

        // 读取响应
        BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
        String line;
        StringBuilder response = new StringBuilder();
        while ((line = reader.readLine()) != null) {
            response.append(line);
        }
        reader.close();

        // 输出响应
        System.out.println("Response Code: " + responseCode);
        System.out.println("Response Body: " + response.toString());

        // 关闭连接
        connection.disconnect();
    }
}
