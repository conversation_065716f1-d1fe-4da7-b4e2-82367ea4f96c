package com.xbj.config;


import com.xbj.util.RedisUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import redis.clients.jedis.JedisPoolConfig;

import java.util.ResourceBundle;

@Configuration
public class RedisConfiguration {


    /**
     * 配置Redis连接池
     */
    @Bean
    public JedisPoolConfig jedisPoolConfig(){
        JedisPoolConfig poolConfig=new JedisPoolConfig();
        //最大空闲数
        poolConfig.setMaxIdle(30);
        //最大连接数
        poolConfig.setMaxTotal(50);
        //最大等待毫秒数
        poolConfig.setMaxWaitMillis(2000);
        return poolConfig;
    }


    /**
     * 配置Redis的连接工厂
     */
    @Bean
    public JedisConnectionFactory redisConnectionFactory(JedisPoolConfig poolConfig) {
        ResourceBundle resourceBundle = ResourceBundle.getBundle("redis");
        String ip = resourceBundle.getString("redis.ip");
        String port = resourceBundle.getString("redis.port");
        String password = resourceBundle.getString("redis.password");
        JedisConnectionFactory connectionFactory = new JedisConnectionFactory(poolConfig);
        connectionFactory.setHostName(ip);
        connectionFactory.setPassword(password);
        connectionFactory.setPort(Integer.parseInt(port));
        return connectionFactory;
    }

    /**
     * 配置RedisTemplate
     */
    @Bean
    public StringRedisTemplate redisTemplate(JedisConnectionFactory connectionFactory){
        StringRedisTemplate redisTemplate = new StringRedisTemplate();
        redisTemplate.setConnectionFactory(connectionFactory);
        return redisTemplate;
    }
    /**
        配置工具类
     */
    @Bean
    public RedisUtil redisUtil(StringRedisTemplate redisTemplate){
        RedisUtil redisUtil = new RedisUtil();
        redisUtil.setRedisTemplate(redisTemplate);
        return redisUtil;
    }
}
