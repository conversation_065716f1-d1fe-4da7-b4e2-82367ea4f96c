package com.xbj.config;

import com.xbj.util.RedisUtil;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;

/**
 * Redis配置类
 * Spring Boot会自动配置RedisTemplate，这里只需要配置自定义的RedisUtil
 */
@Configuration
public class RedisConfiguration {

    /**
     * 配置RedisUtil工具类
     * Spring Boot会自动配置StringRedisTemplate，我们直接注入使用
     */
    @Bean
    @ConditionalOnMissingBean
    public RedisUtil redisUtil(StringRedisTemplate redisTemplate) {
        RedisUtil redisUtil = new RedisUtil();
        redisUtil.setRedisTemplate(redisTemplate);
        return redisUtil;
    }
}
