<!doctype html>
<html lang="en">
<head><title>HTTP Status 400 – Bad Request</title>
    <style type="text/css">H1 {
        font-family: Tahoma, Arial, sans-serif;
        color: white;
        background-color: #525D76;
        font-size: 22px;
    }

    H2 {
        font-family: Tahoma, Arial, sans-serif;
        color: white;
        background-color: #525D76;
        font-size: 16px;
    }

    H3 {
        font-family: Tahoma, Arial, sans-serif;
        color: white;
        background-color: #525D76;
        font-size: 14px;
    }

    BODY {
        font-family: Tahoma, Arial, sans-serif;
        color: black;
        background-color: white;
    }

    B {
        font-family: Tahoma, Arial, sans-serif;
        color: white;
        background-color: #525D76;
    }

    P {
        font-family: Tahoma, Arial, sans-serif;
        background: white;
        color: black;
        font-size: 12px;
    }

    A {
        color: black;
    }

    A.name {
        color: black;
    }

    HR {
        color: #525D76;
    }</style>
</head>
<body><h1>HTTP Status 400 – Bad Request</h1>
<hr class="line"/>
<p><b>Type</b> Status Report</p>
<p><b>Description</b> The server cannot or will not process the request due to something that is perceived to be a
    client error (e.g., malformed request syntax, invalid request message framing, or deceptive request routing).</p>
<hr class="line"/>
<h3>Apache Tomcat/7.0.96</h3></body>
</html>