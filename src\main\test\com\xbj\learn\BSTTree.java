package com.xbj.learn;

public class BSTTree {

    /*
    * 在二叉排序树中查找值为value的节点
    * */
    public BSTNode BST_search(BSTNode tree,int value){
        while (tree!=null&&value!=tree.getValue()){
            //小与，则在左子树查找
            if (value<tree.getValue()){
                tree = tree.getLeftChild();
            }else {
                //大于则在右子树查找
                tree = tree.getRightChild();
            }
        }
        return tree;
    }

    /*
    * 二叉排序树的插入
    * */
    public boolean BST_Insert(BSTNode tree,int k){
        if (tree == null){
            tree = new BSTNode(k,null,null);
            return true;
        }else if (k==tree.getValue()){
            return false;
        }else if (k<tree.getValue()){
            return BST_Insert(tree.getLeftChild(),k);
        }else {
            return BST_Insert(tree.getRightChild(),k);
        }
    }
}
