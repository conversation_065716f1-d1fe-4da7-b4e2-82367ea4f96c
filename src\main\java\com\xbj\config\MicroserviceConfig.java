package com.xbj.config;


import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.sql.DataSource;
import java.sql.Connection;

/**
 * 微服务配置类
 * 整合微服务相关的配置和组件
 */
@Configuration
public class MicroserviceConfig {

    @Autowired
    private DataSource dataSource;

    @Autowired
    private MeterRegistry meterRegistry;



    /**
     * 自定义数据库健康检查
     */
    @Bean
    public HealthIndicator databaseHealthIndicator() {
        return () -> {
            try {
                Connection connection = dataSource.getConnection();
                boolean isValid = connection.isValid(5);
                connection.close();
                
                if (isValid) {
                    return Health.up()
                            .withDetail("database", "MySQL")
                            .withDetail("status", "Connected")
                            .build();
                } else {
                    return Health.down()
                            .withDetail("database", "MySQL")
                            .withDetail("status", "Connection validation failed")
                            .build();
                }
            } catch (Exception e) {
                return Health.down()
                        .withDetail("database", "MySQL")
                        .withDetail("error", e.getMessage())
                        .build();
            }
        };
    }

    /**
     * 请求计时拦截器
     */
    @Bean
    public HandlerInterceptor requestTimingInterceptor() {
        return new HandlerInterceptor() {
            @Override
            public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
                Timer.Sample sample = Timer.start(meterRegistry);
                request.setAttribute("timer.sample", sample);
                
                // 记录请求指标
                meterRegistry.counter("http.requests.total", 
                    "method", request.getMethod(),
                    "uri", request.getRequestURI()).increment();
                
                return true;
            }

            @Override
            public void afterCompletion(HttpServletRequest request, HttpServletResponse response, 
                                      Object handler, Exception ex) {
                Timer.Sample sample = (Timer.Sample) request.getAttribute("timer.sample");
                if (sample != null) {
                    sample.stop(Timer.builder("http.request.duration")
                            .description("HTTP request duration")
                            .tag("method", request.getMethod())
                            .tag("uri", request.getRequestURI())
                            .tag("status", String.valueOf(response.getStatus()))
                            .register(meterRegistry));
                }
                
                // 记录响应状态指标
                meterRegistry.counter("http.responses.total",
                    "method", request.getMethod(),
                    "uri", request.getRequestURI(),
                    "status", String.valueOf(response.getStatus())).increment();
                
                // 记录错误指标
                if (ex != null) {
                    meterRegistry.counter("http.errors.total",
                        "method", request.getMethod(),
                        "uri", request.getRequestURI(),
                        "exception", ex.getClass().getSimpleName()).increment();
                }
            }
        };
    }

    /**
     * 自定义业务指标
     */
    @Bean
    public void customMetrics() {
        // 注册自定义指标
        meterRegistry.gauge("findwatch.version", 2.0);
        meterRegistry.gauge("findwatch.startup.time", System.currentTimeMillis());
    }
}
