package com.xbj.service;

import com.xbj.entity.SearchInfo;
import com.xbj.entity.Watch;
import com.xbj.entity.WatchExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-12-23 16:00
 */
public interface WatchService {
    long countByExample(WatchExample example);

    int deleteByExample(WatchExample example);

    int deleteByPrimaryKey(String id);

    int insert(Watch record);

    int insertSelective(Watch record);

    List<Watch> selectByExample(WatchExample example);

    Watch selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") Watch record, @Param("example") WatchExample example);

    int updateByExample(@Param("record") Watch record, @Param("example") WatchExample example);

    int updateByPrimaryKeySelective(Watch record);

    int updateByPrimaryKey(Watch record);

    List<Watch> selectByCondition(SearchInfo searchInfo);

    List<Watch> selectSeriesByBrandName(String brandName);


}
