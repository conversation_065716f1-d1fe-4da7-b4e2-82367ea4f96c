<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xbj.dao.WatchCodeMapper">
  <resultMap id="BaseResultMap" type="com.xbj.entity.WatchCode">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="code" jdbcType="CHAR" property="code" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="type_name" jdbcType="VARCHAR" property="typeName" />
    <result column="pic_url" jdbcType="VARCHAR" property="picUrl" />
    <result column="start_with" jdbcType="VARCHAR" property="startWith" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, code, type, name, type_name, pic_url, start_with
  </sql>
  <select id="selectByExample" parameterType="com.xbj.entity.WatchCodeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from watch_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="startRow != null and pageSize != null and pageSize != 0">
      limit #{startRow,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from watch_code
    where id = #{id,jdbcType=VARCHAR}
  </select>

  <select id="selectAllType" resultMap="BaseResultMap">
    select distinct type,type_name from watch_code
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from watch_code
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.xbj.entity.WatchCodeExample">
    delete from watch_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.xbj.entity.WatchCode">
    insert into watch_code (id, code, type, 
      name, type_name, pic_url, 
      start_with)
    values (#{id,jdbcType=VARCHAR}, #{code,jdbcType=CHAR}, #{type,jdbcType=VARCHAR}, 
      #{name,jdbcType=VARCHAR}, #{typeName,jdbcType=VARCHAR}, #{picUrl,jdbcType=VARCHAR}, 
      #{startWith,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.xbj.entity.WatchCode">
    insert into watch_code
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="typeName != null">
        type_name,
      </if>
      <if test="picUrl != null">
        pic_url,
      </if>
      <if test="startWith != null">
        start_with,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=CHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="typeName != null">
        #{typeName,jdbcType=VARCHAR},
      </if>
      <if test="picUrl != null">
        #{picUrl,jdbcType=VARCHAR},
      </if>
      <if test="startWith != null">
        #{startWith,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.xbj.entity.WatchCodeExample" resultType="java.lang.Long">
    select count(*) from watch_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update watch_code
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.code != null">
        code = #{record.code,jdbcType=CHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.typeName != null">
        type_name = #{record.typeName,jdbcType=VARCHAR},
      </if>
      <if test="record.picUrl != null">
        pic_url = #{record.picUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.startWith != null">
        start_with = #{record.startWith,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update watch_code
    set id = #{record.id,jdbcType=VARCHAR},
      code = #{record.code,jdbcType=CHAR},
      type = #{record.type,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      type_name = #{record.typeName,jdbcType=VARCHAR},
      pic_url = #{record.picUrl,jdbcType=VARCHAR},
      start_with = #{record.startWith,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.xbj.entity.WatchCode">
    update watch_code
    <set>
      <if test="code != null">
        code = #{code,jdbcType=CHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="typeName != null">
        type_name = #{typeName,jdbcType=VARCHAR},
      </if>
      <if test="picUrl != null">
        pic_url = #{picUrl,jdbcType=VARCHAR},
      </if>
      <if test="startWith != null">
        start_with = #{startWith,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xbj.entity.WatchCode">
    update watch_code
    set code = #{code,jdbcType=CHAR},
      type = #{type,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      type_name = #{typeName,jdbcType=VARCHAR},
      pic_url = #{picUrl,jdbcType=VARCHAR},
      start_with = #{startWith,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>