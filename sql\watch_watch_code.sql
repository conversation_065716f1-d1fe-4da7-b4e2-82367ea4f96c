create table watch_code
(
    id         varchar(42)             not null comment '主键'
        primary key,
    code       char(2)                 not null comment '值',
    type       varchar(100)            not null comment '类型',
    name       varchar(100)            not null comment '名称',
    type_name  varchar(100) default '' null comment 'type对应名称',
    pic_url    varchar(255) default '' null comment '图片路径',
    start_with varchar(2)   default '' null
);

INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('1', '1', 'brand', '劳力士/Rolex', '品牌', 'rolex.jpg', 'L');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('10', '10', 'brand', '百年灵/Breitling', '品牌', 'bnl.jpg', 'B');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('100', '35', 'brand', '理查德米勒/<PERSON>', '品牌', 'rm.jpg', 'L');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('101', '36', 'brand', '罗杰杜彼/Roger Dubuis', '品牌', 'rd.jpg', 'L');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('102', '37', 'brand', '路易威登/Louis Vuitton', '品牌', 'lv.jpg', 'L');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('103', '38', 'brand', '万宝龙/Montblanc', '品牌', 'mbc.jpg', 'W');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('104', '39', 'brand', '麦兰瑞/Mellerio', '品牌', 'mlr.jpg', 'M');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('105', '40', 'brand', '沛纳海/Panerai', '品牌', 'panerai.jpg', 'P');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('106', '41', 'brand', '帕玛强尼/Parmigiani', '品牌', 'pmg.jpg', 'P');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('107', '42', 'brand', '天梭/Tissot', '品牌', 'tissot.jpg', 'T');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('108', '43', 'brand', '泰格豪雅/Tag Heuer', '品牌', 'tag.jpg', 'T');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('109', '44', 'brand', '雅典/Ulysse Nardin', '品牌', 'un.jpg', 'Y');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('11', '1', 'core_type', '石英', '机芯类型', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('110', '45', 'brand', '真力时/Zenith', '品牌', 'zls.jpg', 'Z');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('111', '1', 'fb_date', '2019款', '发布时间', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('112', '2', 'fb_date', '2018款', '发布时间', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('113', '3', 'fb_date', '2018以前款', '发布时间', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('114', '46', 'brand', '宝诗龙/Boucheron', '品牌', 'bsl.jpg', 'B');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('115', '1', 'service_url', 'https://www.globalxbj.com', '服务器url', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('116', '1', 'price', '5千-1万', '价格范围', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('117', '2', 'price', '1万-2万', '价格范围', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('118', '3', 'price', '2万-5万', '价格范围', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('119', '4', 'price', '5万-10万', '价格范围', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('12', '2', 'core_type', '自动机械', '机芯类型', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('120', '5', 'price', '10万-15万', '价格范围', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('121', '6', 'price', '15万-20万', '价格范围', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('122', '7', 'price', '20万-30万', '价格范围', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('123', '8', 'price', '30万以上', '价格范围', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('13', '3', 'core_type', '手动机械', '机芯类型', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('14', '4', 'core_type', '机械', '机械类型', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('15', '1', 'bd_type', '金属', '表带材质', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('16', '2', 'bd_type', '陶瓷', '表带材质', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('17', '3', 'bd_type', '钛金属', '表带材质', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('18', '4', 'bd_type', '仿皮', '表带材质', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('19', '5', 'bd_type', '橡胶', '表带材质', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('2', '2', 'brand', '百达翡丽/Patek Philippe', '品牌', 'pp.jpg', 'B');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('20', '6', 'bd_type', '牛皮', '表带材质', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('21', '7', 'bd_type', '鳄鱼皮', '表带材质', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('22', '8', 'bd_type', '钢', '表带材质', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('23', '9', 'bd_type', '铂金', '表带材质', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('24', '10', 'bd_type', '黄金', '表带材质', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('25', '11', 'bd_type', '白金', '表带材质', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('26', '12', 'bd_type', '玫瑰金', '表带材质', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('27', '13', 'bd_type', '织物', '表带材质', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('28', '1', 'bk_type', '钢', '表壳材质', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('29', '2', 'bk_type', '不锈钢', '表壳材质', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('3', '3', 'brand', '欧米茄/Omega', '品牌', 'omega.jpg', 'O');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('30', '3', 'bk_type', '精钢', '表壳材质', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('31', '4', 'bk_type', '白金', '表壳材质', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('32', '5', 'bk_type', '黄金', '表壳材质', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('33', '6', 'bk_type', '玫瑰金', '表壳材质', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('34', '7', 'bk_type', '铂金', '表壳材质', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('35', '8', 'bk_type', '钛合金', '表壳材质', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('36', '9', 'bk_type', '陶瓷', '表壳材质', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('37', '10', 'bk_type', '镀金', '表壳材质', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('38', '11', 'bk_type', '镶钻', '表壳材质', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('39', '12', 'bk_type', '青铜', '表壳材质', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('4', '4', 'brand', '卡地亚/Cartier', '品牌', 'cartier.jpg', 'K');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('40', '1', 'bp_color', '星空蓝', '表盘颜色', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('41', '2', 'bp_color', '白色', '表盘颜色', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('42', '3', 'bp_color', '银灰', '表盘颜色', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('43', '4', 'bp_color', '深灰', '表盘颜色', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('44', '5', 'bp_color', '黑色', '表盘颜色', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('45', '6', 'bp_color', '深棕', '表盘颜色', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('46', '7', 'bp_color', '咖啡', '表盘颜色', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('47', '8', 'bp_color', '米色', '表盘颜色', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('48', '9', 'bp_color', '香槟金', '表盘颜色', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('49', '10', 'bp_color', '粉色', '表盘颜色', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('5', '5', 'brand', '江诗丹顿/Vacheron Constantin', '品牌', 'vc.jpg', 'J');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('50', '11', 'bp_color', '橙色', '表盘颜色', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('51', '12', 'bp_color', '红色', '表盘颜色', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('52', '13', 'bp_color', '绿色', '表盘颜色', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('53', '14', 'bp_color', '蓝色', '表盘颜色', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('54', '15', 'bp_color', '镶钻', '表盘颜色', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('55', '16', 'bp_color', '图案', '表盘颜色', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('56', '1', 'fz_fun', '日期显示', '复杂功能', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('57', '2', 'fz_fun', '星期显示', '复杂功能', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('58', '3', 'fz_fun', '月份显示', '复杂功能', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('59', '4', 'fz_fun', '万年历', '复杂功能', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('6', '6', 'brand', '万国/IWC', '品牌', 'iwc.jpg', 'W');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('60', '5', 'fz_fun', '月相', '复杂功能', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('61', '6', 'fz_fun', '双时区', '复杂功能', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('62', '7', 'fz_fun', '世界时', '复杂功能', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('63', '8', 'fz_fun', '计时', '复杂功能', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('64', '9', 'fz_fun', '动力存储显示', '复杂功能', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('65', '10', 'fz_fun', '防磁', '复杂功能', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('66', '11', 'fz_fun', '陀飞轮', '复杂功能', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('67', '12', 'fz_fun', '三问', '复杂功能', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('68', '1', 'ks', '男士', '款式', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('69', '2', 'ks', '女士', '款式', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('7', '7', 'brand', '浪琴/Longines', '品牌', 'longi.jpg', 'L');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('70', '3', 'ks', '中性', '款式', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('71', '4', 'ks', '中号', '款式', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('72', '5', 'ks', '小号', '款式', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('73', '6', 'ks', '迷你号', '款式', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('74', '7', 'ks', '大号', '款式', '', '');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('75', '11', 'brand', '爱彼/Audemars Piguet', '品牌', 'ap.jpg', 'A');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('76', '12', 'brand', '朗格/A.Lange&Sohne', '品牌', 'lange.jpg', 'L');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('77', '13', 'brand', '爱马仕/Hermes', '品牌', 'hermes.jpg', 'A');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('78', '14', 'brand', '名仕/Banume&Mercier', '品牌', 'ms.jpg', 'M');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('79', '15', 'brand', '宝格丽/Bvlgari', '品牌', 'bgl.jpg', 'B');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('8', '8', 'brand', '积家/Jaeger-LeCoultre', '品牌', 'jlc.jpg', 'J');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('80', '16', 'brand', '伯爵/Piaget', '品牌', 'bj.jpg', 'B');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('81', '17', 'brand', '宝玑/Breguet', '品牌', 'bji.jpg', 'B');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('82', '18', 'brand', '宝齐莱/Carl F.Bucherer', '品牌', 'bql.jpg', 'B');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('83', '19', 'brand', '布契拉提/Buccellati', '品牌', 'buc.jpg', 'B');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('85', '21', 'brand', '萧邦/Chopard', '品牌', 'chopard.jpg', 'X');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('86', '21', 'brand', '香奈儿/Chanel', '品牌', 'chanel.jpg', 'X');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('87', '22', 'brand', '尚美/Chaumet', '品牌', 'chaumet.jpg', 'S');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('88', '23', 'brand', '帝舵/Tudor', '品牌', 'tudor.jpg', 'D');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('89', '24', 'brand', '迪奥/Dior', '品牌', 'dior.jpg', 'D');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('9', '9', 'brand', '宝珀/Blancpain', '品牌', 'bp.jpg', 'B');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('90', '25', 'brand', '蒂芙尼/Tiffany&Co.', '品牌', 'tfn.jpg', 'D');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('91', '26', 'brand', '法穆兰/Franck Muller', '品牌', 'fml.jpg', 'F');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('92', '27', 'brand', '梵克雅宝/Van Cleef & Arpels', '品牌', 'vca.jpg', 'F');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('93', '28', 'brand', '格拉苏蒂/Glashutte', '品牌', 'glas.jpg', 'G');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('94', '29', 'brand', '芝柏/Girard Perregaux', '品牌', 'gp.jpg', 'Z');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('95', '30', 'brand', '古驰/Gucci', '品牌', 'gucci.jpg', 'G');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('96', '31', 'brand', '宇舶/Hublot', '品牌', 'hub.jpg', 'Y');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('97', '32', 'brand', '雷达/Rado', '品牌', 'rado.jpg', 'L');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('98', '33', 'brand', '精工/Seiko', '品牌', 'seiko.jpg', 'J');
INSERT INTO watch.watch_code (id, code, type, name, type_name, pic_url, start_with) VALUES ('99', '34', 'brand', '雅克德罗/Jaquet Droz', '品牌', 'jd.jpg', 'Y');