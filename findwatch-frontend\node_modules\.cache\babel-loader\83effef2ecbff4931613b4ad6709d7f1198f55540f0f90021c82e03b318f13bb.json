{"ast": null, "code": "/**\n* vue v3.5.17\n* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors\n* @license MIT\n**/\nimport { initCustomFormatter, warn } from '@vue/runtime-dom';\nexport * from '@vue/runtime-dom';\nfunction initDev() {\n  {\n    initCustomFormatter();\n  }\n}\nif (!!(process.env.NODE_ENV !== \"production\")) {\n  initDev();\n}\nconst compile = () => {\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    warn(`Runtime compilation is not supported in this build of Vue.` + ` Configure your bundler to alias \"vue\" to \"vue/dist/vue.esm-bundler.js\".`);\n  }\n};\nexport { compile };", "map": {"version": 3, "names": ["initCustomFormatter", "warn", "initDev", "process", "env", "NODE_ENV", "compile"], "sources": ["D:/devSpace/ideaWorkspace/findwatch/findwatch-frontend/node_modules/vue/dist/vue.runtime.esm-bundler.js"], "sourcesContent": ["/**\n* vue v3.5.17\n* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors\n* @license MIT\n**/\nimport { initCustomFormatter, warn } from '@vue/runtime-dom';\nexport * from '@vue/runtime-dom';\n\nfunction initDev() {\n  {\n    initCustomFormatter();\n  }\n}\n\nif (!!(process.env.NODE_ENV !== \"production\")) {\n  initDev();\n}\nconst compile = () => {\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    warn(\n      `Runtime compilation is not supported in this build of Vue.` + (` Configure your bundler to alias \"vue\" to \"vue/dist/vue.esm-bundler.js\".` )\n    );\n  }\n};\n\nexport { compile };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,SAASA,mBAAmB,EAAEC,IAAI,QAAQ,kBAAkB;AAC5D,cAAc,kBAAkB;AAEhC,SAASC,OAAOA,CAAA,EAAG;EACjB;IACEF,mBAAmB,CAAC,CAAC;EACvB;AACF;AAEA,IAAI,CAAC,EAAEG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,CAAC,EAAE;EAC7CH,OAAO,CAAC,CAAC;AACX;AACA,MAAMI,OAAO,GAAGA,CAAA,KAAM;EACpB,IAAI,CAAC,EAAEH,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,CAAC,EAAE;IAC7CJ,IAAI,CACF,4DAA4D,GAAI,0EAClE,CAAC;EACH;AACF,CAAC;AAED,SAASK,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}