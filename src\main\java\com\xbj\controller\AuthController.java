package com.xbj.controller;

import com.alibaba.fastjson.JSONObject;
import com.xbj.common.ApiResponse;
import com.xbj.common.ResultCode;
import com.xbj.dto.LoginRequest;
import com.xbj.dto.LoginResponse;
import com.xbj.dto.PhoneDecryptRequest;
import com.xbj.entity.User;
import com.xbj.exception.BusinessException;
import com.xbj.interceptor.TraceInterceptor;
import com.xbj.service.UserService;
import com.xbj.service.WeixinService;
import com.xbj.util.JWTUtils;
import com.xbj.util.WexiDecode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 认证控制器 - 微服务化重构版本
 */
@Tag(name = "认证管理", description = "用户认证相关接口")
@RestController
@RequestMapping("/api/v1/auth")
public class AuthController {

    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);

    @Autowired
    private WeixinService weixinService;

    @Autowired
    private UserService userService;

    /**
     * 微信小程序登录
     */
    @Operation(summary = "微信小程序登录", description = "通过微信授权码获取访问令牌")
    @PostMapping("/login")
    public ApiResponse<LoginResponse> login(@Valid @RequestBody LoginRequest request) {
        try {
            String traceId = TraceInterceptor.getCurrentTraceId();
            logger.info("用户登录请求 - TraceId: {}", traceId);

            // 调用微信接口获取session信息
            JSONObject sessionInfo = weixinService.getSessionKeyAndOpenId(request.getWxCode());
            
            if (sessionInfo == null || sessionInfo.get("openid") == null) {
                throw new BusinessException(ResultCode.WECHAT_CODE_INVALID);
            }

            String openid = sessionInfo.getString("openid");
            String sessionKey = sessionInfo.getString("session_key");

            // 生成JWT令牌
            Map<String, Object> claims = new HashMap<>();
            claims.put("openid", openid);
            claims.put("session_key", sessionKey);
            String token = JWTUtils.generateHMAC256Token(claims, "lookforwatch");

            LoginResponse response = new LoginResponse(token, 7200L, openid);
            
            ApiResponse<LoginResponse> result = ApiResponse.success("登录成功", response);
            result.setTraceId(traceId);
            
            logger.info("用户登录成功 - OpenId: {}, TraceId: {}", openid, traceId);
            return result;
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            logger.error("登录失败", e);
            throw new BusinessException(ResultCode.WECHAT_API_ERROR, "登录失败: " + e.getMessage());
        }
    }

    /**
     * 解密手机号
     */
    @Operation(summary = "解密手机号", description = "解密微信小程序获取的手机号信息")
    @SecurityRequirement(name = "Bearer Authentication")
    @PostMapping("/decrypt-phone")
    public ApiResponse<Boolean> decryptPhone(@Valid @RequestBody PhoneDecryptRequest request,
                                           HttpServletRequest httpRequest) {
        try {
            String traceId = TraceInterceptor.getCurrentTraceId();
            logger.info("解密手机号请求 - TraceId: {}", traceId);

            // 验证JWT令牌
            String token = httpRequest.getHeader("authorization");
            if (!StringUtils.hasText(token)) {
                throw new BusinessException(ResultCode.UNAUTHORIZED, "缺少访问令牌");
            }

            Map<String, Object> tokenResult = JWTUtils.verifyTokenGetResult(token, "lookforwatch");
            boolean isValid = Boolean.parseBoolean(tokenResult.get("isSuccess").toString());
            
            if (!isValid) {
                throw new BusinessException(ResultCode.TOKEN_INVALID);
            }

            String sessionKey = tokenResult.get("session_key").toString();
            String openid = tokenResult.get("openid").toString();

            // 解密手机号
            String phoneInfo = WexiDecode.decrypt(sessionKey, request.getIv(), request.getEncryptedData());
            JSONObject phoneData = JSONObject.parseObject(phoneInfo);
            String phoneNumber = phoneData.getString("phoneNumber");

            // 更新用户手机号
            User user = userService.getUserByUnionId(openid);
            if (user != null) {
                user.setPhone(phoneNumber);
                userService.updateByPrimaryKeySelective(user);
                logger.info("用户手机号更新成功 - OpenId: {}, Phone: {}, TraceId: {}", 
                           openid, phoneNumber, traceId);
            }

            ApiResponse<Boolean> result = ApiResponse.success("手机号解密成功", true);
            result.setTraceId(traceId);
            return result;
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            logger.error("手机号解密失败", e);
            throw new BusinessException(ResultCode.WECHAT_DECRYPT_FAILED, "手机号解密失败: " + e.getMessage());
        }
    }

    /**
     * 用户注册
     */
    @Operation(summary = "用户注册", description = "注册新用户或获取已存在用户信息")
    @SecurityRequirement(name = "Bearer Authentication")
    @PostMapping("/register")
    public ApiResponse<User> register(@Valid @RequestBody User user, HttpServletRequest httpRequest) {
        try {
            String traceId = TraceInterceptor.getCurrentTraceId();
            logger.info("用户注册请求 - TraceId: {}", traceId);

            // 验证JWT令牌
            String token = httpRequest.getHeader("authorization");
            if (!StringUtils.hasText(token)) {
                throw new BusinessException(ResultCode.UNAUTHORIZED, "缺少访问令牌");
            }

            Map<String, Object> tokenResult = JWTUtils.verifyTokenGetResult(token, "lookforwatch");
            boolean isValid = Boolean.parseBoolean(tokenResult.get("isSuccess").toString());
            
            if (!isValid) {
                throw new BusinessException(ResultCode.TOKEN_INVALID);
            }

            String openid = tokenResult.get("openid").toString();

            // 检查用户是否已存在
            User existingUser = userService.getUserByUnionId(openid);
            if (existingUser != null) {
                logger.info("用户已存在 - OpenId: {}, TraceId: {}", openid, traceId);
                ApiResponse<User> result = ApiResponse.success("用户已存在", existingUser);
                result.setTraceId(traceId);
                return result;
            }

            // 创建新用户
            user.setId(UUID.randomUUID().toString());
            user.setGender("1".equals(user.getGender()) ? "男" : "女");
            user.setOpenid(openid);
            user.setData1("");
            
            userService.insertSelective(user);
            
            logger.info("用户注册成功 - OpenId: {}, UserId: {}, TraceId: {}", 
                       openid, user.getId(), traceId);

            ApiResponse<User> result = ApiResponse.success("注册成功", user);
            result.setTraceId(traceId);
            return result;
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            logger.error("用户注册失败", e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "注册失败: " + e.getMessage());
        }
    }

    /**
     * 简单登录接口 - 用于前端测试
     */
    @Operation(summary = "简单登录", description = "用于前端测试的简单登录接口")
    @PostMapping("/simple-login")
    public ApiResponse<Map<String, Object>> simpleLogin(@RequestBody Map<String, String> loginRequest) {
        try {
            String traceId = TraceInterceptor.getCurrentTraceId();
            String username = loginRequest.get("username");
            String password = loginRequest.get("password");

            logger.info("简单登录请求 - Username: {}, TraceId: {}", username, traceId);

            // 简单的演示登录验证
            if ("admin".equals(username) && "123456".equals(password)) {
                Map<String, Object> result = new HashMap<>();
                result.put("token", "demo-token-" + System.currentTimeMillis());
                result.put("username", username);
                result.put("nickname", "管理员");
                result.put("id", 1);

                logger.info("演示登录成功 - Username: {}, TraceId: {}", username, traceId);

                ApiResponse<Map<String, Object>> response = ApiResponse.success("登录成功", result);
                response.setTraceId(traceId);
                return response;
            } else {
                throw new BusinessException(ResultCode.UNAUTHORIZED, "用户名或密码错误");
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            logger.error("登录失败", e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "登录失败: " + e.getMessage());
        }
    }

    /**
     * 验证令牌
     */
    @Operation(summary = "验证令牌", description = "验证访问令牌是否有效")
    @SecurityRequirement(name = "Bearer Authentication")
    @GetMapping("/verify")
    public ApiResponse<Map<String, Object>> verifyToken(HttpServletRequest httpRequest) {
        try {
            String traceId = TraceInterceptor.getCurrentTraceId();
            
            String token = httpRequest.getHeader("authorization");
            if (!StringUtils.hasText(token)) {
                throw new BusinessException(ResultCode.UNAUTHORIZED, "缺少访问令牌");
            }

            Map<String, Object> tokenResult = JWTUtils.verifyTokenGetResult(token, "lookforwatch");
            boolean isValid = Boolean.parseBoolean(tokenResult.get("isSuccess").toString());
            
            if (!isValid) {
                throw new BusinessException(ResultCode.TOKEN_INVALID);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("valid", true);
            result.put("openid", tokenResult.get("openid"));
            
            ApiResponse<Map<String, Object>> response = ApiResponse.success("令牌有效", result);
            response.setTraceId(traceId);
            return response;
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            logger.error("令牌验证失败", e);
            throw new BusinessException(ResultCode.TOKEN_INVALID, "令牌验证失败");
        }
    }
}
