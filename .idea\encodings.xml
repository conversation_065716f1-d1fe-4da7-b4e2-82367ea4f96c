<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding" native2AsciiForPropertiesFiles="true" defaultCharsetForPropertiesFiles="UTF-8">
    <file url="file://$PROJECT_DIR$" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources/server.cer" charset="GBK" />
    <file url="file://$PROJECT_DIR$/src/main/resources/server.keystore" charset="GBK" />
    <file url="file://$PROJECT_DIR$/src/main/resources/yml/CD.yml" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources/yml/banned-ips.json" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources/yml/blockvalues.yml" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources/yml/challenges.yml" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources/yml/commands.yml" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources/yml/config.yml" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources/yml/controlpanel.yml" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources/yml/cs.yml" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources/yml/db.yml" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources/yml/houj.yml" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources/yml/is.yml" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources/yml/islands.yml" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources/yml/js.yml" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources/yml/junj.yml" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources/yml/locale.yml" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources/yml/messages.yml" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources/yml/messages_en.yml" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources/yml/minishop.yml" charset="GBK" />
    <file url="file://$PROJECT_DIR$/src/main/resources/yml/otheraccounts.yml" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources/yml/players.yml" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources/yml/rw.yml" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources/yml/shop.yml" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources/yml/spawn.yml" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources/yml/spout.yml" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources/yml/topten.yml" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources/yml/vip.yml" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources/yml/viphou.yml" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources/yml/vipjun.yml" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources/yml/vipwang.yml" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources/yml/wangj.yml" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources/yml/warps.yml" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources/yml/welcome.txt" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources/yml/worlds.yml" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/test/com/xbj/test/server.keystore" charset="GBK" />
    <file url="PROJECT" charset="GBK" />
  </component>
</project>