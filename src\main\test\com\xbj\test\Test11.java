package com.xbj.test;

import com.idealista.tlsh.TLSH;
import com.idealista.tlsh.digests.Digest;
import com.idealista.tlsh.digests.DigestBuilder;

public class Test11 {
    public static void main(String[] args) {
        String a = "The best documentation is the UNIX source. After all, this is what the \"\n" +
                "\t\t\t\t+ \"system uses for documentation when it decides what to do next! The \"\n" +
                "\t\t\t\t+ \"manuals paraphrase the source code, often having been written at \"\n" +
                "\t\t\t\t+ \"different times and by different people than who wrote the code. \"\n" +
                "\t\t\t\t+ \"Think of them as guidelines. Sometimes they are more like wishes... \"\n" +
                "\t\t\t\t+ \"Nonetheless, it is all too common to turn to the source and find \"\n" +
                "\t\t\t\t+ \"options and behaviors that are not documented in the manual. Sometimes \"\n" +
                "\t\t\t\t+ \"you find options described in the manual that are unimplemented \"\n" +
                "\t\t\t\t+ \"and ignored by the source." ;
        String b = "The best documentation is the UNIX source. After all, this is what the \"\n" +
                "\t\t\t\t+ \"system uses for documentation when it decides what to do next! The \"\n" +
                "\t\t\t\t+ \"manuals paraphrase the source code, often having been written at \"\n" +
                "\t\t\t\t+ \"different times and by different people than who wrote the code. \"\n" +
                "\t\t\t\t+ \"Think of them as guidelines. Sometimes they are more like wishes... \"\n" +
                "\t\t\t\t+ \"Nonetheless, it is all too common to turn to the source and find \"\n" +
                "\t\t\t\t+ \"options and behaviors that are not documented in the manual. Sometimes \"\n" +
                "\t\t\t\t+ \"you find options described in the manual that are unimplemented \"\n" +
                "\t\t\t\t+ \"and ignored by the source." ;
        TLSH alsh = new TLSH(a);
        TLSH blsh = new TLSH(b);
        String ahash = alsh.hash();
        String bhash = blsh.hash();
        System.out.println("a的hash值："+ahash+"\n b的hash值："+bhash);
        Digest digest1 = new DigestBuilder().withHash(ahash).build();
        Digest digest2 = new DigestBuilder().withHash(bhash).build();
        int difference = digest2.calculateDifference(digest1, true);
        /*
        * How to measure the difference?
            A difference of 0 means the objects are almost identical.
            A difference of 200 or higher means the objects are very different.
        * */
        System.out.println("a与b的不同程度为："+difference);
    }
}
