package com.xbj.test;

import java.util.Calendar;

/**
 * <AUTHOR>
 * @date 2023/12/29 9:56
 **/
public class DateTest {
    public static void main(String[] args) {
// 获取当前时间点
//        Calendar calendar = Calendar.getInstance();
//        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:00:00");
//        int year = calendar.get(Calendar.YEAR);
//        int month = calendar.get(Calendar.MONTH);
//        int day = calendar.get(Calendar.DAY_OF_MONTH);
//
//        calendar.add(Calendar.HOUR_OF_DAY, 1);
//        String endTime = formatter.format(calendar.getTime());
//        calendar.set(year, month, day - 29, 0, 0, 0);
//        String startTime = formatter.format(calendar.getTime());
//        System.out.println("开始时间："+startTime+";结束时间："+endTime);
//        int createTimeStart = getCreateTimeStart(-29);
//        System.out.println(createTimeStart);
        String aa = "{\"combineField\":\"\",\"combineFlag\":1,\"combineType\":0,\"combineValue\":\"1\",\"createTime\":1713264323801,\"createUser\":\"yangyang\",\"deleteFlag\":0,\"handleAdvise\":\"排查相应终端存储数据内容\",\"id\":1,\"matchType\":1,\"modifyTime\":1713264323801,\"modifyUser\":\"yangyang\",\"reportFlag\":0,\"riskRuleId\":1,\"rules\":\"[{\\\"matchType\\\":\\\"=\\\",\\\"name\\\":\\\"stMedia\\\",\\\"type\\\":\\\"string\\\",\\\"value\\\":\\\"terminal\\\"},{\\\"matchType\\\":\\\"=\\\",\\\"name\\\":\\\"stPosition\\\",\\\"type\\\":\\\"string\\\",\\\"value\\\":\\\"1\\\"},{\\\"matchType\\\":\\\"=\\\",\\\"name\\\":\\\"stDataSecMeasures\\\",\\\"type\\\":\\\"string\\\",\\\"value\\\":\\\"0\\\"}]\",\"targetObjectType\":0}";
        System.out.println(aa.length());
    }


    public static int getCreateTimeStart(int dateIndex) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, dateIndex);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return (int) (calendar.getTimeInMillis() / 1000);
    }
}
