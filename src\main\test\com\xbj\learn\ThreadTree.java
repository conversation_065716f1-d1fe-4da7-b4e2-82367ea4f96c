package com.xbj.learn;

/**
 * 线索二叉树
 */
public class ThreadTree {
    //线索化时保存前驱
    private Node preNode;
    //根节点
    private Node root;

    public ThreadTree(){
        this.preNode = null;
        this.root = null;
    }

    /**
     * 中序线索化二叉树
     */
    public void inThread(Node root){
        if (root!=null){
            //中序遍历左子树
            inThread(root.getLeft());
            //访问根节点
            visit(root);
            //中序遍历右子树
            inThread(root.getRight());
            /*线索化到最后一个节点要 判断pre的rchild 是否为null，如果是，令rightIsThread 为true；否则进行中序遍历的时候最后一个节点会导致空指针*/
            if (preNode.getRight() == null){
                preNode.setRightIsThread(true);
            }
        }
    }

    /*
    * 先序线索化二叉树
    * */
    public void inThread1(Node root){
        if (root!=null){
            //访问根节点
            visit(root);
            /*
            * 如果前驱未被线索化，再进行遍历，否则会导致循环引用
            * */
            if (!root.isLeftIsThread()){
                //遍历左子树
                inThread(root.getLeft());
            }
            //遍历右子树
            inThread(root.getRight());
            /*线索化到最后一个节点要 判断pre的rchild 是否为null，如果是，令rightIsThread 为true；否则进行中序遍历的时候最后一个节点会导致空指针*/
            if (preNode.getRight() == null){
                preNode.setRightIsThread(true);
            }
        }
    }
    /*
    * 后续线索化
    * */
    public void inThread2(Node root){
        if (root!=null){
            //访问坐子树
            inThread(root.getLeft());
            //遍历右子树
            inThread(root.getRight());
            /*访问根节点*/
            visit(root);
            /*线索化到最后一个节点要 判断pre的rchild 是否为null，如果是，令rightIsThread 为true；否则进行中序遍历的时候最后一个节点会导致空指针*/
            if (preNode.getRight() == null){
                preNode.setRightIsThread(true);
            }
        }
    }

    public void visit(Node root){
        if (null == root.getLeft()){
            root.setLeftIsThread(true);
            root.setLeft(preNode);
        }
        if (preNode!=null && null == preNode.getRight()){
            preNode.setRight(root);
            preNode.setRightIsThread(true);
        }
        preNode = root;
    }


    public ThreadTree(int[] data) {
        this.preNode = null;
        this.root = createTree(data, 0);   // 创建二叉树
    }
    /**
     * 创建二叉树
     * @param data
     * @param index
     * @return
     */
    public Node createTree(int[] data, int index) {
        if (index >= data.length) {
            return null;
        }
        Node node = new Node(data[index]);
        node.setLeft(createTree(data, 2 * index + 1));
        node.setRight(createTree(data, 2 * index + 2));
        return node;
    }

    /*
    * 找到以p为根的子树中，第一个被中序遍历的节点
    * */
    public Node getFirstNode(Node p){
        while (!p.isLeftIsThread()){
            p = p.getLeft();
        }
        return p;
    }

    /**
     * 在中序线索二叉树中找到结点p的后继节点
     * @param p
     * @return
     */
    public Node getNextNode(Node p){

        //如果右孩子未被线索化，说明右孩子有子节点，右孩子中最左下节点，为p节点的后继节点
        if (!p.isRightIsThread()){
            return getFirstNode(p.getRight());
        }else {
            return p.getRight();
        }
    }

    //对中序线索二叉树进行中序遍历
    public void InOrder(Node root){
        for (Node p = getFirstNode(root);p!=null;p = getNextNode(p)){
            System.out.println(p.getData());
        }
    }



    public static void main(String[] args) {
        int[] a = new int[]{1,2,3,4,5,6};
        ThreadTree threadTree = new ThreadTree(a);
        threadTree.inThread(threadTree.root);
        threadTree.InOrder(threadTree.root);
    }
}
