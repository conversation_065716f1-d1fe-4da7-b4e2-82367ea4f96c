{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nexport default {\n  name: 'Dashboard',\n  data() {\n    return {\n      stats: {\n        totalWatches: 0,\n        totalBrands: 0,\n        totalUsers: 0,\n        totalValue: 0\n      }\n    };\n  },\n  async mounted() {\n    await this.loadStats();\n  },\n  methods: {\n    async loadStats() {\n      try {\n        // 模拟数据，实际应该从API获取\n        this.stats = {\n          totalWatches: 156,\n          totalBrands: 25,\n          totalUsers: 8,\n          totalValue: 2580000\n        };\n      } catch (error) {\n        console.error('加载统计数据失败:', error);\n      }\n    },\n    logout() {\n      localStorage.removeItem('userToken');\n      localStorage.removeItem('userInfo');\n      this.$router.push('/login');\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "stats", "totalWatches", "totalBrands", "totalUsers", "totalValue", "mounted", "loadStats", "methods", "error", "console", "logout", "localStorage", "removeItem", "$router", "push"], "sources": ["D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\Dashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard\">\n    <nav class=\"navbar\">\n      <div class=\"nav-brand\">\n        <h2>寻表记</h2>\n      </div>\n      <div class=\"nav-menu\">\n        <router-link to=\"/dashboard\" class=\"nav-item active\">首页</router-link>\n        <router-link to=\"/watches\" class=\"nav-item\">手表管理</router-link>\n        <router-link to=\"/brands\" class=\"nav-item\">品牌管理</router-link>\n        <router-link to=\"/profile\" class=\"nav-item\">个人信息</router-link>\n        <button @click=\"logout\" class=\"logout-btn\">退出</button>\n      </div>\n    </nav>\n\n    <div class=\"main-content\">\n      <div class=\"welcome-section\">\n        <h1>欢迎使用寻表记管理系统</h1>\n        <p>专业的手表收藏与管理平台</p>\n      </div>\n\n      <div class=\"stats-grid\">\n        <div class=\"stat-card\">\n          <div class=\"stat-icon\">⌚</div>\n          <div class=\"stat-info\">\n            <h3>{{ stats.totalWatches }}</h3>\n            <p>手表总数</p>\n          </div>\n        </div>\n        \n        <div class=\"stat-card\">\n          <div class=\"stat-icon\">🏷️</div>\n          <div class=\"stat-info\">\n            <h3>{{ stats.totalBrands }}</h3>\n            <p>品牌数量</p>\n          </div>\n        </div>\n        \n        <div class=\"stat-card\">\n          <div class=\"stat-icon\">👥</div>\n          <div class=\"stat-info\">\n            <h3>{{ stats.totalUsers }}</h3>\n            <p>用户数量</p>\n          </div>\n        </div>\n        \n        <div class=\"stat-card\">\n          <div class=\"stat-icon\">💰</div>\n          <div class=\"stat-info\">\n            <h3>¥{{ stats.totalValue.toLocaleString() }}</h3>\n            <p>总价值</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"quick-actions\">\n        <h2>快速操作</h2>\n        <div class=\"action-grid\">\n          <router-link to=\"/watches\" class=\"action-card\">\n            <div class=\"action-icon\">⌚</div>\n            <h3>管理手表</h3>\n            <p>查看、添加、编辑手表信息</p>\n          </router-link>\n          \n          <router-link to=\"/brands\" class=\"action-card\">\n            <div class=\"action-icon\">🏷️</div>\n            <h3>管理品牌</h3>\n            <p>管理手表品牌信息</p>\n          </router-link>\n          \n          <router-link to=\"/profile\" class=\"action-card\">\n            <div class=\"action-icon\">👤</div>\n            <h3>个人资料</h3>\n            <p>查看和编辑个人信息</p>\n          </router-link>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Dashboard',\n  data() {\n    return {\n      stats: {\n        totalWatches: 0,\n        totalBrands: 0,\n        totalUsers: 0,\n        totalValue: 0\n      }\n    }\n  },\n  async mounted() {\n    await this.loadStats()\n  },\n  methods: {\n    async loadStats() {\n      try {\n        // 模拟数据，实际应该从API获取\n        this.stats = {\n          totalWatches: 156,\n          totalBrands: 25,\n          totalUsers: 8,\n          totalValue: 2580000\n        }\n      } catch (error) {\n        console.error('加载统计数据失败:', error)\n      }\n    },\n    logout() {\n      localStorage.removeItem('userToken')\n      localStorage.removeItem('userInfo')\n      this.$router.push('/login')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.dashboard {\n  min-height: 100vh;\n}\n\n.navbar {\n  background: white;\n  padding: 0 20px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  height: 60px;\n}\n\n.nav-brand h2 {\n  color: #333;\n  margin: 0;\n}\n\n.nav-menu {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n}\n\n.nav-item {\n  text-decoration: none;\n  color: #666;\n  padding: 8px 16px;\n  border-radius: 4px;\n  transition: all 0.3s;\n}\n\n.nav-item:hover,\n.nav-item.active {\n  color: #667eea;\n  background: #f0f2ff;\n}\n\n.logout-btn {\n  background: #ff4757;\n  color: white;\n  border: none;\n  padding: 8px 16px;\n  border-radius: 4px;\n  cursor: pointer;\n  transition: background 0.3s;\n}\n\n.logout-btn:hover {\n  background: #ff3742;\n}\n\n.main-content {\n  padding: 40px 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.welcome-section {\n  text-align: center;\n  margin-bottom: 40px;\n}\n\n.welcome-section h1 {\n  color: #333;\n  margin-bottom: 10px;\n  font-size: 32px;\n}\n\n.welcome-section p {\n  color: #666;\n  font-size: 16px;\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n  margin-bottom: 40px;\n}\n\n.stat-card {\n  background: white;\n  padding: 30px;\n  border-radius: 10px;\n  box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n  display: flex;\n  align-items: center;\n  gap: 20px;\n}\n\n.stat-icon {\n  font-size: 40px;\n  width: 60px;\n  height: 60px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: #f0f2ff;\n  border-radius: 50%;\n}\n\n.stat-info h3 {\n  margin: 0 0 5px 0;\n  color: #333;\n  font-size: 24px;\n}\n\n.stat-info p {\n  margin: 0;\n  color: #666;\n  font-size: 14px;\n}\n\n.quick-actions h2 {\n  color: #333;\n  margin-bottom: 20px;\n}\n\n.action-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n}\n\n.action-card {\n  background: white;\n  padding: 30px;\n  border-radius: 10px;\n  box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n  text-decoration: none;\n  color: inherit;\n  transition: transform 0.3s, box-shadow 0.3s;\n  text-align: center;\n}\n\n.action-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 5px 20px rgba(0,0,0,0.15);\n}\n\n.action-icon {\n  font-size: 48px;\n  margin-bottom: 15px;\n}\n\n.action-card h3 {\n  color: #333;\n  margin-bottom: 10px;\n}\n\n.action-card p {\n  color: #666;\n  margin: 0;\n}\n</style>\n"], "mappings": ";AAkFA,eAAe;EACbA,IAAI,EAAE,WAAW;EACjBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,KAAK,EAAE;QACLC,YAAY,EAAE,CAAC;QACfC,WAAW,EAAE,CAAC;QACdC,UAAU,EAAE,CAAC;QACbC,UAAU,EAAE;MACd;IACF;EACF,CAAC;EACD,MAAMC,OAAOA,CAAA,EAAG;IACd,MAAM,IAAI,CAACC,SAAS,CAAC;EACvB,CAAC;EACDC,OAAO,EAAE;IACP,MAAMD,SAASA,CAAA,EAAG;MAChB,IAAI;QACF;QACA,IAAI,CAACN,KAAI,GAAI;UACXC,YAAY,EAAE,GAAG;UACjBC,WAAW,EAAE,EAAE;UACfC,UAAU,EAAE,CAAC;UACbC,UAAU,EAAE;QACd;MACF,EAAE,OAAOI,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;MAClC;IACF,CAAC;IACDE,MAAMA,CAAA,EAAG;MACPC,YAAY,CAACC,UAAU,CAAC,WAAW;MACnCD,YAAY,CAACC,UAAU,CAAC,UAAU;MAClC,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,QAAQ;IAC5B;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}