package com.xbj;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 寻表记 Spring Boot 启动类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@SpringBootApplication
@MapperScan("com.xbj.dao")
@EnableTransactionManagement
public class FindWatchApplication {

    public static void main(String[] args) {
        SpringApplication.run(FindWatchApplication.class, args);
    }
}
