{"ast": null, "code": "import { createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, vModelText as _vModelText, withDirectives as _withDirectives } from \"vue\";\nconst _hoisted_1 = {\n  class: \"test-container\"\n};\nconst _hoisted_2 = {\n  class: \"test-section\"\n};\nconst _hoisted_3 = [\"disabled\"];\nconst _hoisted_4 = {\n  key: 0,\n  class: \"result\"\n};\nconst _hoisted_5 = {\n  key: 0\n};\nconst _hoisted_6 = {\n  class: \"test-section\"\n};\nconst _hoisted_7 = {\n  class: \"login-form\"\n};\nconst _hoisted_8 = [\"disabled\"];\nconst _hoisted_9 = {\n  key: 0,\n  class: \"result\"\n};\nconst _hoisted_10 = {\n  key: 0\n};\nconst _hoisted_11 = {\n  class: \"test-section\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[18] || (_cache[18] = _createElementVNode(\"h2\", null, \"前后端连接测试\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_2, [_cache[7] || (_cache[7] = _createElementVNode(\"h3\", null, \"1. 测试后端连接\", -1 /* CACHED */)), _createElementVNode(\"button\", {\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.testBackendConnection && $options.testBackendConnection(...args)),\n    disabled: $data.loading\n  }, _toDisplayString($data.loading ? '测试中...' : '测试连接'), 9 /* TEXT, PROPS */, _hoisted_3), $data.connectionResult ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [_createElementVNode(\"p\", null, [_cache[4] || (_cache[4] = _createElementVNode(\"strong\", null, \"状态:\", -1 /* CACHED */)), _createTextVNode(\" \" + _toDisplayString($data.connectionResult.success ? '成功' : '失败'), 1 /* TEXT */)]), _createElementVNode(\"p\", null, [_cache[5] || (_cache[5] = _createElementVNode(\"strong\", null, \"消息:\", -1 /* CACHED */)), _createTextVNode(\" \" + _toDisplayString($data.connectionResult.message), 1 /* TEXT */)]), $data.connectionResult.data ? (_openBlock(), _createElementBlock(\"p\", _hoisted_5, [_cache[6] || (_cache[6] = _createElementVNode(\"strong\", null, \"数据:\", -1 /* CACHED */)), _createTextVNode(\" \" + _toDisplayString($data.connectionResult.data), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_6, [_cache[13] || (_cache[13] = _createElementVNode(\"h3\", null, \"2. 测试登录接口\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_7, [_withDirectives(_createElementVNode(\"input\", {\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $data.testUsername = $event),\n    placeholder: \"用户名 (admin)\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.testUsername]]), _withDirectives(_createElementVNode(\"input\", {\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $data.testPassword = $event),\n    type: \"password\",\n    placeholder: \"密码 (123456)\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.testPassword]]), _createElementVNode(\"button\", {\n    onClick: _cache[3] || (_cache[3] = (...args) => $options.testLogin && $options.testLogin(...args)),\n    disabled: $data.loginLoading\n  }, _toDisplayString($data.loginLoading ? '登录中...' : '测试登录'), 9 /* TEXT, PROPS */, _hoisted_8)]), $data.loginResult ? (_openBlock(), _createElementBlock(\"div\", _hoisted_9, [_createElementVNode(\"p\", null, [_cache[8] || (_cache[8] = _createElementVNode(\"strong\", null, \"状态:\", -1 /* CACHED */)), _createTextVNode(\" \" + _toDisplayString($data.loginResult.success ? '成功' : '失败'), 1 /* TEXT */)]), _createElementVNode(\"p\", null, [_cache[9] || (_cache[9] = _createElementVNode(\"strong\", null, \"消息:\", -1 /* CACHED */)), _createTextVNode(\" \" + _toDisplayString($data.loginResult.message), 1 /* TEXT */)]), $data.loginResult.data ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [_createElementVNode(\"p\", null, [_cache[10] || (_cache[10] = _createElementVNode(\"strong\", null, \"Token:\", -1 /* CACHED */)), _createTextVNode(\" \" + _toDisplayString($data.loginResult.data.token), 1 /* TEXT */)]), _createElementVNode(\"p\", null, [_cache[11] || (_cache[11] = _createElementVNode(\"strong\", null, \"用户名:\", -1 /* CACHED */)), _createTextVNode(\" \" + _toDisplayString($data.loginResult.data.username), 1 /* TEXT */)]), _createElementVNode(\"p\", null, [_cache[12] || (_cache[12] = _createElementVNode(\"strong\", null, \"昵称:\", -1 /* CACHED */)), _createTextVNode(\" \" + _toDisplayString($data.loginResult.data.nickname), 1 /* TEXT */)])])) : _createCommentVNode(\"v-if\", true)])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_11, [_cache[17] || (_cache[17] = _createElementVNode(\"h3\", null, \"3. 网络信息\", -1 /* CACHED */)), _createElementVNode(\"p\", null, [_cache[14] || (_cache[14] = _createElementVNode(\"strong\", null, \"前端地址:\", -1 /* CACHED */)), _createTextVNode(\" \" + _toDisplayString($data.frontendUrl), 1 /* TEXT */)]), _createElementVNode(\"p\", null, [_cache[15] || (_cache[15] = _createElementVNode(\"strong\", null, \"后端地址:\", -1 /* CACHED */)), _createTextVNode(\" \" + _toDisplayString($data.backendUrl), 1 /* TEXT */)]), _createElementVNode(\"p\", null, [_cache[16] || (_cache[16] = _createElementVNode(\"strong\", null, \"API基础URL:\", -1 /* CACHED */)), _createTextVNode(\" \" + _toDisplayString($data.apiBaseUrl), 1 /* TEXT */)])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "onClick", "_cache", "args", "$options", "testBackendConnection", "disabled", "$data", "loading", "_hoisted_3", "connectionResult", "_hoisted_4", "_toDisplayString", "success", "message", "data", "_hoisted_5", "_hoisted_6", "_hoisted_7", "testUsername", "$event", "placeholder", "testPassword", "type", "testLogin", "loginLoading", "_hoisted_8", "loginResult", "_hoisted_9", "_hoisted_10", "token", "username", "nickname", "_hoisted_11", "frontendUrl", "backendUrl", "apiBaseUrl"], "sources": ["D:\\devSpace\\ideaWorkspace\\findwatch\\findwatch-frontend\\src\\views\\Test.vue"], "sourcesContent": ["<template>\n  <div class=\"test-container\">\n    <h2>前后端连接测试</h2>\n    \n    <div class=\"test-section\">\n      <h3>1. 测试后端连接</h3>\n      <button @click=\"testBackendConnection\" :disabled=\"loading\">\n        {{ loading ? '测试中...' : '测试连接' }}\n      </button>\n      <div v-if=\"connectionResult\" class=\"result\">\n        <p><strong>状态:</strong> {{ connectionResult.success ? '成功' : '失败' }}</p>\n        <p><strong>消息:</strong> {{ connectionResult.message }}</p>\n        <p v-if=\"connectionResult.data\"><strong>数据:</strong> {{ connectionResult.data }}</p>\n      </div>\n    </div>\n\n    <div class=\"test-section\">\n      <h3>2. 测试登录接口</h3>\n      <div class=\"login-form\">\n        <input v-model=\"testUsername\" placeholder=\"用户名 (admin)\" />\n        <input v-model=\"testPassword\" type=\"password\" placeholder=\"密码 (123456)\" />\n        <button @click=\"testLogin\" :disabled=\"loginLoading\">\n          {{ loginLoading ? '登录中...' : '测试登录' }}\n        </button>\n      </div>\n      <div v-if=\"loginResult\" class=\"result\">\n        <p><strong>状态:</strong> {{ loginResult.success ? '成功' : '失败' }}</p>\n        <p><strong>消息:</strong> {{ loginResult.message }}</p>\n        <div v-if=\"loginResult.data\">\n          <p><strong>Token:</strong> {{ loginResult.data.token }}</p>\n          <p><strong>用户名:</strong> {{ loginResult.data.username }}</p>\n          <p><strong>昵称:</strong> {{ loginResult.data.nickname }}</p>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"test-section\">\n      <h3>3. 网络信息</h3>\n      <p><strong>前端地址:</strong> {{ frontendUrl }}</p>\n      <p><strong>后端地址:</strong> {{ backendUrl }}</p>\n      <p><strong>API基础URL:</strong> {{ apiBaseUrl }}</p>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Test',\n  data() {\n    return {\n      loading: false,\n      loginLoading: false,\n      connectionResult: null,\n      loginResult: null,\n      testUsername: 'admin',\n      testPassword: '123456',\n      frontendUrl: window.location.origin,\n      backendUrl: 'http://localhost:8083',\n      apiBaseUrl: this.$http.defaults.baseURL\n    }\n  },\n  methods: {\n    async testBackendConnection() {\n      this.loading = true;\n      this.connectionResult = null;\n      \n      try {\n        const response = await this.$http.get('/test/hello');\n        this.connectionResult = {\n          success: true,\n          message: response.data.message || '连接成功',\n          data: response.data.data\n        };\n      } catch (error) {\n        this.connectionResult = {\n          success: false,\n          message: error.message || '连接失败',\n          data: error.response?.data\n        };\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    async testLogin() {\n      this.loginLoading = true;\n      this.loginResult = null;\n      \n      try {\n        const response = await this.$http.post('/test/login', {\n          username: this.testUsername,\n          password: this.testPassword\n        });\n        \n        this.loginResult = {\n          success: true,\n          message: response.data.message || '登录成功',\n          data: response.data.data\n        };\n      } catch (error) {\n        this.loginResult = {\n          success: false,\n          message: error.response?.data?.message || error.message || '登录失败',\n          data: error.response?.data\n        };\n      } finally {\n        this.loginLoading = false;\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.test-container {\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 20px;\n}\n\n.test-section {\n  margin-bottom: 30px;\n  padding: 20px;\n  border: 1px solid #ddd;\n  border-radius: 8px;\n}\n\n.login-form {\n  margin: 10px 0;\n}\n\n.login-form input {\n  margin-right: 10px;\n  padding: 8px;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n}\n\nbutton {\n  padding: 10px 20px;\n  background-color: #007bff;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n}\n\nbutton:disabled {\n  background-color: #ccc;\n  cursor: not-allowed;\n}\n\nbutton:hover:not(:disabled) {\n  background-color: #0056b3;\n}\n\n.result {\n  margin-top: 15px;\n  padding: 15px;\n  background-color: #f8f9fa;\n  border-radius: 4px;\n  border-left: 4px solid #007bff;\n}\n\nh2, h3 {\n  color: #333;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAgB;;EAGpBA,KAAK,EAAC;AAAc;;;;EAKMA,KAAK,EAAC;;;;;;EAOhCA,KAAK,EAAC;AAAc;;EAElBA,KAAK,EAAC;AAAY;;;;EAOCA,KAAK,EAAC;;;;;;EAW3BA,KAAK,EAAC;AAAc;;uBAnC3BC,mBAAA,CAyCM,OAzCNC,UAyCM,G,4BAxCJC,mBAAA,CAAgB,YAAZ,SAAO,qBAEXA,mBAAA,CAUM,OAVNC,UAUM,G,0BATJD,mBAAA,CAAkB,YAAd,WAAS,qBACbA,mBAAA,CAES;IAFAE,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,qBAAA,IAAAD,QAAA,CAAAC,qBAAA,IAAAF,IAAA,CAAqB;IAAGG,QAAQ,EAAEC,KAAA,CAAAC;sBAC7CD,KAAA,CAAAC,OAAO,4CAAAC,UAAA,GAEDF,KAAA,CAAAG,gBAAgB,I,cAA3Bb,mBAAA,CAIM,OAJNc,UAIM,GAHJZ,mBAAA,CAAwE,Y,0BAArEA,mBAAA,CAAoB,gBAAZ,KAAG,qB,iBAAS,GAAC,GAAAa,gBAAA,CAAGL,KAAA,CAAAG,gBAAgB,CAACG,OAAO,+B,GACnDd,mBAAA,CAA0D,Y,0BAAvDA,mBAAA,CAAoB,gBAAZ,KAAG,qB,iBAAS,GAAC,GAAAa,gBAAA,CAAGL,KAAA,CAAAG,gBAAgB,CAACI,OAAO,iB,GAC1CP,KAAA,CAAAG,gBAAgB,CAACK,IAAI,I,cAA9BlB,mBAAA,CAAoF,KAAAmB,UAAA,G,0BAApDjB,mBAAA,CAAoB,gBAAZ,KAAG,qB,iBAAS,GAAC,GAAAa,gBAAA,CAAGL,KAAA,CAAAG,gBAAgB,CAACK,IAAI,iB,iFAIjFhB,mBAAA,CAkBM,OAlBNkB,UAkBM,G,4BAjBJlB,mBAAA,CAAkB,YAAd,WAAS,qBACbA,mBAAA,CAMM,OANNmB,UAMM,G,gBALJnB,mBAAA,CAA0D;+DAA1CQ,KAAA,CAAAY,YAAY,GAAAC,MAAA;IAAEC,WAAW,EAAC;iDAA1Bd,KAAA,CAAAY,YAAY,E,mBAC5BpB,mBAAA,CAA0E;+DAA1DQ,KAAA,CAAAe,YAAY,GAAAF,MAAA;IAAEG,IAAI,EAAC,UAAU;IAACF,WAAW,EAAC;iDAA1Cd,KAAA,CAAAe,YAAY,E,GAC5BvB,mBAAA,CAES;IAFAE,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAoB,SAAA,IAAApB,QAAA,CAAAoB,SAAA,IAAArB,IAAA,CAAS;IAAGG,QAAQ,EAAEC,KAAA,CAAAkB;sBACjClB,KAAA,CAAAkB,YAAY,4CAAAC,UAAA,E,GAGRnB,KAAA,CAAAoB,WAAW,I,cAAtB9B,mBAAA,CAQM,OARN+B,UAQM,GAPJ7B,mBAAA,CAAmE,Y,0BAAhEA,mBAAA,CAAoB,gBAAZ,KAAG,qB,iBAAS,GAAC,GAAAa,gBAAA,CAAGL,KAAA,CAAAoB,WAAW,CAACd,OAAO,+B,GAC9Cd,mBAAA,CAAqD,Y,0BAAlDA,mBAAA,CAAoB,gBAAZ,KAAG,qB,iBAAS,GAAC,GAAAa,gBAAA,CAAGL,KAAA,CAAAoB,WAAW,CAACb,OAAO,iB,GACnCP,KAAA,CAAAoB,WAAW,CAACZ,IAAI,I,cAA3BlB,mBAAA,CAIM,OAAAgC,WAAA,GAHJ9B,mBAAA,CAA2D,Y,4BAAxDA,mBAAA,CAAuB,gBAAf,QAAM,qB,iBAAS,GAAC,GAAAa,gBAAA,CAAGL,KAAA,CAAAoB,WAAW,CAACZ,IAAI,CAACe,KAAK,iB,GACpD/B,mBAAA,CAA4D,Y,4BAAzDA,mBAAA,CAAqB,gBAAb,MAAI,qB,iBAAS,GAAC,GAAAa,gBAAA,CAAGL,KAAA,CAAAoB,WAAW,CAACZ,IAAI,CAACgB,QAAQ,iB,GACrDhC,mBAAA,CAA2D,Y,4BAAxDA,mBAAA,CAAoB,gBAAZ,KAAG,qB,iBAAS,GAAC,GAAAa,gBAAA,CAAGL,KAAA,CAAAoB,WAAW,CAACZ,IAAI,CAACiB,QAAQ,iB,mFAK1DjC,mBAAA,CAKM,OALNkC,WAKM,G,4BAJJlC,mBAAA,CAAgB,YAAZ,SAAO,qBACXA,mBAAA,CAA+C,Y,4BAA5CA,mBAAA,CAAsB,gBAAd,OAAK,qB,iBAAS,GAAC,GAAAa,gBAAA,CAAGL,KAAA,CAAA2B,WAAW,iB,GACxCnC,mBAAA,CAA8C,Y,4BAA3CA,mBAAA,CAAsB,gBAAd,OAAK,qB,iBAAS,GAAC,GAAAa,gBAAA,CAAGL,KAAA,CAAA4B,UAAU,iB,GACvCpC,mBAAA,CAAkD,Y,4BAA/CA,mBAAA,CAA0B,gBAAlB,WAAS,qB,iBAAS,GAAC,GAAAa,gBAAA,CAAGL,KAAA,CAAA6B,UAAU,iB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}