package com.xbj.test;

import java.util.ArrayList;
import java.util.Iterator;

public class TestIterator {
    public static void main(String[] args) {
        ArrayList<String> list = new ArrayList<>();
        list.add("1");
        list.add("@");
        list.add("2");
        list.add("#");
        list.add("$");
        Iterator<String> iterator = list.iterator();
        while (iterator.hasNext()){
            String next = iterator.next();
            System.out.println(next);
            iterator.remove();
        }
        int i = 0;
        Iterator<String> iterator1 = list.iterator();
        while (iterator1.hasNext()){
            String next = iterator1.next();
            System.out.println(next);
            i++;
            iterator1.remove();
        }
        System.out.println(i);
    }
}
