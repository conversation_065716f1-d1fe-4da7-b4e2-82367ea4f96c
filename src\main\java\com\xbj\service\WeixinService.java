package com.xbj.service;

import com.alibaba.fastjson.JSONObject;
import com.xbj.config.WeixinConfig;
import com.xbj.util.HttpsUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 微信小程序服务类
 */
@Service
public class WeixinService {
    
    @Autowired
    private WeixinConfig weixinConfig;
    
    /**
     * 获取微信小程序的session_key和openid
     *
     * @param code 微信前端login()方法返回的code
     * @return jsonObject
     */
    public JSONObject getSessionKeyAndOpenId(String code) {
        // 封装需要的参数信息
        Map<String, String> requestUrlParam = new HashMap<>(16);
        // 开发者设置中的appId
        requestUrlParam.put("appid", weixinConfig.getAppId());
        // 开发者设置中的appSecret
        requestUrlParam.put("secret", weixinConfig.getAppSecret());
        // 小程序调用wx.login返回的code
        requestUrlParam.put("js_code", code);
        // 默认参数
        requestUrlParam.put("grant_type", "authorization_code");
        
        return JSONObject.parseObject(HttpsUtils.sendPost(weixinConfig.getUrl(), requestUrlParam));
    }
}
