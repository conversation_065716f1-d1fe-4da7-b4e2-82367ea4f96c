package com.xbj.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 */
public class MD5Utils {
    private static final Logger LOGGER = LoggerFactory.getLogger(MD5Utils.class);

    public static String MD5(String string, String enCode) {
        byte[] hash = null;
        try {
            hash = MessageDigest.getInstance("MD5").digest(string.getBytes(enCode));
        } catch (NoSuchAlgorithmException | UnsupportedEncodingException e) {
            LOGGER.error("MD5 Encode Err:%s", e);
        }
        assert hash != null;
        StringBuilder hex = new StringBuilder(hash.length * 2);
        for (byte b : hash) {
            if ((b & 0xFF) < 0x10) {
                hex.append("0");
            }
            hex.append(Integer.toHexString(b & 0xFF));
        }
        return hex.toString();
    }

    /*public static void main(String[] args) {
        String md5 = SecureUtil.md5("hello world");
        String world = MD5Utils.MD5("hello world", "utf-8");
        System.out.println(md5);
        System.out.println(world);
    }*/
}
