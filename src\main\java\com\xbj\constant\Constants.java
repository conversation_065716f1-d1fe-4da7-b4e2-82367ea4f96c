package com.xbj.constant;

/**
 * <AUTHOR>
 * @create 2020-09-17-16:13
 */
public class Constants {
    /**
     * redis中的 品牌分组
     */
    public static final String BRAND_KEY = "brand:";

    /**
     * redis中的 user分组
     */
    public static final String USER_KEY = "user:";

    /**
     * redis中的 验证码分组
     */
    public static final String YZM_KEY = "code:";

    /**
     * 一个ip每天最多可以发送的验证码的次数
     */
    public final static Integer MAX_REQUEST_IP_TIMES_ONE_DAY = 20;

    /**
     * 可再次发送验证码的间隔
     */
    public final static long TIME_INTERVAL = 60000;

    /**
     * 一个手机号码每天最多发送验证码次数
     */
    public final static Integer MAX_REQUEST_PHONE_TIMES_ONE_DAY = 5;

    /**
     * 验证码存在时长
     */
    public final static long CODE_FAIL_TIME = 300000;

}
