package com.xbj.common;

/**
 * 统一结果状态码枚举
 */
public enum ResultCode {

    // 成功状态码
    SUCCESS(200, "操作成功"),

    // 客户端错误状态码 4xx
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未授权访问"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    CONFLICT(409, "资源冲突"),
    VALIDATION_ERROR(422, "参数验证失败"),
    TOO_MANY_REQUESTS(429, "请求过于频繁"),

    // 服务端错误状态码 5xx
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    BAD_GATEWAY(502, "网关错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),
    GATEWAY_TIMEOUT(504, "网关超时"),

    // 业务错误状态码 1xxx
    USER_NOT_FOUND(1001, "用户不存在"),
    USER_ALREADY_EXISTS(1002, "用户已存在"),
    INVALID_CREDENTIALS(1003, "用户名或密码错误"),
    ACCOUNT_LOCKED(1004, "账户已锁定"),
    ACCOUNT_DISABLED(1005, "账户已禁用"),
    TOKEN_EXPIRED(1006, "令牌已过期"),
    TOKEN_INVALID(1007, "令牌无效"),
    INSUFFICIENT_PERMISSIONS(1008, "权限不足"),

    // 微信相关错误 2xxx
    WECHAT_CODE_INVALID(2001, "微信授权码无效"),
    WECHAT_SESSION_EXPIRED(2002, "微信会话已过期"),
    WECHAT_DECRYPT_FAILED(2003, "微信数据解密失败"),
    WECHAT_API_ERROR(2004, "微信接口调用失败"),

    // 手表相关错误 3xxx
    WATCH_NOT_FOUND(3001, "手表信息不存在"),
    WATCH_CODE_INVALID(3002, "手表编码无效"),
    WATCH_BRAND_NOT_FOUND(3003, "手表品牌不存在"),

    // 验证码相关错误 4xxx
    SMS_SEND_FAILED(4001, "短信发送失败"),
    SMS_CODE_EXPIRED(4002, "验证码已过期"),
    SMS_CODE_INVALID(4003, "验证码错误"),
    SMS_SEND_TOO_FREQUENT(4004, "短信发送过于频繁"),

    // 缓存相关错误 5xxx
    CACHE_ERROR(5001, "缓存操作失败"),
    REDIS_CONNECTION_ERROR(5002, "Redis连接失败"),

    // 数据库相关错误 6xxx
    DATABASE_ERROR(6001, "数据库操作失败"),
    DATA_NOT_FOUND(6002, "数据不存在"),
    DATA_ALREADY_EXISTS(6003, "数据已存在"),
    DATA_INTEGRITY_VIOLATION(6004, "数据完整性约束违反");

    private final Integer code;
    private final String message;

    ResultCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    /**
     * 根据状态码获取枚举
     */
    public static ResultCode getByCode(Integer code) {
        for (ResultCode resultCode : values()) {
            if (resultCode.getCode().equals(code)) {
                return resultCode;
            }
        }
        return INTERNAL_SERVER_ERROR;
    }

    @Override
    public String toString() {
        return "ResultCode{" +
                "code=" + code +
                ", message='" + message + '\'' +
                '}';
    }
}
