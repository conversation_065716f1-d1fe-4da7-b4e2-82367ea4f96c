package com.xbj.test;

import cn.hutool.core.util.ArrayUtil;
import com.hankcs.hanlp.HanLP;
import com.hankcs.hanlp.seg.common.Term;
import info.debatty.java.lsh.MinHash;

import java.util.*;

public class LshTest1 {
    public static void main(String[] args) {
        String a = "最近在工作中需要去优化离职同事留下的用户协同过滤算法，本来想协同过滤嘛，不就是一顿算相似度，然后取top-k相似的用户去做推荐就完了。结果看代码的过程中，对计算相似度的部分却是一头雾水，主要是对其中使用的LSH算法不甚了解。经过了一番调研之后，才算是理解了这个算法的精妙，也感到自己之前的粗糙想法实在是naive。\n" +
                "\n" +
                "传统的协同过滤算法，不管是基于用户还是基于物品的，其中最关键的一个问题便是：计算两个用户（或物品）之间的相似度。相似度的计算有多种方式：欧氏距离、余弦相似度或者Jaccard相似度，不管以何种计算方式，在数据维度较小时，都可以用naive的方式直接遍历每一个pair去计算。但当数据维度增大到一定程度时，计算复杂度就开始飙升了，主要体现在两个方面（以计算用户相似度为例）：\n" +
                "\n" +
                "两个用户之间相似度的计算随着物品维度的增加而增加\n" +
                "计算每一个用户和其他所有用户之间的相似度的复杂度随着用户规模的增长，呈平方增长\n" +
                "对于工业界的数据，用户和物品的维度都在千万甚至更高的情况下，直接计算两两之间的相似度，即便使用大规模计算集群有可能实现，所需要的计算成本也是极高的。这时便需要使用近似算法，牺牲一些精度来大大提高计算效率。Min Hashing和Locality Sensitive Hashing（LSH，局部敏感哈希）便是用来分别提高这两个方面的计算效率的" ;
        String b = "最近在工作中需要去优化离职同事留下的用户协同过滤算法，本来想协同过滤嘛，不就是一顿算相似度，然后取top-k相似的用户去做推荐就完了。结果看代码的过程中，对计算相似度的部分却是一头雾水，主要是对其中使用的LSH算法不甚了解。经过了一番调研之后，才算是理解了这个算法的精妙，也感到自己之前的粗糙想法实在是naive。\n" +
                "\n" +
                "传统的协同过滤算法，这是瞎写的一部分，其中最关键的一个问题便是：计算两个用户（或物品）之间的相似度。相似度的计算有多种方式：欧氏距离、余弦相似度或者Jaccard相似度，不管以何种计算方式，在数据维度较小时，都可以用naive的方式直接遍历每一个pair去计算。但当数据维度增大到一定程度时，计算复杂度就开始飙升了，主要体现在两个方面（以计算用户相似度为例）：\n" +
                "\n" +
                "两个用户之间相似度的计算随着物品维度的增加而增加\n" +
                "计算每一个用户和其他所有用户之间的相似度的复杂度随着用户规模的增长，呈平方增长\n" +
                "对于工业界的数据，用户和物品的维度都在千万甚至更高的情况下，直接计算两两之间的相似度，即便使用大规模计算集群有可能实现，所需要的计算成本也是极高的。这时便需要使用近似算法，牺牲一些精度来大大提高计算效率。Min Hashing和Locality Sensitive Hashing（LSH，局部敏感哈希）便是用来分别提高这两个方面的计算效率的" ;
        List<Term> segment = HanLP.segment(a);
        List<Term> segmentb = HanLP.segment(b);
        Set<String> set = new HashSet<>();
        Set<String> setb = new HashSet<>();
        for (Term term : segment) {
            set.add(term.word);
        }
        for (Term term : segmentb) {
            setb.add(term.word);
        }
        Map<String, Boolean> map = new HashMap<>();
        boolean [] arra = new boolean[set.size()];
        int i =0;
        for (String s : set) {
            map.put(s,true);
            arra[i] = true;
            i++;
        }
        int j =0;
        boolean [] arrb = new boolean[setb.size()];
        for (String s : setb) {
            if (map.containsKey(s)){
                arrb[j] = true;
            }else {
                arrb[j] = false;
            }
            j++;
        }
        boolean [] min = arrb.length>arra.length?arra:arrb;
        boolean [] max = arrb.length>arra.length?arrb:arra;
        boolean [] extra = new boolean [max.length-min.length];
        Arrays.fill(extra, true);
        boolean[] fills = ArrayUtil.addAll(min, extra);

        /*int sizeOfVectors = fills.length;
        int numberOfBuckets = 10;
        int stages = 4;
        LSHMinHash lshMinHash = new LSHMinHash(stages,numberOfBuckets,sizeOfVectors);

        //计算出两个向量的hash值
        int[] hash = lshMinHash.hash(fills);
        int[] hash1 = lshMinHash.hash(max);

        MinHash.jaccardIndex(hash, hash1)*/


        MinHash minhash = new MinHash(0.1, max.length);

        // Sets can be defined as an vector of booleans:
        // [1 0 0 1 0]
        //boolean[] vector1 = {true, false, false, true, false};
        int[] sig1 = minhash.signature(fills);

        // Or as a set of integers:
        // set2 = [1 0 1 1 0]
        /*TreeSet<Integer> set2 = new TreeSet<Integer>();
        set2.add(0);
        set2.add(2);
        set2.add(3);*/
        int[] sig2 = minhash.signature(max);

        System.out.println("Signature similarity: " + MinHash.jaccardIndex(max, fills));
        //System.out.println("Signature similarity: " + minhash.similarity(sig1, sig2));
        /* 考虑将一段文本，拆词后，组成一个set集合，set中每一个字符串都对应 boolean，并组成一个hashMap 集合
        * 第二段文字拆词后，利用hashmap 判断 该集合中是否存在该项，存在为true，不存在为false，此时便形成了第二个 Boolean 数组
        *参考链接 https://www.baeldung.com/locality-sensitive-hashing
        *
        * */

    }
}
