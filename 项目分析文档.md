# 寻表记(FindWatch) 项目分析文档

## 项目概述

**寻表记**是一个基于Java Spring MVC架构的手表信息查询系统，主要为用户提供手表品牌、型号、价格等信息的查询服务。该项目采用微信小程序作为前端，Java后台提供API服务。

### 基本信息
- **项目名称**: findWatch (寻表记JAVA后台)
- **技术栈**: Spring MVC + MyBatis + MySQL + Redis
- **开发语言**: Java 8
- **构建工具**: Maven
- **数据库**: MySQL
- **缓存**: Redis
- **部署方式**: WAR包部署

## 技术架构

### 1. 整体架构
```
前端(微信小程序) 
    ↓ HTTP/HTTPS
后端API服务(Spring MVC)
    ↓
业务逻辑层(Service)
    ↓
数据访问层(MyBatis)
    ↓
数据库(MySQL) + 缓存(Redis)
```

### 2. 核心技术栈

#### 后端框架
- **Spring Framework 4.3.25**: 核心容器和依赖注入
- **Spring MVC**: Web层框架，处理HTTP请求
- **MyBatis 3.3.0**: ORM框架，数据库操作
- **Spring Data Redis 1.8.8**: Redis缓存操作

#### 数据库相关
- **MySQL 8.0.30**: 主数据库
- **HikariCP 4.0.3**: 数据库连接池
- **Redis**: 缓存数据库，使用Jedis 2.9.0客户端

#### 工具库
- **FastJSON 1.2.75**: JSON序列化/反序列化
- **Jackson 2.7.4**: JSON处理
- **Hutool 5.5.7**: Java工具类库
- **Apache POI 4.1.2**: Excel文件处理
- **EasyExcel 3.3.2**: Excel读写

#### 第三方服务
- **阿里云短信服务**: 验证码发送
- **微信小程序**: 用户认证和数据解密
- **JWT**: Token生成和验证

## 项目结构

### 1. 目录结构
```
findwatch/
├── src/main/
│   ├── java/com/xbj/
│   │   ├── config/          # 配置类
│   │   ├── constant/        # 常量定义
│   │   ├── controller/      # 控制器层
│   │   ├── dao/            # 数据访问层
│   │   ├── entity/         # 实体类
│   │   ├── service/        # 业务逻辑层
│   │   ├── test/           # 测试类
│   │   └── util/           # 工具类
│   ├── resources/
│   │   ├── mapper/         # MyBatis映射文件
│   │   ├── spring/         # Spring配置文件
│   │   ├── jdbc.properties # 数据库配置
│   │   ├── redis.properties # Redis配置
│   │   └── weixin.properties # 微信配置
│   └── webapp/
│       ├── WEB-INF/        # Web配置
│       └── static/         # 静态资源
├── sql/                    # 数据库脚本
└── pom.xml                # Maven配置
```

### 2. 核心包结构

#### Controller层
- `LoginController`: 用户登录、注册、验证码发送
- `WatchController`: 手表信息查询、品牌数据获取

#### Service层
- `UserService`: 用户相关业务逻辑
- `WatchService`: 手表信息业务逻辑
- `WatchCodeService`: 手表编码数据业务逻辑

#### Entity层
- `User`: 用户实体
- `Watch`: 手表信息实体
- `WatchCode`: 手表编码实体

## 数据库设计

### 1. 核心表结构

#### 用户表 (user)
```sql
CREATE TABLE user (
    id VARCHAR(40) PRIMARY KEY COMMENT '主键或微信unid',
    openid VARCHAR(40) NOT NULL,
    unionid VARCHAR(40),
    nick_name VARCHAR(100) DEFAULT '' COMMENT '昵称',
    province VARCHAR(40) DEFAULT '' COMMENT '省份',
    country VARCHAR(40) DEFAULT '' COMMENT '国家',
    phone VARCHAR(20) DEFAULT '' COMMENT '手机号码',
    email VARCHAR(40) DEFAULT '' COMMENT '邮箱',
    adress VARCHAR(200) DEFAULT '' COMMENT '住址',
    gender VARCHAR(4) DEFAULT '' COMMENT '性别',
    wx_number VARCHAR(50) DEFAULT '' COMMENT '微信号',
    data1 VARCHAR(255) DEFAULT '' COMMENT '备用1',
    data2 VARCHAR(255) DEFAULT '' COMMENT '备用2'
) COMMENT '用户表';
```

#### 手表信息表 (watch)
```sql
CREATE TABLE watch (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '编号',
    brand VARCHAR(80) DEFAULT '' COMMENT '手表品牌',
    series VARCHAR(200) DEFAULT '' COMMENT '手表系列',
    model VARCHAR(80) DEFAULT '' COMMENT '手表型号',
    size VARCHAR(20) DEFAULT '' COMMENT '手表尺寸',
    price_inland VARCHAR(20) DEFAULT '' COMMENT '大陆售价',
    core_type VARCHAR(80) DEFAULT '' COMMENT '机芯类型',
    core_model VARCHAR(80) DEFAULT '' COMMENT '机芯型号',
    bk_type VARCHAR(20) DEFAULT '' COMMENT '表壳材质',
    bd_type VARCHAR(100) DEFAULT '' COMMENT '表带材质',
    bk_hor_size VARCHAR(100) DEFAULT '' COMMENT '表壳直径',
    bp_color VARCHAR(100) DEFAULT '' COMMENT '表盘颜色',
    ks VARCHAR(10) DEFAULT '' COMMENT '款式',
    fb_date VARCHAR(40) DEFAULT '' COMMENT '发布时间',
    fz_fun VARCHAR(80) DEFAULT '' COMMENT '复杂功能',
    watch_url VARCHAR(80) DEFAULT '' COMMENT '图片路径',
    bkou_type VARCHAR(100) DEFAULT '' COMMENT '表扣类型',
    use_hours VARCHAR(40) DEFAULT '' COMMENT '动力储备',
    water_depth VARCHAR(10) DEFAULT '' COMMENT '防水深度',
    price_in_hk VARCHAR(20) DEFAULT '' COMMENT '中国香港售价',
    price_in_italy VARCHAR(20) DEFAULT '' COMMENT '意大利售价',
    price_in_france VARCHAR(20) DEFAULT '' COMMENT '法国售价',
    price_in_spain VARCHAR(20) DEFAULT '' COMMENT '西班牙售价',
    price_in_switzerland VARCHAR(20) DEFAULT '' COMMENT '瑞士售价',
    price_in_germany VARCHAR(20) DEFAULT '' COMMENT '德国售价',
    price_in_holland VARCHAR(20) DEFAULT '' COMMENT '荷兰售价'
) COMMENT '手表';
```

#### 手表编码表 (watch_code)
```sql
CREATE TABLE watch_code (
    id VARCHAR(42) PRIMARY KEY COMMENT '主键',
    code CHAR(2) NOT NULL COMMENT '值',
    type VARCHAR(100) NOT NULL COMMENT '类型',
    name VARCHAR(100) NOT NULL COMMENT '名称',
    type_name VARCHAR(100) DEFAULT '' COMMENT 'type对应名称',
    pic_url VARCHAR(255) DEFAULT '' COMMENT '图片路径',
    start_with VARCHAR(2) DEFAULT ''
);
```

### 2. 数据特点
- **手表数据丰富**: 包含品牌、系列、型号、价格、材质等详细信息
- **多地区价格**: 支持大陆、香港、欧洲多个国家的价格信息
- **编码系统**: 通过watch_code表管理品牌、材质、颜色等分类数据
- **用户系统**: 基于微信OpenID的用户体系

## 核心功能模块

### 1. 用户认证模块
- **微信登录**: 通过微信小程序获取用户OpenID
- **用户注册**: 绑定手机号码完成注册
- **验证码服务**: 阿里云短信验证码发送
- **JWT Token**: 用户会话管理

### 2. 手表查询模块
- **品牌查询**: 获取所有手表品牌信息
- **系列查询**: 根据品牌获取手表系列
- **条件搜索**: 多条件组合搜索手表
- **详情查询**: 获取手表详细信息

### 3. 缓存优化模块
- **Redis缓存**: 品牌数据、用户信息缓存
- **缓存策略**: 不同数据设置不同过期时间
- **缓存更新**: 自动更新和手动刷新机制

### 4. 数据管理模块
- **编码管理**: 手表属性编码维护
- **数据导入**: Excel数据批量导入
- **图片管理**: 手表图片存储和访问

## 关键技术实现

### 1. 缓存策略
```java
// 品牌数据缓存2天
redisUtil.setExpireObject(Constants.BRAND_KEY + brand, res, 2, TimeUnit.DAYS);

// 空数据缓存1小时
redisUtil.setExpireObject(Constants.BRAND_KEY + brand, res, 1, TimeUnit.HOURS);
```

### 2. 验证码限制
```java
// IP限制：每天最多20次
public final static Integer MAX_REQUEST_IP_TIMES_ONE_DAY = 20;

// 手机号限制：每天最多5次
public final static Integer MAX_REQUEST_PHONE_TIMES_ONE_DAY = 5;

// 发送间隔：60秒
public final static long TIME_INTERVAL = 60000;
```

### 3. JWT Token验证
```java
Map<String,Object> res = JWTUtils.verifyTokenGetResult(token, "lookforwatch");
boolean isSuccess = Boolean.parseBoolean(res.get("isSuccess").toString());
```

## 部署配置

### 1. 数据库配置
```properties
jdbc.driver=com.mysql.jdbc.Driver
jdbc.url=jdbc:mysql://************:3306/watch?useUnicode=true&characterEncoding=utf8
jdbc.username=YY
jdbc.password=FLZX3000C_ysyhl9t
```

### 2. Redis配置
- 连接池配置
- 序列化配置
- 过期策略配置

### 3. 微信小程序配置
- AppID和AppSecret配置
- 数据解密配置
- API接口配置

## 项目特色

### 1. 技术特色
- **微信生态集成**: 深度集成微信小程序
- **多级缓存**: Redis + 应用级缓存
- **数据丰富**: 全球手表品牌数据库
- **安全机制**: JWT + 验证码双重验证

### 2. 业务特色
- **专业性强**: 专注手表领域的垂直应用
- **数据全面**: 包含价格、参数、图片等完整信息
- **用户体验**: 微信小程序便捷访问
- **国际化**: 支持多国价格对比

## 总结

寻表记项目是一个技术架构清晰、功能完整的手表信息查询系统。项目采用了成熟的Java Web技术栈，具有良好的可扩展性和维护性。通过微信小程序提供便捷的用户体验，通过Redis缓存提升系统性能，是一个典型的垂直领域应用案例。
